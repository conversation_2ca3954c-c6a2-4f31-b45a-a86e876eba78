<view class="xilu data-v-69d1319c"><view class="page-head bg-white data-v-69d1319c"><view class="g_tab data-v-69d1319c"><view data-event-opts="{{[['tap',[['tabClick',[-1]]]]]}}" class="{{['item','data-v-69d1319c',(tabIdx==-1)?'active':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['tabClick',[0]]]]]}}" class="{{['item','data-v-69d1319c',(tabIdx==0)?'active':'']}}" bindtap="__e">待付款</view><view data-event-opts="{{[['tap',[['tabClick',[1]]]]]}}" class="{{['item','data-v-69d1319c',(tabIdx==1)?'active':'']}}" bindtap="__e">待使用</view><view data-event-opts="{{[['tap',[['tabClick',[2]]]]]}}" class="{{['item','data-v-69d1319c',(tabIdx==2)?'active':'']}}" bindtap="__e">已使用</view><view data-event-opts="{{[['tap',[['tabClick',[3]]]]]}}" class="{{['item','data-v-69d1319c',(tabIdx==3)?'active':'']}}" bindtap="__e">退款/取消</view></view></view><view class="container data-v-69d1319c"><block wx:for="{{sceneryList}}" wx:for-item="order" wx:for-index="index" wx:key="index"><view class="g_order1 data-v-69d1319c"><view class="flex-box mb25 data-v-69d1319c"><view class="fs26 col-89 data-v-69d1319c">订单号</view><view class="flex-1 fs26 col-5 data-v-69d1319c">{{order.order_no}}</view><view class="{{['fs30','data-v-69d1319c',order.state==0?'col-price':order.state==1?'col-10':order.state==2?'col-5':'col-5']}}">{{order.state_text}}</view></view><view data-event-opts="{{[['tap',[['orderDetail',[index]]]]]}}" class="flex-box data-v-69d1319c" bindtap="__e"><image class="img data-v-69d1319c" src="{{order.order_project.thumb_image}}" mode="aspectFill"></image><view class="flex-1 data-v-69d1319c"><view class="m-ellipsis fs36 col-10 mb10 data-v-69d1319c">{{order.order_project.scenery_name}}</view><view class="fs30 col-3 m-ellipsis mb10 data-v-69d1319c">{{order.order_project.project_name}}</view><view class="flex-box col-3 mb10 data-v-69d1319c"><text class="fs24 data-v-69d1319c">¥</text><text class="fs30 flex-1 data-v-69d1319c">{{order.order_project.project_price}}</text><view class="fs30 col-89 data-v-69d1319c">{{"数量 "+order.total_count}}</view></view><view class="data-v-69d1319c"><text class="fs30 col-89 data-v-69d1319c">实付款</text><text class="fs30 col-price data-v-69d1319c">¥</text><text class="fs40 col-price data-v-69d1319c">{{order.pay_price}}</text></view></view></view><block wx:if="{{order.state==0}}"><view class="pt30 flex-box flex-end data-v-69d1319c"><view class="flex-1 col-price fs24 data-v-69d1319c"><view class="data-v-69d1319c">{{"请在"+order.expiretime_text+"前支付"}}</view><view class="data-v-69d1319c">过期自动取消</view></view><view data-event-opts="{{[['tap',[['orderCancel',[index]]]]]}}" class="foot_btn1 data-v-69d1319c" bindtap="__e">取消订单</view><view data-event-opts="{{[['tap',[['payment',['$0'],[[['sceneryList','',index]]]]]]]}}" class="foot_btn2 data-v-69d1319c" bindtap="__e">立即支付</view></view></block><block wx:else><block wx:if="{{order.state==1}}"><view class="pt30 flex-box flex-end data-v-69d1319c"><button class="foot_btn1 data-v-69d1319c" open-type="contact">联系客服</button><view data-event-opts="{{[['tap',[['orderDetail',[index]]]]]}}" class="foot_btn2 data-v-69d1319c" bindtap="__e">查看券码</view></view></block><block wx:else><block wx:if="{{order.state==2&&order.comment_status==0}}"><view data-event-opts="{{[['tap',[['bindComment',[index]]]]]}}" class="pt30 flex-box flex-end data-v-69d1319c" bindtap="__e"><view class="foot_btn2 data-v-69d1319c">立即评价</view></view></block></block></block></view></block><view class="g-btn3-wrap data-v-69d1319c"><view data-event-opts="{{[['tap',[['fetch',['$event']]]]]}}" class="g-btn3 data-v-69d1319c" bindtap="__e">{{sceneryListMore.text}}</view></view></view></view>