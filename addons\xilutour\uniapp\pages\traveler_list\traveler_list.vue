<template>
	<view class="xilu">
		<view class="page-foot flex-box" @click="addTraveler()">
			<view class="g-btn1 flex-1">新建出行人</view>
		</view>
		<view class="container">
			<view class="xilu_traveler"  v-for="(item,index) in travelerList" :key="index">
				<view class="">
					<view class="flex-box mb30">
						<view class="col-10 mr20">{{item.username}}</view>
						<image v-if="item.gender == 1" class="icon_gender" src="/static/icon/icon_gender1.png" mode="aspectFit"></image>
						<image v-else class="icon_gender" src="/static/icon/icon_gender2.png" mode="aspectFit"></image>
					</view>
					<view class="flex-box mb20">
						<view class="col-9 mr15">身份证号</view>
						<view class="col-3 flex-1">{{item.idcard}}</view>
					</view>
					<view class="flex-box">
						<view class="col-9 mr15">手机号码</view>
						<view class="col-3 flex-1">{{item.mobile}}</view>
					</view>
				</view>
				<view class="flex-box flex-end fs30 col-89">
					<view class="flex-box mr30" @click="bindEdit(index)">
						<image src="/static/icon/icon_edit1.png" mode="aspectFit" class="icon"></image>
						<view>编辑</view>
					</view>
					<view class="flex-box" @click="bindDel(index)">
						<image src="/static/icon/icon_del1.png" mode="aspectFit" class="icon"></image>
						<view>删除</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				travelerList:[]
			};
		},
		onLoad() {
			this.fetch();
			
		},
		methods:{
			fetch() {
				this.$core.get({url: 'xilutour.traveler/lists',data: this.tour,success: ret => {
						this.travelerList = ret.data;
					},
					fail: err => {
						
					}
				});
			},
			//添加出行
			addTraveler(){
				uni.navigateTo({
					url: '/pages/new_traveler/new_traveler',
					events:{
						setSuccess: data=>{
							this.fetch();
						}
					}
				})
			},
			bindEdit(index){
				let item = this.travelerList[index];
				uni.navigateTo({
					url: '/pages/new_traveler/new_traveler',
					events:{
						setSuccess: data=>{
							this.fetch();
						}
					},
					success(res) {
						res.eventChannel.emit("editTransfor",item)
					}
				})
			},
			bindDel(index){
				let page = this;
				let travelerList = page.travelerList
				let item = travelerList[index];
				uni.showModal({
					title: '提示',
					content: '确认删除',
					success(res){
						if(res.confirm){
							page.$core.post({url: 'xilutour.traveler/del',data: {traveler_id: item.id},success: ret => {
								uni.showToast({title:'删除成功', icon:'none'})
									travelerList.splice(index,1);
									page.travelerList = travelerList;
								},
								fail: err => {
									
								}
							});
						}
					}
				})
			},
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		.page-foot {
			padding: 20rpx 40rpx;
			background-color: #FFF;

			.btn1 {
				width: 290rpx;
				height: 90rpx;
				border-radius: 30rpx;
				border: 2rpx solid var(--normal);
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: var(--normal);
				line-height: 88rpx;
				text-align: center;
			}

			.btn2 {
				width: 290rpx;
				height: 90rpx;
				border-radius: 30rpx;
				background: var(--normal);
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 90rpx;
				text-align: center;
			}
		}

		.container {
			padding: 30rpx 40rpx 160rpx !important;
		}

		&_traveler {
			margin: 0 0 30rpx;
			padding: 30rpx;
			width: 670rpx;
			background: #F7F9FB;
			border-radius: 20rpx;
			font-size: 30rpx;
			line-height: 32rpx;

			.icon_gender {
				display: block;
				width: 30rpx;
				height: 30rpx;
			}

			.icon_check {
				display: block;
				width: 40rpx;
				height: 40rpx;
			}

			.icon {
				margin-right: 8rpx;
				display: block;
				width: 30rpx;
				height: 30rpx;
			}
		}
	}
</style>