<view><block xhs:for="{{sceneryList}}" xhs:for-item="item" xhs:for-index="index"><navigator class="item" url="{{'/pages/landscape_detail/landscape_detail?id='+item.id}}" hover-class="none"><image class="img" src="{{item.thumb_image_text}}" mode="aspectFill"></image><view class="mb20"><block xhs:if="{{item.level}}"><text class="level">{{item.level.name}}</text></block><text class="g_feng">{{item.points}}</text><text class="fs36 col-10">{{item.name}}</text></view><view class="desc m-ellipsis mb15">{{item.introduce}}</view><view class="flex-box"><block xhs:if="{{item.project}}"><view class="flex-1 col-price"><text class="fs30">¥</text><text class="fs40">{{item.project.salesprice}}</text><text class="fs30">起</text></view></block><view class="fs28 col-a">{{item.view_count+"人浏览"}}</view></view></navigator></block></view>