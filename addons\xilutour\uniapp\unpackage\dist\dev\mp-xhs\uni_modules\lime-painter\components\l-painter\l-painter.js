(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["uni_modules/lime-painter/components/l-painter/l-painter"],{

/***/ 419:
/*!***************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue ***!
  \***************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _l_painter_vue_vue_type_template_id_cae877da___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./l-painter.vue?vue&type=template&id=cae877da& */ 420);
/* harmony import */ var _l_painter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./l-painter.vue?vue&type=script&lang=js& */ 422);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _l_painter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _l_painter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _l_painter_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./l-painter.vue?vue&type=style&index=0&lang=css& */ 428);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 31);

var renderjs





/* normalize component */

var component = Object(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _l_painter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _l_painter_vue_vue_type_template_id_cae877da___WEBPACK_IMPORTED_MODULE_0__["render"],
  _l_painter_vue_vue_type_template_id_cae877da___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _l_painter_vue_vue_type_template_id_cae877da___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "uni_modules/lime-painter/components/l-painter/l-painter.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 420:
/*!**********************************************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue?vue&type=template&id=cae877da& ***!
  \**********************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_template_id_cae877da___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=template&id=cae877da& */ 421);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_template_id_cae877da___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_template_id_cae877da___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_template_id_cae877da___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_template_id_cae877da___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 421:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue?vue&type=template&id=cae877da& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 422:
/*!****************************************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=script&lang=js& */ 423);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 423:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 3);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 34));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 36));
var _relation = __webpack_require__(/*! ../common/relation */ 424);
var _props = _interopRequireDefault(__webpack_require__(/*! ./props */ 425));
var _utils = __webpack_require__(/*! ./utils */ 426);
var _painter = _interopRequireDefault(__webpack_require__(/*! ./painter */ 427));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var nvue = {};
var _default = {
  name: 'lime-painter',
  mixins: [_props.default, (0, _relation.parent)('painter'), nvue],
  data: function data() {
    return {
      use2dCanvas: false,
      canvasHeight: 150,
      canvasWidth: null,
      parentWidth: 0,
      isPC: false,
      inited: false,
      progress: 0,
      first: 0,
      done: false
    };
  },
  computed: {
    styles: function styles() {
      return "".concat(this.size).concat(this.customStyle || '');
    },
    canvasId: function canvasId() {
      return "l-painter".concat(this._uid || this._.uid);
    },
    size: function size() {
      if (this.boardWidth && this.boardHeight) {
        return "width:".concat(this.boardWidth, "px; height: ").concat(this.boardHeight, "px;");
      }
    },
    dpr: function dpr() {
      return this.pixelRatio || uni.getSystemInfoSync().pixelRatio;
    },
    boardWidth: function boardWidth() {
      var _ref = this.elements && this.elements.css || this.elements || this,
        _ref$width = _ref.width,
        width = _ref$width === void 0 ? 0 : _ref$width;
      return (0, _utils.toPx)(width) || Math.max((0, _utils.toPx)(width), (0, _utils.toPx)(this.canvasWidth));
    },
    boardHeight: function boardHeight() {
      var _ref2 = this.elements && this.elements.css || this.elements || this,
        _ref2$height = _ref2.height,
        height = _ref2$height === void 0 ? 0 : _ref2$height;
      return (0, _utils.toPx)(height) || Math.max((0, _utils.toPx)(height), (0, _utils.toPx)(this.canvasHeight));
    },
    hasBoard: function hasBoard() {
      return this.board && Object.keys(this.board).length;
    },
    elements: function elements() {
      return JSON.parse(JSON.stringify(this.hasBoard ? this.board : this.el));
    }
  },
  watch: {},
  created: function created() {
    var _uni$getSystemInfoSyn = uni.getSystemInfoSync(),
      SDKVersion = _uni$getSystemInfoSyn.SDKVersion,
      version = _uni$getSystemInfoSyn.version,
      platform = _uni$getSystemInfoSyn.platform;
  },
  mounted: function mounted() {
    var _this = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
      return _regenerator.default.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return (0, _utils.sleep)(30);
            case 2:
              _context.next = 4;
              return _this.getParentWeith();
            case 4:
              _this.$nextTick(function () {
                setTimeout(function () {
                  _this.$watch('elements', _this.watchRender, {
                    deep: true,
                    immediate: true
                  });
                }, 30);
              });
            case 5:
            case "end":
              return _context.stop();
          }
        }
      }, _callee);
    }))();
  },
  methods: {
    watchRender: function watchRender(val, old) {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                if (!(!val || !val.views || (!_this2.first ? !val.views.length : !_this2.first) || JSON.stringify(val) === '{}' || JSON.stringify(val) == JSON.stringify(old))) {
                  _context2.next = 2;
                  break;
                }
                return _context2.abrupt("return");
              case 2:
                _this2.first = 1;
                clearTimeout(_this2.rendertimer);
                _this2.rendertimer = setTimeout(function () {
                  _this2.render(val);
                }, _this2.beforeDelay);
              case 5:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }))();
    },
    setFilePath: function setFilePath(path, param) {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var filePath, _ref3, _ref3$pathType, pathType;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                filePath = path;
                _ref3 = param || _this3, _ref3$pathType = _ref3.pathType, pathType = _ref3$pathType === void 0 ? _this3.pathType : _ref3$pathType;
                if (!(pathType == 'base64' && !(0, _utils.isBase64)(path))) {
                  _context3.next = 8;
                  break;
                }
                _context3.next = 5;
                return (0, _utils.pathToBase64)(path);
              case 5:
                filePath = _context3.sent;
                _context3.next = 12;
                break;
              case 8:
                if (!(pathType == 'url' && (0, _utils.isBase64)(path))) {
                  _context3.next = 12;
                  break;
                }
                _context3.next = 11;
                return (0, _utils.base64ToPath)(path);
              case 11:
                filePath = _context3.sent;
              case 12:
                if (param && param.isEmit) {
                  _this3.$emit('success', filePath);
                }
                return _context3.abrupt("return", filePath);
              case 14:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    },
    getSize: function getSize(args) {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var _ref4, width, _ref5, height;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _ref4 = args.css || args, width = _ref4.width;
                _ref5 = args.css || args, height = _ref5.height;
                if (_this4.size) {
                  _context4.next = 12;
                  break;
                }
                if (!(width || height)) {
                  _context4.next = 10;
                  break;
                }
                _this4.canvasWidth = width || _this4.canvasWidth;
                _this4.canvasHeight = height || _this4.canvasHeight;
                _context4.next = 8;
                return (0, _utils.sleep)(30);
              case 8:
                _context4.next = 12;
                break;
              case 10:
                _context4.next = 12;
                return _this4.getParentWeith();
              case 12:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    canvasToTempFilePathSync: function canvasToTempFilePathSync(args) {
      var _this5 = this;
      this.stopWatch = this.$watch('done', function (v) {
        if (v) {
          _this5.canvasToTempFilePath(args);
          _this5.stopWatch && _this5.stopWatch();
        }
      }, {
        immediate: true
      });
    },
    getParentWeith: function getParentWeith() {
      var _this6 = this;
      return new Promise(function (resolve) {
        uni.createSelectorQuery().in(_this6).select(".lime-painter").boundingClientRect().exec(function (res) {
          _this6.parentWidth = Math.ceil(res[0].width);
          _this6.canvasWidth = _this6.parentWidth || 300;
          _this6.canvasHeight = res[0].height || _this6.canvasHeight || 150;
          resolve(res[0]);
        });
      });
    },
    render: function render() {
      var _arguments = arguments,
        _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var args, ctx, use2dCanvas, boardWidth, boardHeight, canvas, afterDelay, _ref6, _width, _ref7, _height, param, _yield$_this7$painter, width, height;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                args = _arguments.length > 0 && _arguments[0] !== undefined ? _arguments[0] : {};
                _this7.progress = 0;
                _this7.done = false;
                _context5.next = 5;
                return _this7.getSize(args);
              case 5:
                _context5.next = 7;
                return _this7.getContext();
              case 7:
                ctx = _context5.sent;
                use2dCanvas = _this7.use2dCanvas, boardWidth = _this7.boardWidth, boardHeight = _this7.boardHeight, canvas = _this7.canvas, afterDelay = _this7.afterDelay;
                if (!(use2dCanvas && !canvas)) {
                  _context5.next = 11;
                  break;
                }
                return _context5.abrupt("return", Promise.reject(new Error('render: fail canvas has not been created')));
              case 11:
                _this7.boundary = {
                  top: 0,
                  left: 0,
                  width: boardWidth,
                  height: boardHeight
                };
                if (!_this7.painter) {
                  _ref6 = args.css || args, _width = _ref6.width;
                  _ref7 = args.css || args, _height = _ref7.height;
                  if (!_width && _this7.parentWidth) {
                    Object.assign(args, {
                      width: _this7.parentWidth
                    });
                  }
                  param = {
                    context: ctx,
                    canvas: canvas,
                    width: boardWidth,
                    height: boardHeight,
                    pixelRatio: _this7.dpr,
                    fixed: "".concat(_this7.width || _width ? 'width' : '').concat(_this7.height || _height ? 'height' : ''),
                    listen: {
                      onProgress: function onProgress(v) {
                        _this7.progress = v;
                        _this7.$emit('progress', v);
                      },
                      onEffectFail: function onEffectFail(err) {
                        _this7.$emit('faill', err);
                      }
                    }
                  };
                  if (!use2dCanvas) {
                    param.createImage = _utils.getImageInfo;
                  }
                  _this7.painter = new _painter.default(param, _this7);
                } // vue3 赋值给data会引起图片无法绘制 
                _context5.next = 15;
                return _this7.painter.source(_this7.elements.views.length ? args : JSON.parse(JSON.stringify(args)));
              case 15:
                _yield$_this7$painter = _context5.sent;
                width = _yield$_this7$painter.width;
                height = _yield$_this7$painter.height;
                _this7.boundary.height = _this7.canvasHeight = height;
                _this7.boundary.width = _this7.canvasWidth = width;
                // await sleep(this.sleep);
                _context5.next = 22;
                return _this7.painter.render();
              case 22:
                _context5.next = 24;
                return new Promise(function (resolve) {
                  return _this7.$nextTick(resolve);
                });
              case 24:
                if (use2dCanvas) {
                  _context5.next = 27;
                  break;
                }
                _context5.next = 27;
                return _this7.canvasDraw();
              case 27:
                if (!(afterDelay && use2dCanvas)) {
                  _context5.next = 30;
                  break;
                }
                _context5.next = 30;
                return (0, _utils.sleep)(afterDelay);
              case 30:
                _this7.$emit('done');
                _this7.done = true;
                if (_this7.isCanvasToTempFilePath) {
                  _this7.canvasToTempFilePath().then(function (res) {
                    _this7.$emit('success', res.tempFilePath);
                  }).catch(function (err) {
                    _this7.$emit('fail', new Error(JSON.stringify(err)));
                  });
                }
                return _context5.abrupt("return", Promise.resolve({
                  ctx: ctx,
                  draw: _this7.painter,
                  node: _this7.node
                }));
              case 34:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5);
      }))();
    },
    canvasDraw: function canvasDraw() {
      var _this8 = this;
      var flag = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
      return new Promise(function (resolve, reject) {
        return _this8.ctx.draw(flag, function () {
          return setTimeout(function () {
            return resolve();
          }, _this8.afterDelay);
        });
      });
    },
    getContext: function getContext() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var type, use2dCanvas, dpr, boardWidth, boardHeight, _getContext;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                if (_this9.canvasWidth) {
                  _context6.next = 4;
                  break;
                }
                _this9.$emit('fail', 'painter no size');
                console.error('painter no size: 请给画板或父级设置尺寸');
                return _context6.abrupt("return", Promise.reject());
              case 4:
                if (!(_this9.ctx && _this9.inited)) {
                  _context6.next = 6;
                  break;
                }
                return _context6.abrupt("return", Promise.resolve(_this9.ctx));
              case 6:
                type = _this9.type, use2dCanvas = _this9.use2dCanvas, dpr = _this9.dpr, boardWidth = _this9.boardWidth, boardHeight = _this9.boardHeight;
                _getContext = function _getContext() {
                  return new Promise(function (resolve) {
                    uni.createSelectorQuery().in(_this9).select("#".concat(_this9.canvasId)).boundingClientRect().exec(function (res) {
                      if (res) {
                        var ctx = uni.createCanvasContext(_this9.canvasId, _this9);
                        if (!_this9.inited) {
                          _this9.inited = true;
                          _this9.use2dCanvas = false;
                          _this9.canvas = res;
                        }
                        if (_this9.isPC) {
                          ctx.scale(1 / dpr, 1 / dpr);
                        }
                        _this9.ctx = ctx;
                        resolve(_this9.ctx);
                      }
                    });
                  });
                };
                if (use2dCanvas) {
                  _context6.next = 10;
                  break;
                }
                return _context6.abrupt("return", _getContext());
              case 10:
                return _context6.abrupt("return", new Promise(function (resolve) {
                  uni.createSelectorQuery().in(_this9).select("#".concat(_this9.canvasId)).node().exec(function (res) {
                    var canvas = res[0].node;
                    if (!canvas) {
                      _this9.use2dCanvas = false;
                      resolve(_this9.getContext());
                    }
                    var ctx = canvas.getContext(type);
                    if (!_this9.inited) {
                      _this9.inited = true;
                      _this9.use2dCanvas = true;
                      _this9.canvas = canvas;
                    }
                    _this9.ctx = ctx;
                    resolve(_this9.ctx);
                  });
                }));
              case 11:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6);
      }))();
    },
    canvasToTempFilePath: function canvasToTempFilePath() {
      var _this10 = this;
      var args = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      return new Promise( /*#__PURE__*/function () {
        var _ref8 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8(resolve, reject) {
          var use2dCanvas, canvasId, dpr, fileType, quality, tempFilePath, _ref9, _ref9$top, y, _ref9$left, x, width, height, destWidth, destHeight, success, copyArgs;
          return _regenerator.default.wrap(function _callee8$(_context8) {
            while (1) {
              switch (_context8.prev = _context8.next) {
                case 0:
                  use2dCanvas = _this10.use2dCanvas, canvasId = _this10.canvasId, dpr = _this10.dpr, fileType = _this10.fileType, quality = _this10.quality;
                  if (!use2dCanvas) {
                    _context8.next = 17;
                    break;
                  }
                  _context8.prev = 2;
                  if (!args.pathType && !_this10.pathType) {
                    args.pathType = 'url';
                  }
                  _context8.next = 6;
                  return _this10.setFilePath(_this10.canvas.toDataURL("image/".concat(args.fileType || fileType).replace(/pg/, 'peg'), args.quality || quality), args);
                case 6:
                  tempFilePath = _context8.sent;
                  args.success && args.success({
                    tempFilePath: tempFilePath
                  });
                  resolve({
                    tempFilePath: tempFilePath
                  });
                  _context8.next = 15;
                  break;
                case 11:
                  _context8.prev = 11;
                  _context8.t0 = _context8["catch"](2);
                  args.fail && args.fail(_context8.t0);
                  reject(_context8.t0);
                case 15:
                  _context8.next = 23;
                  break;
                case 17:
                  _ref9 = _this10.boundary || _this10, _ref9$top = _ref9.top, y = _ref9$top === void 0 ? 0 : _ref9$top, _ref9$left = _ref9.left, x = _ref9$left === void 0 ? 0 : _ref9$left, width = _ref9.width, height = _ref9.height;
                  destWidth = width * dpr;
                  destHeight = height * dpr;
                  success = /*#__PURE__*/function () {
                    var _ref10 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7(res) {
                      var _tempFilePath;
                      return _regenerator.default.wrap(function _callee7$(_context7) {
                        while (1) {
                          switch (_context7.prev = _context7.next) {
                            case 0:
                              _context7.prev = 0;
                              _context7.next = 3;
                              return _this10.setFilePath(res.tempFilePath || res);
                            case 3:
                              _tempFilePath = _context7.sent;
                              resolve(Object.assign(res, {
                                tempFilePath: _tempFilePath
                              }));
                              _context7.next = 10;
                              break;
                            case 7:
                              _context7.prev = 7;
                              _context7.t0 = _context7["catch"](0);
                              _this10.$emit('fail', _context7.t0);
                            case 10:
                            case "end":
                              return _context7.stop();
                          }
                        }
                      }, _callee7, null, [[0, 7]]);
                    }));
                    return function success(_x3) {
                      return _ref10.apply(this, arguments);
                    };
                  }();
                  copyArgs = Object.assign({
                    x: x,
                    y: y,
                    width: width,
                    height: height,
                    destWidth: destWidth,
                    destHeight: destHeight,
                    canvasId: canvasId,
                    fileType: fileType,
                    quality: quality,
                    success: success,
                    fail: reject
                  }, args);
                  uni.canvasToTempFilePath(copyArgs, _this10);
                case 23:
                case "end":
                  return _context8.stop();
              }
            }
          }, _callee8, null, [[2, 11]]);
        }));
        return function (_x, _x2) {
          return _ref8.apply(this, arguments);
        };
      }());
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"]))

/***/ }),

/***/ 428:
/*!************************************************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue?vue&type=style&index=0&lang=css& ***!
  \************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=style&index=0&lang=css& */ 429);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_l_painter_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 429:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue?vue&type=style&index=0&lang=css& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/lime-painter/components/l-painter/l-painter-create-component',
    {
        'uni_modules/lime-painter/components/l-painter/l-painter-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('1')['createComponent'](__webpack_require__(419))
        })
    },
    [['uni_modules/lime-painter/components/l-painter/l-painter-create-component']]
]);
