.g_order_info.data-v-8fe3bdf4 {
  padding-bottom: 5rpx;
}
.g_order_foot1.data-v-8fe3bdf4 {
  padding-left: 30rpx;
}
.xilu_status.data-v-8fe3bdf4 {
  padding: 30rpx;
  margin: 0 0 40rpx;
  background: rgba(5, 185, 174, 0.1);
  border-radius: 22rpx;
  font-size: 28rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #555555;
  line-height: 30rpx;
}
.xilu_status .title.data-v-8fe3bdf4 {
  margin: 0 0 20rpx;
  font-size: 34rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: var(--normal);
  line-height: 36rpx;
}
.xilu_goods .img.data-v-8fe3bdf4 {
  margin-right: 30rpx;
  display: block;
  width: 180rpx;
  height: 180rpx;
  border-radius: 15rpx;
}
.xilu .opening_box.data-v-8fe3bdf4 {
  padding: 30rpx;
  background: #F7F9FB;
  border-radius: 20rpx;
}
.xilu .opening_box .icon.data-v-8fe3bdf4 {
  display: block;
  width: 34rpx;
  height: 34rpx;
}
.xilu_code_box.data-v-8fe3bdf4 {
  margin: 0 0 30rpx;
  padding: 30rpx 30rpx 50rpx;
  width: 670rpx;
  background: #F7F9FB;
  border-radius: 25rpx;
}
.xilu_code_box .code.data-v-8fe3bdf4 {
  position: relative;
  margin: 0 auto 30rpx;
  width: 260rpx;
  height: 260rpx;
}
.xilu_code_box .code image.data-v-8fe3bdf4 {
  display: block;
  width: 260rpx;
  height: 260rpx;
}
.xilu_code_box .code .mask.data-v-8fe3bdf4 {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 260rpx;
  height: 260rpx;
  background: #D8D8D8;
  opacity: 0.9;
  display: none;
}
.xilu_code_box .num_box.data-v-8fe3bdf4 {
  margin: 0 auto;
  padding: 0 30rpx 0 35rpx;
  width: 530rpx;
  height: 90rpx;
  background: #FFFFFF;
  border-radius: 34rpx;
  border: 1px solid #EEEEEE;
}
.xilu_code_box .num_box .copy.data-v-8fe3bdf4 {
  width: 66rpx;
  height: 40rpx;
  background: var(--normal);
  border-radius: 14rpx;
  font-size: 22rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 40rpx;
  text-align: center;
}
.xilu_code_box.disabled .code .mask.data-v-8fe3bdf4 {
  display: block;
}
.xilu_code_box.disabled .num_box .num.data-v-8fe3bdf4 {
  color: #898989;
  text-decoration: line-through;
}
.xilu_code_box.disabled .num_box .copy.data-v-8fe3bdf4 {
  display: none;
}
.xilu_code_swiper.data-v-8fe3bdf4 {
  margin: 0 0 30rpx;
}
.xilu_code_swiper swiper.data-v-8fe3bdf4 {
  height: 590rpx;
}
.xilu_code_swiper swiper .xilu_code_box.data-v-8fe3bdf4 {
  margin: 0;
  width: 568rpx;
  padding: 30rpx;
}
.xilu_code_swiper swiper .xilu_code_box .num_box.data-v-8fe3bdf4 {
  width: 508rpx;
}
.xilu_code_swiper swiper .xilu_code_box .index.data-v-8fe3bdf4 {
  margin: 30rpx auto 0;
  padding: 0 6rpx;
  height: 34rpx;
  background: rgba(5, 185, 174, 0.1);
  border-radius: 11rpx;
  font-size: 24rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #05B9AE;
  line-height: 34rpx;
  text-align: center;
}
.xilu .page-foot ~ .container.data-v-8fe3bdf4 {
  padding-bottom: 170rpx;
}
.xilu .container.data-v-8fe3bdf4 {
  padding-top: 30rpx;
  padding-left: 40rpx;
  padding-right: 40rpx;
}

