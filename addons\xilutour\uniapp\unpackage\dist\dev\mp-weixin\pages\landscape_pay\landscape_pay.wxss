.xilu .g_order_info.data-v-540e25e0 {
  background-color: #fff;
}
.xilu_travel .img.data-v-540e25e0 {
  display: block;
  width: 180rpx;
  height: 180rpx;
  border-radius: 15rpx;
}
.xilu_number.data-v-540e25e0 {
  padding: 0 35rpx 0 30rpx;
  width: 670rpx;
  height: 110rpx;
  background: #FFFFFF;
  box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
  border-radius: 25rpx;
  font-size: 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #555555;
  line-height: 34rpx;
}
.xilu_number image.data-v-540e25e0 {
  display: block;
  width: 40rpx;
  height: 40rpx;
}
.xilu_number .num.data-v-540e25e0 {
  width: 62rpx;
  font-size: 36rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #101010;
  line-height: 40rpx;
  text-align: center;
}
.xilu .container.data-v-540e25e0 {
  padding: 0 0 200rpx !important;
  background: #F7F9FB;
}
.xilu .opening_box.data-v-540e25e0 {
  padding: 30rpx;
  background: #F7F9FB;
  border-radius: 20rpx;
}
.xilu .opening_box .icon.data-v-540e25e0 {
  display: block;
  width: 34rpx;
  height: 34rpx;
}
.xilu .page-foot.data-v-540e25e0 {
  background: #F7F9FB;
}
.xilu .page-foot.data-v-540e25e0::after {
  background: #F7F9FB;
}

