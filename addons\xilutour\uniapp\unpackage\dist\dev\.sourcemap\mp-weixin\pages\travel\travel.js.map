{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel/travel.vue?ee4a", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel/travel.vue?c406", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel/travel.vue?9568", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel/travel.vue?c24f", "uni-app:///pages/travel/travel.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel/travel.vue?2f20", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel/travel.vue?a9de"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tourList", "data", "statusBarHeight", "swiper<PERSON><PERSON>rent", "messageCount", "categoryList", "bannerList", "currentCity", "tourListMore", "page", "query", "sort", "order", "category_id", "searchTab", "isShowSelect", "onLoad", "uni", "onShow", "onPullDownRefresh", "onShareAppMessage", "onShareTimeline", "onUnload", "onReachBottom", "methods", "swiper<PERSON><PERSON>e", "bindCityChange", "url", "tabClick", "bindSearch", "bindChangeSearchTab", "events", "searchSuccess", "success", "res", "bindChangeSort", "refreshPage", "group", "loading", "refresh", "fetch", "bannerjump", "bindMessage", "closeSelect"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA+zB,CAAgB,+xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC2Hn1B;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAP;MACAQ;QACAC;MACA;MACAC;QAAAC;QAAAC;QAAAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACAP;IACAQ;MACAR;MACAA;IACA;EACA;EACAS;IAAA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC,iDAEA;EACAC,6CAEA;EACAC;IACAL;EACA;EACAM;IACA;EACA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACAT;QACAU;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACAZ;QACAU;MACA;IACA;IACA;IACAG;MAAA;MACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;QACAb;UACAU;UACAI;YACAC;cACA;cACA;YACA;UACA;UACAC;YACAC;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;QACAT;QACA1B;UACAoC;QACA;QACAC;QACAL;UACA;QACA;MACA;MACA;MACA;QACAN;QACA1B;QACAqC;QACAL;UACA;UACA;UACA;UACA;QACA;MACA;MAEAhB;IACA;IACAsB;MAAA;MACA;MACA;QACA9B;MACA;MACA;MACA;IACA;IACA+B;MACA;MACA9B;MACA,0GAEA;IACA;IACA;IACA+B;MACA;QACA;MACA;MACAxB;QACAU;MACA;IACA;IACAe;MACAzB;QACAU;MACA;IACA;IACAgB;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACpSA;AAAA;AAAA;AAAA;AAAkhD,CAAgB,m5CAAG,EAAC,C;;;;;;;;;;;ACAtiD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/travel/travel.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/travel/travel.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./travel.vue?vue&type=template&id=2f7c86f4&scoped=true&\"\nvar renderjs\nimport script from \"./travel.vue?vue&type=script&lang=js&\"\nexport * from \"./travel.vue?vue&type=script&lang=js&\"\nimport style0 from \"./travel.vue?vue&type=style&index=0&id=2f7c86f4&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2f7c86f4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/travel/travel.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel.vue?vue&type=template&id=2f7c86f4&scoped=true&\"", "var components\ntry {\n  components = {\n    tourList: function () {\n      return import(\n        /* webpackChunkName: \"components/tour-list/tour-list\" */ \"@/components/tour-list/tour-list.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"container\" style=\"padding-bottom: 30rpx;\">\r\n\t\t\t<!-- <tabbar :footIdx=\"2\" footText=\"路线\"></tabbar> -->\r\n\t\t\t<image class=\"m-backdrop\" src=\"/static/icon/icon_bg1.png\" mode=\"widthFix\"></image>\r\n\t\t\t<view class=\"m-header\">\r\n\t\t\t\t<image class=\"m-backdrop\" src=\"/static/icon/icon_bg1.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view class=\"g-custom-nav flex-box plr30\"\r\n\t\t\t\t\t:style=\"{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }\">\r\n\t\t\t\t\t<view class=\"message\" :class=\"{active:messageCount>0}\" @click=\"bindMessage()\">\r\n\t\t\t\t\t\t<image src=\"/static/icon/icon_message.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<view>消息</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"search_box flex-box\">\r\n\t\t\t\t\t\t<view class=\"addr m-ellipsis\" @click=\"bindCityChange\">{{currentCity?currentCity.name:''}}</view>\r\n\t\t\t\t\t\t<image class=\"icon_arrow\" src=\"/static/icon/icon_arrow.png\"></image>\r\n\t\t\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t\t\t<image class=\"icon_search\" src=\"/static/icon/icon_search.png\"></image>\r\n\t\t\t\t\t\t<input class=\"input flex-1\"  @click=\"bindSearch()\" disabled=\"true\" type=\"text\" placeholder=\"出发城市/目的地\" placeholder-class=\"cola\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!--  -->\r\n\t\t\t\t<view class=\"xilu_select\" v-show=\"isShowSelect\">\r\n\t\t\t\t\t<view class=\"g_tab flex-box\">\r\n\t\t\t\t\t\t<view class=\"item flex-1\" :class=\"{'active': query.category_id == item.id}\"\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in categoryList\" @click=\"tabClick(item.id)\">{{item.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"g_select flex-box\">\r\n\t\t\t\t\t\t<view class=\"flex-box flex-center flex-1\" @click=\"bindChangeSearchTab(0)\" :class=\"{active: searchTab==0}\">\r\n\t\t\t\t\t\t\t<view>综合</view>\r\n\t\t\t\t\t\t\t<image :src=\"'/static/icon/icon_arrow1' + (searchTab==0 ? 'on' : '') + '.png'\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex-box flex-center flex-1\" @click=\"bindChangeSearchTab(1)\" :class=\"{active: searchTab==1}\">\r\n\t\t\t\t\t\t\t<view>价格</view>\r\n\t\t\t\t\t\t\t<image class=\"icon_price\" :class=\"{'rotate': searchTab==1 && query.order=='desc'}\" src=\"/static/icon/icon_arrow3.png\"\r\n\t\t\t\t\t\t\t\tmode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex-box flex-center flex-1\" @click=\"bindChangeSearchTab(2)\" :class=\"{active: searchTab==2}\">\r\n\t\t\t\t\t\t\t<view>热门</view>\r\n\t\t\t\t\t\t\t<image :src=\"'/static/icon/icon_arrow1' + (searchTab==2 ? 'on' : '') + '.png'\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex-box flex-center flex-1\" @click=\"bindChangeSearchTab(3)\" :class=\"{active: searchTab==3}\">\r\n\t\t\t\t\t\t\t<view>筛选</view>\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_arrow1.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"g_select_wrap\">\r\n\t\t\t\t\t\t<block v-if=\"searchTab == 0\">\r\n\t\t\t\t\t\t\t<view class=\"item\" :class=\"{active:query.sort=='weigh'}\" @click=\"bindChangeSort('weigh')\">综合</view>\r\n\t\t\t\t\t\t\t<view class=\"item\" :class=\"{active:query.sort=='points'}\" @click=\"bindChangeSort('points')\">推荐</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t<block v-if=\"searchTab == 2\">\r\n\t\t\t\t\t\t\t<view class=\"item\" :class=\"{active:query.sort=='updatetime'}\" @click=\"bindChangeSort('updatetime')\">最新</view>\r\n\t\t\t\t\t\t\t<view class=\"item\" :class=\"{active:query.sort=='appoint_count'}\" @click=\"bindChangeSort('appoint_count')\">热门</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"g_mask\" v-show=\"isShowSelect\" @click=\"closeSelect\"></view>\r\n\r\n\r\n\t\t\t<view class=\"pr\" :style=\"{ paddingTop: 'calc(90rpx + ' + statusBarHeight + 'px)' }\">\r\n\t\t\t\t<view class=\"xilu_swiper\">\r\n\t\t\t\t\t<swiper class=\"swiper\" :current=\"swiperCurrent\" circular @change=\"swiperChange\">\r\n\t\t\t\t\t\t<swiper-item v-for=\"(item,index) in bannerList\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"nav\" :class=\"{'scale': swiperCurrent !==index}\" @click=\"bannerjump(item.minapp_url)\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item.thumb_image_text\" mode=\"aspectFill\" class=\"img\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t</swiper>\r\n\t\t\t\t\t<view class=\"swiper_dots flex-box flex-center\">\r\n\t\t\t\t\t\t<view class=\"dots\" v-for=\"(item,index) in bannerList\" :key=\"index\"\r\n\t\t\t\t\t\t\t:class=\"{'active': swiperCurrent == index}\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"xilu_select\" :style=\"{top: 'calc(90rpx + ' + statusBarHeight + 'px)'}\">\r\n\t\t\t\t\t<view class=\"g_tab flex-box\">\r\n\t\t\t\t\t\t<view class=\"item flex-1\" :class=\"{'active': query.category_id == item.id}\"\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in categoryList\" @click=\"tabClick(item.id)\">{{item.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"g_select flex-box\">\r\n\t\t\t\t\t\t<view class=\"flex-box flex-center flex-1\" @click=\"bindChangeSearchTab(0)\" :class=\"{active: searchTab==0}\">\r\n\t\t\t\t\t\t\t<view>综合</view>\r\n\t\t\t\t\t\t\t<image :src=\"'/static/icon/icon_arrow1' + (searchTab==0 ? 'on' : '') + '.png'\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex-box flex-center flex-1\" @click=\"bindChangeSearchTab(1)\" :class=\"{active: searchTab==1}\">\r\n\t\t\t\t\t\t\t<view>价格</view>\r\n\t\t\t\t\t\t\t<image class=\"icon_price\" :class=\"{'rotate': searchTab==1 && query.order=='desc'}\" src=\"/static/icon/icon_arrow3.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex-box flex-center flex-1\" @click=\"bindChangeSearchTab(2)\" :class=\"{active: searchTab==2}\">\r\n\t\t\t\t\t\t\t<view>热门</view>\r\n\t\t\t\t\t\t\t<image :src=\"'/static/icon/icon_arrow1' + (searchTab==2 ? 'on' : '') + '.png'\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"flex-box flex-center flex-1\" @click=\"bindChangeSearchTab(3)\" :class=\"{active: searchTab==3}\">\r\n\t\t\t\t\t\t\t<view>筛选</view>\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_arrow1.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"p40\">\r\n\t\t\t\t\t<view class=\"g_travel_list\">\r\n\t\t\t\t\t\t<tour-list :tourList=\"tourList\"></tour-list>\r\n\t\t\t\t\t\t<view class=\"g-btn3-wrap\">\r\n\t\t\t\t\t\t\t<view class=\"g-btn3\" @click=\"fetch\">{{tourListMore.text}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport tourList from '@/components/tour-list/tour-list.vue'\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttourList\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatusBarHeight: 20,\r\n\t\t\t\tswiperCurrent: 0,\n\t\t\t\tmessageCount: 0,\r\n\t\t\t\tcategoryList: [],\r\n\t\t\t\tbannerList: [],\r\n\t\t\t\tcurrentCity: null,\r\n\t\t\t\ttourList: [],\r\n\t\t\t\ttourListMore: {\r\n\t\t\t\t\tpage: 1\r\n\t\t\t\t},\n\t\t\t\tquery: {sort: 'weigh',order: 'desc',category_id: -1},\n\t\t\t\tsearchTab: 0,\r\n\t\t\t\tisShowSelect: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.statusBarHeight = app.globalData.statusBarHeight;\r\n\t\t\tlet page = this;\r\n\t\t\tthis.currentCity = this.$core.getCurrentCity();\r\n\t\t\tpage.refreshPage();\r\n\t\t\tuni.$on(app.globalData.Event.CurrentCityChange, function(currentCity) {\r\n\t\t\t\tpage.currentCity = currentCity;\r\n\t\t\t\tpage.refresh();\r\n\t\t\t})\r\n\t\t},\n\t\tonShow() {\n\t\t\tif(this.$core.getUserinfo()){\n\t\t\t\tthis.$util.getMessageCount(false).then(count=>{\n\t\t\t\t\tthis.messageCount = count\n\t\t\t\t})\n\t\t\t}\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\tthis.refreshPage();\r\n\t\t},\r\n\t\tonShareAppMessage() {\r\n\r\n\t\t},\r\n\t\tonShareTimeline() {\r\n\r\n\t\t},\r\n\t\tonUnload() {\r\n\t\t\tuni.$off(app.globalData.Event.CurrentCityChange,this);\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tthis.fetch();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tswiperChange(e) {\r\n\t\t\t\tthis.swiperCurrent = e.detail.current\r\n\t\t\t},\r\n\t\t\t//更换城市\r\n\t\t\tbindCityChange() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/change_city/change_city'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//分类\r\n\t\t\ttabClick(id) {\r\n\t\t\t\tthis.query.category_id = id;\r\n\t\t\t\tthis.refresh();\r\n\t\t\t},\n\t\t\tbindSearch(){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/search/search'\n\t\t\t\t})\n\t\t\t},\r\n\t\t\t//排序,0=综合（弹窗），1=价格，2=热门（弹窗)\n\t\t\tbindChangeSearchTab(searchTabIndex){\n\t\t\t\tif(searchTabIndex == 0 || searchTabIndex == 2){\n\t\t\t\t\tthis.isShowSelect = this.isShowSelect&&this.searchTab == searchTabIndex?false:true;\n\t\t\t\t}else if(searchTabIndex == 1){\n\t\t\t\t\tthis.query.sort = 'salesprice';\n\t\t\t\t\tthis.query.order = this.query.order == 'asc'?'desc':'asc';\n\t\t\t\t\tthis.isShowSelect = false;\n\t\t\t\t\tthis.refresh();\n\t\t\t\t}else if(searchTabIndex == 3){\n\t\t\t\t\tlet query = this.query;\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl:'/pages/filtrate/filtrate?type=tour',\n\t\t\t\t\t\tevents:{\n\t\t\t\t\t\t\tsearchSuccess: data=>{\n\t\t\t\t\t\t\t\tthis.query = data;\n\t\t\t\t\t\t\t\tthis.refresh();\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t\tres.eventChannel.emit(\"searchTransform\",query)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tthis.searchTab = searchTabIndex;\n\t\t\t},\n\t\t\t//二级排序\n\t\t\tbindChangeSort(sort){\n\t\t\t\tthis.query.sort = sort;\n\t\t\t\tthis.query.order = 'desc';\n\t\t\t\tthis.isShowSelect = false;\n\t\t\t\tthis.refresh();\n\t\t\t},\r\n\r\n\t\t\trefreshPage() {\r\n\t\t\t\t//轮播\r\n\t\t\t\tthis.$core.get({\r\n\t\t\t\t\turl: 'xilutour.banner/index',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tgroup: 'tour_index'\r\n\t\t\t\t\t},\r\n\t\t\t\t\tloading: false,\r\n\t\t\t\t\tsuccess: (ret) => {\r\n\t\t\t\t\t\tthis.bannerList = ret.data;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t//分类\r\n\t\t\t\tthis.$core.get({\r\n\t\t\t\t\turl: 'xilutour.common/tour_category',\r\n\t\t\t\t\tdata: {},\r\n\t\t\t\t\tloading: false,\r\n\t\t\t\t\tsuccess: (ret) => {\r\n\t\t\t\t\t\tthis.categoryList = ret.data;\r\n\t\t\t\t\t\tif (ret.data.length > 0) this.query.category_id = ret.data[0].id;\r\n\t\t\t\t\t\t//线路列表\r\n\t\t\t\t\t\tthis.refresh();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t},\r\n\t\t\trefresh(cacheQuery = false) {\r\n\t\t\t\tthis.tourList = [];\r\n\t\t\t\tthis.tourListMore = {\r\n\t\t\t\t\tpage: 1\r\n\t\t\t\t};\r\n\t\t\t\tif (cacheQuery) this.query = {};\r\n\t\t\t\tthis.fetch();\r\n\t\t\t},\r\n\t\t\tfetch() {\r\n\t\t\t\tlet query = this.query;\r\n\t\t\t\tquery.pagesize = 10;\r\n\t\t\t\tthis.$util.fetch(this, 'xilutour.tour/lists', query, 'tourListMore', 'tourList', 'data', data => {\r\n\r\n\t\t\t\t})\r\n\t\t\t},\n\t\t\t//轮播图\n\t\t\tbannerjump(url){\n\t\t\t\tif(url.trim() == ''){\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: url\n\t\t\t\t})\n\t\t\t},\n\t\t\tbindMessage(){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/my_message/my_message'\n\t\t\t\t})\n\t\t\t},\r\n\t\t\tcloseSelect() {\r\n\t\t\t\tthis.isShowSelect = false\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.xilu {\r\n\t\t&_swiper {\r\n\t\t\tposition: relative;\r\n\t\t\tpadding: 30rpx 40rpx 40rpx;\r\n\r\n\t\t\t.swiper {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 670rpx;\r\n\t\t\t\theight: 320rpx;\r\n\r\n\t\t\t\t.nav {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\twidth: 670rpx;\r\n\t\t\t\t\theight: 320rpx;\r\n\t\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.img {\r\n\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\twidth: 670rpx;\r\n\t\t\t\t\theight: 320rpx;\r\n\t\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.swiper_dots {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright: 0;\r\n\r\n\t\t\t\t.dots {\r\n\t\t\t\t\tmargin: 0 4rpx;\r\n\t\t\t\t\twidth: 14rpx;\r\n\t\t\t\t\theight: 4rpx;\r\n\t\t\t\t\tbackground: #D8D8D8;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.dots.active {\r\n\t\t\t\t\tbackground: #333333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_select {\r\n\t\t\tposition: sticky;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\tz-index: 10;\r\n\t\t\tbackground-color: #fff;\r\n\t\t}\r\n\r\n\t\t.container {\r\n\t\t\toverflow-y: unset;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel.vue?vue&type=style&index=0&id=2f7c86f4&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel.vue?vue&type=style&index=0&id=2f7c86f4&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341195\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}