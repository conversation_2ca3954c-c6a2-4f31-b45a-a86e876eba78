{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/personal_center/personal_center.vue?6074", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/personal_center/personal_center.vue?b1f8", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/personal_center/personal_center.vue?b979", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/personal_center/personal_center.vue?3cf1", "uni-app:///pages/personal_center/personal_center.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/personal_center/personal_center.vue?9cda", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/personal_center/personal_center.vue?bf54"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "statusBarHeight", "messageCount", "userinfo", "coupon_count", "collection_count", "user_message", "nickname", "avatar", "onLoad", "uni", "page", "onShow", "onPullDownRefresh", "onUnload", "methods", "getUserinfo", "url", "loading", "success", "fail", "console", "jump", "bindScan", "layout", "title", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuH51B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;IACA;IACAC;MACAC;IACA;IACAD;MACAC;QAAAJ;QAAAC;MAAA;IACA;EACA;EACAI;IAAA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAH;EACA;EACAI;IACAJ;IACAA;EACA;EACAK;IACAC;MAAA;MACA;QAAAC;QAAAjB;QAAAkB;QAAAC;UACA;QACA;QAAAC;UACAC;QACA;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;MACA;MACAZ;QACAO;MACA;IACA;IACAM;MACA;QACA;MACA;MACAb;QACAS;UACAE;UACAX;YACAO;UACA;QACA;MACA;IAEA;IACAO;MACA;MACAd;QACAe;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3MA;AAAA;AAAA;AAAA;AAA2hD,CAAgB,45CAAG,EAAC,C;;;;;;;;;;;ACA/iD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/personal_center/personal_center.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/personal_center/personal_center.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./personal_center.vue?vue&type=template&id=3e8d8070&scoped=true&\"\nvar renderjs\nimport script from \"./personal_center.vue?vue&type=script&lang=js&\"\nexport * from \"./personal_center.vue?vue&type=script&lang=js&\"\nimport style0 from \"./personal_center.vue?vue&type=style&index=0&id=3e8d8070&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3e8d8070\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/personal_center/personal_center.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./personal_center.vue?vue&type=template&id=3e8d8070&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./personal_center.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./personal_center.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"container\">\r\n\t\t\t\t<image class=\"m-backdrop\" src=\"../../static/icon/icon_bg2.png\" mode=\"widthFix\"></image>\r\n\t\t\t<view class=\"m-header\">\r\n\t\t\t\t\t<image class=\"m-backdrop\" src=\"../../static/icon/icon_bg2.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view class=\"g-custom-nav flex-box flex-center plr30\"\r\n\t\t\t\t\t:style=\"{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }\">\r\n\t\t\t\t\t<view class=\"fs26\">个人中心</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"page-body pr\" :style=\"{ paddingTop: 'calc(90rpx + ' + statusBarHeight + 'px)' }\">\r\n\t\t\t\t<view class=\"flex-box xilu_user\">\r\n\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t<view class=\"fs40 mb40 col-10\" @click=\"jump('/pages/basic_information/basic_information',1)\">{{userinfo.nickname || '请登录'}}</view>\r\n\t\t\t\t\t\t<view class=\"flex-box tc\">\r\n\t\t\t\t\t\t\t<view class=\"pr40 mr30\" @click=\"jump('/pages/coupon/coupon',1)\">\r\n\t\t\t\t\t\t\t\t<view class=\"num\">{{userinfo.coupon_count || 0}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"fs28 col-89\">优惠券</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"pr40 mr30\" @click=\"jump('/pages/my_collection/my_collection',1)\">\r\n\t\t\t\t\t\t\t\t<view class=\"num\">{{userinfo.collection_count || 0}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"fs28 col-89\">收藏</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"pr40 mr30\" @click=\"jump('/pages/my_message/my_message')\">\r\n\t\t\t\t\t\t\t\t<view class=\"num\" :class=\"{dot:messageCount>0}\">{{messageCount}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"fs28 col-89\">消息</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"head\" @click=\"jump('/pages/basic_information/basic_information',1)\">\r\n\t\t\t\t\t\t<image :src=\"userinfo.avatar\" mode=\"aspectFill\" class=\"img_head\"></image>\r\n\t\t\t\t\t\t<image src=\"../../static/icon/icon_edit.png\" mode=\"aspectFit\" class=\"icon_edit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"flex-box flex-between\">\r\n\t\t\t\t\t<view class=\"xilu_menu\" @click=\"jump('/pages/landscape_order/landscape_order',1)\" style=\"background-color: #08CDC1;\">\r\n\t\t\t\t\t\t<view class=\"title\">景点订单</view>\r\n\t\t\t\t\t\t<view class=\"flex-box\">\r\n\t\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t\t<text class=\"fs40 col-f mr10\">{{userinfo.scenery_order_count}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"fs24\" style=\"color: #B8FFFB;\">个订单</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"view\">查看</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"xilu_menu\" @click=\"jump('/pages/travel_order/travel_order',1)\" style=\"background-color: #FFAB29;\">\r\n\t\t\t\t\t\t<view class=\"title\">旅游订单</view>\r\n\t\t\t\t\t\t<view class=\"flex-box\">\r\n\t\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t\t<text class=\"fs40 col-f mr10\">{{userinfo.tour_order_count}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"fs24\" style=\"color: #FFEDD0;\">个订单</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"view\">查看</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"xilu_nav_box\">\r\n\t\t\t\t\t<view class=\"title\">我的服务</view>\r\n\t\t\t\t\t<view class=\"flex-box flex-wrap\">\r\n\t\t\t\t\t\t<view class=\"item\" @click=\"jump('/pages/my_commission/my_commission',1)\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_nav1.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>我的佣金</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\" @click=\"jump('/pages/traveler_list/traveler_list',1)\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_nav10.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>出行人</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\"  @click=\"jump('/pages/invite_friends/invite_friends',1)\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_nav2.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>邀请好友</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\" @click=\"jump('/pages/problem/problem',1)\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_nav3.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>常见问题</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\" @click=\"jump('/pages/singlepage/singlepage?type=privacy_agreement',0)\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_nav4.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>隐私协议</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button class=\"item\" hover-class=\"none\" open-type=\"contact\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_nav5.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>我的客服</view>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<view class=\"item\" @click=\"layout()\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_nav6.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>退出登录</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"xilu_nav_box\" v-if=\"userinfo.verifier_status\">\r\n\t\t\t\t\t<view class=\"title\">核销功能</view>\r\n\t\t\t\t\t<view class=\"flex-box flex-wrap\">\r\n\t\t\t\t\t\t<view class=\"item\" @click=\"bindScan()\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_nav7.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>扫码核销</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\" @click=\"jump('/pages/write_off_order/write_off_order',1)\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_nav8.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>核销记录</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item\" @click=\"jump('/pages/store_information/store_information',1)\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_nav9.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>门店信息</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\n\tconst app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatusBarHeight: 20,\n\t\t\t\tmessageCount:0,\n\t\t\t\tuserinfo:{\n\t\t\t\t\tcoupon_count:0,\n\t\t\t\t\tcollection_count:0,\n\t\t\t\t\tuser_message: 0,\n\t\t\t\t\tnickname:'',\n\t\t\t\t\tavatar:'',\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\n\t\tonLoad() {\n\t\t\tlet page = this;\n\t\t\tthis.statusBarHeight = getApp().globalData.statusBarHeight;\n\t\t\tif(this.$core.getUserinfo()) {\n\t\t\t\tthis.getUserinfo();\n\t\t\t}\n\t\t\tuni.$on(\"user_update\",function(){\n\t\t\t\tpage.getUserinfo();\n\t\t\t})\n\t\t\tuni.$on(app.globalData.Event.loginOut,function(){\n\t\t\t\tpage.userinfo = {nickname:'',avatar:''};\n\t\t\t})\n\t\t},\n\t\tonShow() {\n\t\t\tif(this.$core.getUserinfo()){\n\t\t\t\tthis.$util.getMessageCount(false).then(count=>{\n\t\t\t\t\tthis.messageCount = count\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tthis.getUserinfo();\n\t\t\tuni.stopPullDownRefresh();\n\t\t},\n\t\tonUnload() {\n\t\t\tuni.$off(\"user_update\");\n\t\t\tuni.$off(app.globalData.Event.loginOut)\n\t\t},\n\t\tmethods:{\n\t\t\tgetUserinfo(){\n\t\t\t\tthis.$core.post({url: 'xilutour.user/info',data: {},loading: false,success: ret => {\n\t\t\t\t\t\tthis.userinfo = ret.data;\n\t\t\t\t\t},fail: err => {\n\t\t\t\t\t\tconsole.log(err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tjump(url,needLogin){\n\t\t\t\tif(needLogin){\n\t\t\t\t\tif(!this.$core.getUserinfo(true)){\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: url\n\t\t\t\t})\n\t\t\t},\n\t\t\tbindScan(){\n\t\t\t\tif(!this.$core.getUserinfo(true)){\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\tuni.scanCode({\n\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\tconsole.log('条码类型：', res);\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/'+res.path\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t},\n\t\t\tlayout(){\n\t\t\t\tthis.$core.logout();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle:\"退出登录成功\",\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.xilu {\r\n\t\t&_user {\r\n\t\t\tmargin: 0 0 46rpx;\r\n\r\n\t\t\t.num {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin: 0 0 15rpx;\r\n\t\t\t\tfont-size: 46rpx;\r\n\t\t\t\tfont-family: Poppins, Poppins;\r\n\t\t\t\tfont-weight: 800;\r\n\t\t\t\tcolor: #101010;\r\n\t\t\t\tline-height: 48rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.num.dot::after {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 0rpx;\r\n\t\t\t\ttop: 0rpx;\r\n\t\t\t\tcontent: '';\r\n\t\t\t\twidth: 10rpx;\r\n\t\t\t\theight: 10rpx;\r\n\t\t\t\tbackground: #F35F4B;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t}\r\n\r\n\t\t\t.head {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\twidth: 208rpx;\r\n\t\t\t\theight: 208rpx;\r\n\t\t\t\tborder: 6rpx solid #FFFFFF;\r\n\t\t\t\tborder-radius: 50%;\r\n\r\n\t\t\t\t.img_head {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.icon_edit {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tright: 20rpx;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\twidth: 45rpx;\r\n\t\t\t\t\theight: 45rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_menu {\r\n\t\t\tpadding: 30rpx;\r\n\t\t\twidth: 320rpx;\r\n\t\t\tborder-radius: 30rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\tmargin: 0 0 24rpx;\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tline-height: 36rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.view {\r\n\t\t\t\twidth: 78rpx;\r\n\t\t\t\theight: 46rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t\tline-height: 46rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_nav_box {\r\n\t\t\tmargin: 30rpx 0 0;\r\n\t\t\tpadding: 30rpx 0 40rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tbox-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);\r\n\t\t\tborder-radius: 30rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\tpadding: 0 26rpx;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #101010;\r\n\t\t\t\tline-height: 38rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.item {\r\n\t\t\t\tpadding: 0;\r\n\t\t\t\tmargin: 40rpx 0 0;\r\n\t\t\t\twidth: 25%;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #555555;\r\n\t\t\t\tline-height: 30rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tbackground-color: #fff;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\tmargin: 0 auto 20rpx;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.item::after {\r\n\t\t\t\tcontent: none;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.container {\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t}\r\n\r\n\t\t.page-body {\r\n\t\t\tpadding: 40rpx 40rpx 30rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./personal_center.vue?vue&type=style&index=0&id=3e8d8070&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./personal_center.vue?vue&type=style&index=0&id=3e8d8070&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341193\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}