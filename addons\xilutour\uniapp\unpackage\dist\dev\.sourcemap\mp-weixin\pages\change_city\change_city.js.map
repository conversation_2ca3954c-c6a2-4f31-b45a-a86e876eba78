{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/change_city/change_city.vue?d43f", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/change_city/change_city.vue?1a25", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/change_city/change_city.vue?8288", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/change_city/change_city.vue?eaf7", "uni-app:///pages/change_city/change_city.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/change_city/change_city.vue?f5d2", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/change_city/change_city.vue?c50d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentCity", "hotCities", "list", "onLoad", "methods", "fetchList", "url", "loading", "success", "bindCity", "id", "name", "uni", "bindClick"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkBx1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAAC;QAAAC;QAAAR;QAAAS;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;QACAC;MACA;MACA;MACAC;MACAA;IACA;IACA;IACAC;MACA;MACA;MACA;QACAH;QACAC;MACA;MACA;MACAC;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAAuhD,CAAgB,w5CAAG,EAAC,C;;;;;;;;;;;ACA3iD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/change_city/change_city.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/change_city/change_city.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./change_city.vue?vue&type=template&id=ffd78488&scoped=true&\"\nvar renderjs\nimport script from \"./change_city.vue?vue&type=script&lang=js&\"\nexport * from \"./change_city.vue?vue&type=script&lang=js&\"\nimport style0 from \"./change_city.vue?vue&type=style&index=0&id=ffd78488&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ffd78488\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/change_city/change_city.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./change_city.vue?vue&type=template&id=ffd78488&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIndexedList: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-indexed-list/uni-indexed-list\" */ \"@/components/uni-indexed-list/uni-indexed-list.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./change_city.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./change_city.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"xilu\">\n        <view class=\"pt40 plr30 pb30\">\n            <view class=\"flex-box change_city mb30\">\n                <text class=\"pr10\">{{currentCity?currentCity.name:''}}</text>\n            </view>\n            <view class=\"pt70 pb30 fs28 col6\">热门城市</view>\n            <view class=\"flex-box flex-wrap tc\">\n                <view class=\"city_item\" @click=\"bindCity(index)\" v-for=\"(item,index) in hotCities\">{{item.name}}</view>\n            </view>\n        </view>\n        <view class=\"list_box\">\n            <uni-indexed-list :options=\"list\" :show-select=\"false\" @click=\"bindClick\" />\n        </view>\n    </view>\n</template>\n\n<script>\n\tconst app = getApp();\n    export default {\n        data() {\n            return {\n\t\t\t\tcurrentCity: null,\n\t\t\t\thotCities:[],\n                list: []\n\t\t\t};\n        },\n\t\tonLoad() {\n\t\t\t//城市\n\t\t\tthis.currentCity = this.$core.getCurrentCity();\n\t\t\tthis.fetchList();\n\t\t},\n        methods: {\n\t\t\tfetchList(){\n\t\t\t\tthis.$core.get({url: 'xilutour.common/cities',loading:false,data:{},success: (ret) => {\n\t\t\t\t\t\tthis.hotCities = ret.data.hot_cities;\n\t\t\t\t\t\tthis.list = ret.data.cities;\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t//热门城市\n\t\t\tbindCity(index) {\n\t\t\t\tlet city = this.hotCities[index];\n\t\t\t\tlet area = {\n\t\t\t\t\tid: city.id,\n\t\t\t\t\tname: city.name\n\t\t\t\t}\n\t\t\t\tthis.$core.setCurrentCity(area);\n\t\t\t\tuni.$emit(app.globalData.Event.CurrentCityChange2,this.$core.getCurrentCity());\n\t\t\t\tuni.navigateBack({})\n\t\t\t},\n\t\t\t//修改城市\n            bindClick(e) {\n                //console.log('点击item，返回数据' + JSON.stringify(e),e);\n\t\t\t\tlet city = e.item;\n\t\t\t\tlet area = {\n\t\t\t\t\tid: city.city.id,\n\t\t\t\t\tname: city.city.name\n\t\t\t\t}\n\t\t\t\tthis.$core.setCurrentCity(area);\n\t\t\t\tuni.$emit(app.globalData.Event.CurrentCityChange2,this.$core.getCurrentCity());\n\t\t\t\tuni.navigateBack({})\n            }\n        }\n    }\n</script>\n\n<style lang=\"less\" scoped>\n    .xilu {\n        display: flex;\n        width: 100vw;\n        height: 100vh;\n        flex-direction: column;\n\n        .change_city::after {\n            content: \"\";\n            display: block;\n            width: 0;\n            height: 0;\n            border-width: 14rpx 10rpx 0;\n            border-color: #999 transparent transparent;\n            border-style: solid;\n            margin-top: 8rpx;\n        }\n\n        .list_box {\n            position: relative;\n            width: 100vw;\n            flex: 1;\n        }\n\n        .city_item {\n            width: 190rpx;\n            height: 65rpx;\n            line-height: 65rpx;\n            background: #F5F6F7;\n            border-radius: 33rpx;\n            margin-right: 30rpx;\n            margin-bottom: 30rpx;\n            font-size: 26rpx;\n            color: #333;\n        }\n    }\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./change_city.vue?vue&type=style&index=0&id=ffd78488&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./change_city.vue?vue&type=style&index=0&id=ffd78488&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341179\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}