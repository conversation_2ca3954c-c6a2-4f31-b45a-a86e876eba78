<view class="xilu data-v-0354b366"><view class="page-head bg-ghostWhite data-v-0354b366"><view class="g_tab data-v-0354b366"><block wx:for="{{categoryList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['catagoryChange',[index]]]]]}}" class="{{['item','','data-v-0354b366',(index==categroyIndex)?'active':'']}}" bindtap="__e">{{item.name}}</view></block></view></view><view class="container plr30 pb30 bg-ghostWhite data-v-0354b366"><liu-waterfall vue-id="67423a60-1" dataList="{{articleList}}" column="{{2}}" margin="{{0}}" radius="{{15}}" class="data-v-0354b366" bind:__l="__l"></liu-waterfall><view class="g-btn3-wrap data-v-0354b366"><view data-event-opts="{{[['tap',[['fetch',['$event']]]]]}}" class="g-btn3 data-v-0354b366" bindtap="__e">{{articleListMore.text}}</view></view></view></view>