<template>
    <view class="xilu">
        <view class="pt40 plr30 pb30">
            <view class="flex-box change_city mb30">
                <text class="pr10">{{currentCity?currentCity.name:''}}</text>
            </view>
            <view class="pt70 pb30 fs28 col6">热门城市</view>
            <view class="flex-box flex-wrap tc">
                <view class="city_item" @click="bindCity(index)" v-for="(item,index) in hotCities">{{item.name}}</view>
            </view>
        </view>
        <view class="list_box">
            <uni-indexed-list :options="list" :show-select="false" @click="bindClick" />
        </view>
    </view>
</template>

<script>
	const app = getApp();
    export default {
        data() {
            return {
				currentCity: null,
				hotCities:[],
                list: []
			};
        },
		onLoad() {
			//城市
			this.currentCity = this.$core.getCurrentCity();
			this.fetchList();
		},
        methods: {
			fetchList(){
				this.$core.get({url: 'xilutour.common/cities',loading:false,data:{},success: (ret) => {
						this.hotCities = ret.data.hot_cities;
						this.list = ret.data.cities;
					}
				})
			},
			//热门城市
			bindCity(index) {
				let city = this.hotCities[index];
				let area = {
					id: city.id,
					name: city.name
				}
				this.$core.setCurrentCity(area);
				uni.$emit(app.globalData.Event.CurrentCityChange2,this.$core.getCurrentCity());
				uni.navigateBack({})
			},
			//修改城市
            bindClick(e) {
                //console.log('点击item，返回数据' + JSON.stringify(e),e);
				let city = e.item;
				let area = {
					id: city.city.id,
					name: city.city.name
				}
				this.$core.setCurrentCity(area);
				uni.$emit(app.globalData.Event.CurrentCityChange2,this.$core.getCurrentCity());
				uni.navigateBack({})
            }
        }
    }
</script>

<style lang="less" scoped>
    .xilu {
        display: flex;
        width: 100vw;
        height: 100vh;
        flex-direction: column;

        .change_city::after {
            content: "";
            display: block;
            width: 0;
            height: 0;
            border-width: 14rpx 10rpx 0;
            border-color: #999 transparent transparent;
            border-style: solid;
            margin-top: 8rpx;
        }

        .list_box {
            position: relative;
            width: 100vw;
            flex: 1;
        }

        .city_item {
            width: 190rpx;
            height: 65rpx;
            line-height: 65rpx;
            background: #F5F6F7;
            border-radius: 33rpx;
            margin-right: 30rpx;
            margin-bottom: 30rpx;
            font-size: 26rpx;
            color: #333;
        }
    }
</style>