<template>
	<view class="xilu">
		<view class="container">
			<view class="xilu_withdrawal">
				<view class="flex-box mb40">
					<view class="fs30 col-10 flex-1">提现金额</view>
					<view class="fs28 col-89 mr10">可提现 </view>
					<view class="fs28 col-price">¥{{account.money}}</view>
				</view>
				<view class="ptb35 flex-box m-hairline--bottom mb50">
					<image src="/static/icon/icon_¥.png" mode="aspectFit" class="icon_rmb"></image>
					<input class="flex-1 fs30 col-3" type="digit" v-model="withdraw.money" placeholder="请输入提现金额" placeholder-class="col-89" />
				</view>
				<view class="pt50 fs28 col-5 mb30">提现说明</view>
				<view class="fs28 col-3 mb25"><text>{{apply.apply_rule}}</text></view>
				<view class="pt35 mt40">
					<view class="g-btn1" @click="formSubmit()">确定提现</view>
					<navigator class="nav_record" url="/pages/withdrawal_record/withdrawal_record" hover-class="none">提现记录</navigator>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var validate = require("../../xilu/validate.js");
	export default {
		data() {
			return {
				apply:{apply_rule:''},
				account:{
					id: 0,
					money: '0.00',
					total_money: '0.00',
					freeze_money: '0.00',
				},
				withdraw:{
					money:''
				}
			};
		},
		onLoad() {
			this.apply.apply_rule = getApp().globalData.config.apply_rule
			this.getAccount();
		},
		methods:{
			getAccount(){
				this.$core.post({url: 'xilutour.user/account',data: {},loading: false,success: ret => {
						this.account = ret.data;
					},fail: err => {
						console.log(err);
					}
				});
			},
			//提交
			formSubmit() {
			    let formData = this.withdraw;
				if(formData.money*100&&this.account.money*100<formData.money*100){
					uni.showToast({
						title:"提现金额大于可提现金额",
						icon:'none'
					});
					return false;
				}
			    var rule = [
					{name: 'money',nameChn: '金额',rules: ['require','gt:0'],errorMsg: {require: '提现金额错误',gt: '提现金额大于0'},},
					];
					
			    // 是否全部通过，返回Boolean
				if(!validate.check(formData,rule)){
					uni.showToast({title: validate.getError()[0],icon:'none'});
					return ;
				}
				this.$core.post({url:'xilutour.user/withdraw',data:formData,success:ret=>{
					this.getOpenerEventChannel().emit('withdrawSuccess',ret.data);
					uni.navigateBack({});
					uni.showToast({title: ret.msg,icon:'none'});
				}})
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		&_withdrawal {
			padding: 50rpx;
			background-color: #fff;

			.icon_rmb {
				margin-right: 20rpx;
				display: block;
				width: 24rpx;
				height: 30rpx;
			}
			.nav_record{
				margin-top: 45rpx;
				padding: 34rpx 0;
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #555555;
				line-height: 34rpx;
				text-align: center;
			}
		}

		.container {
			background: #F7F9FB;
		}

	}
</style>