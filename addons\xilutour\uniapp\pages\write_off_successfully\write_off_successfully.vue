<template>
	<view class="xilu">
		<view class="container">
			<image src="../../static/icon/icon_success.png" mode="aspectFill" class="img"></image>
			<view class="fs36 col-10 tc">核销成功！</view>
			<navigator class="g-btn1" url="/pages/personal_center/personal_center" open-type="switchTab" hover-class="none">去核销</navigator>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="less" scoped>
.xilu {
	.container{
		padding: 60rpx 75rpx;
	}
	.img{
		margin: 0 auto 30rpx;
		display: block;
		width: 200rpx;
		height: 190rpx;
	}
	.g-btn1{
		margin-top: 276rpx;
	}
}
</style>
