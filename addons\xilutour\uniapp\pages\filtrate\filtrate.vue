<template>
	<view class="xilu">
		<view class="page-foot flex-box flex-between">
			<view class="btn1" @click="bindReset()">清空</view>
			<view class="btn2" @click="bindSubmit()">确定</view>
		</view>
		<view class="container">
			<view v-if="type !== 'tour'">
				<view class="fs30 col-10 mb30">景点级别</view>
				<view class="xilu_filtrate flex-box flex-wrap">
					<view class="item" @click="bindChanceLevel(level.id)" :class="{active: level.id==query.level_id}" v-for="(level,index) in levels" :key="index">{{level.name}}</view>
				</view>
			</view>
			<view>
				<view class="fs30 col-10 mb30">标签</view>
				<view class="xilu_filtrate flex-box flex-wrap">
					<view class="item" @click="bindChangeTags(index)" :class="{active: tag.check}" v-for="(tag,index) in tags" :key="index">{{tag.name}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				type:'',
				levels:[],
				tags: [],
				selectTags:[],
				query:{
					level_id:0
				}
			};
		},
		onLoad(options) {
			this.type = options.type || '';
			let page = this;
			this.getOpenerEventChannel().on("searchTransform",function(query){
				if(query.tag_ids){
					page.selectTags = query.tag_ids.split(',');
				}
				page.query = query;
				page.$util.getTags(page.type,false).then(tags=>{
					for (let i = 0; i < tags.length; i++) {
						if (page.selectTags.indexOf(tags[i].id.toString()) > -1) {
							tags[i].check = true;
						}
					}
					page.tags = tags;
				})
			})
			if(this.type != 'tour'){
				this.$util.getSceneryLevel(false).then(levels=>{
					page.levels = levels;
				})
			}
			
		},
		methods:{
			//切换等级
			bindChanceLevel(id){
				this.query.level_id = id;
				this.$forceUpdate();
			},
			//选择标签
			bindChangeTags(firstIndex) {
				//判断是选中还是取消
				if (!this.tags[firstIndex].check) {
					//原来是true,则为取消
					// if (this.tags.length >= 5) {
					// 	uni.showToast({title: '最多选择五个',icon: 'error'});
					// 	return false;
					// }
				}
				this.tags[firstIndex].check = !this.tags[firstIndex].check;
				if (this.tags[firstIndex].check) {
					this.selectTags.push(this.tags[firstIndex].id);
				} else {
					for (let i = 0; i < this.selectTags.length; i++) {
						if (this.tags[firstIndex].id == this.selectTags[i]) {
							this.selectTags.splice(i, 1);
							break;
						}
					}
				}
				this.$forceUpdate()
			},
			
			bindSubmit(){
				let query = this.query;
				query.tag_ids = this.selectTags.length>0?this.selectTags.join(','):'';
				
				this.getOpenerEventChannel().emit("searchSuccess",query);
				uni.navigateBack()
			},
			bindReset(){
				this.query.level_id = 0;
				this.selectTags = [];
				for (let i = 0; i < this.tags.length; i++) {
					this.tags[i].check = false;
				}
			}
			
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		.page-foot {
			padding: 10rpx 85rpx;
			background-color: #FFF;

			.btn1 {
				width: 250rpx;
				height: 90rpx;
				border-radius: 30rpx;
				border: 2rpx solid var(--normal);
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: var(--normal);
				line-height: 88rpx;
				text-align: center;
			}

			.btn2 {
				width: 250rpx;
				height: 90rpx;
				border-radius: 30rpx;
				background: var(--normal);
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 90rpx;
				text-align: center;
			}
		}

		.container {
			padding: 30rpx 40rpx 160rpx !important;
		}

		&_filtrate {
			padding: 0 0 20rpx;

			.item {
				margin: 0 30rpx 30rpx 0;
				padding: 0 6rpx;
				width: 202rpx;
				height: 90rpx;
				background: #F7F9FB;
				border-radius: 20rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 90rpx;
				text-align: center;
			}

			.item:nth-of-type(3n) {
				margin: 0 0 30rpx;
			}

			.item.active {
				background: rgba(5, 185, 174, 0.1);
				border: 2rpx solid var(--normal);
				font-weight: 500;
				color: var(--normal);
			}
		}
	}
</style>