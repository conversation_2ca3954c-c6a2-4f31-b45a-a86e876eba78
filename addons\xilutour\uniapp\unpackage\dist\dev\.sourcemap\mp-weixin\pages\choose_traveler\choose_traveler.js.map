{"version": 3, "sources": ["uni-app:///pages/choose_traveler/choose_traveler.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/choose_traveler/choose_traveler.vue?989c", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/choose_traveler/choose_traveler.vue?0f05", "uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/choose_traveler/choose_traveler.vue?2ea2", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/choose_traveler/choose_traveler.vue?d324", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/choose_traveler/choose_traveler.vue?59a7", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/choose_traveler/choose_traveler.vue?3f0d"], "names": ["data", "tour", "tour_id", "buy_adult_count", "buy_child_count", "tour_date_id", "travelerList", "onLoad", "page", "methods", "fetch", "url", "success", "fail", "bindChoose", "childCount", "adultCount", "uni", "title", "icon", "traveler", "addTraveler", "events", "setSuccess", "bindConfirm", "lists", "content", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAmDA;EACAA;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;QACAC;MACA;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAAC;QAAAX;QAAAY;UACA;QACA;QACAC,0BAEA;MACA;IACA;IACA;IACAC;MACA;MACA;QAAAC;MACA;QACA;UACAC;QACA;UACAD;QACA;MACA;MACA;MACA;MACA;QACA;UACAE;YACAC;YACAC;UACA;UACA;QACA;UACAF;YACAC;YACAC;UACA;UACA;QACA;MACA;MACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MACAJ;QACAN;QACAW;UACAC;YACA;UACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;QACAR;UAAAC;UAAAC;QAAA;QACA;MACA;MACA;MACA;QACAF;UAAAC;UAAAQ;QAAA;QACA;MACA;MACA;MACAT;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnJA;AAAA;AAAA;AAAA;AAA2hD,CAAgB,45CAAG,EAAC,C;;;;;;;;;;;ACA/iD;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAU,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAw0B,CAAgB,wyBAAG,EAAC,C", "file": "pages/choose_traveler/choose_traveler.js", "sourcesContent": ["<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"page-foot flex-box flex-between\">\r\n\t\t\t<view class=\"btn1\" @click=\"addTraveler()\">新建出行人</view>\r\n\t\t\t<view class=\"btn2\" @click=\"bindConfirm()\">确定</view>\r\n\t\t</view>\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"xilu_traveler flex-box\" v-for=\"(item,index) in travelerList\" :key=\"index\">\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"flex-box mb30\">\r\n\t\t\t\t\t\t<view class=\"col-10 mr20\">{{item.username}}</view>\r\n\t\t\t\t\t\t<image v-if=\"item.gender == 1\" class=\"icon_gender\" src=\"/static/icon/icon_gender1.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t<image v-else class=\"icon_gender\" src=\"/static/icon/icon_gender2.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box mb20\">\r\n\t\t\t\t\t\t<view class=\"col-9 mr15\">身份证号</view>\r\n\t\t\t\t\t\t<view class=\"col-3 flex-1\">{{item.idcard}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box\">\r\n\t\t\t\t\t\t<view class=\"col-9 mr15\">手机号码</view>\r\n\t\t\t\t\t\t<view class=\"col-3 flex-1\">{{item.mobile}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\n\t\t\t\t<image @click=\"bindChoose(index)\" v-if=\"item.checked\" class=\"icon_check\" src=\"/static/icon/icon_checkon.png\" mode=\"aspectFit\"></image>\n\t\t\t\t<image @click=\"bindChoose(index)\" v-else class=\"icon_check\" src=\"/static/icon/icon_check.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- <view class=\"xilu_traveler flex-box\">\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"flex-box mb30\">\r\n\t\t\t\t\t\t<view class=\"col-10 mr20\">王一一</view>\r\n\t\t\t\t\t\t<image class=\"icon_gender\" src=\"/static/icon/icon_gender2.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box mb20\">\r\n\t\t\t\t\t\t<view class=\"col-9 mr15\">身份证号</view>\r\n\t\t\t\t\t\t<view class=\"col-3 flex-1\">211224188801019225</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box\">\r\n\t\t\t\t\t\t<view class=\"col-9 mr15\">手机号码</view>\r\n\t\t\t\t\t\t<view class=\"col-3 flex-1\">13501010201</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<image class=\"icon_check\" src=\"/static/icon/icon_check.png\" mode=\"aspectFit\"></image>\r\n\t\t\t</view> -->\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttour:{\n\t\t\t\t\ttour_id:0,\n\t\t\t\t\tbuy_adult_count:0,\n\t\t\t\t\tbuy_child_count:0,\n\t\t\t\t\ttour_date_id:0,\n\t\t\t\t},\n\t\t\t\ttravelerList:[],\r\n\t\t\t};\r\n\t\t},\n\t\tonLoad() {\n\t\t\tlet page = this;\n\t\t\tthis.getOpenerEventChannel().on(\"travelTransfor\",function(data){\n\t\t\t\tif(Object.keys(data).length>0){\n\t\t\t\t\tpage.tour = data;\n\t\t\t\t}\n\t\t\t})\n\t\t\tthis.fetch();\n\t\t},\n\t\tmethods:{\n\t\t\tfetch() {\n\t\t\t\tthis.$core.get({url: 'xilutour.traveler/lists',data: this.tour,success: ret => {\n\t\t\t\t\t\tthis.travelerList = ret.data;\n\t\t\t\t\t},\n\t\t\t\t\tfail: err => {\n\t\t\t\t\t\t\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t//选择出行\n\t\t\tbindChoose(index){\n\t\t\t\tlet travelerList = this.travelerList;\n\t\t\t\tlet adultCount = 0,childCount = 0;\n\t\t\t\tfor(let i=0;i<travelerList.length;i++){\n\t\t\t\t\tif(travelerList[i].adult_type == 1 && travelerList[i].checked){\n\t\t\t\t\t\tadultCount+=1;\n\t\t\t\t\t}else if(travelerList[i].adult_type == 2 && travelerList[i].checked){\n\t\t\t\t\t\tchildCount += 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tlet tour = this.tour;\n\t\t\t\tlet traveler = travelerList[index];\n\t\t\t\tif(!traveler.checked){\n\t\t\t\t\tif(traveler.adult_type==1 && tour.buy_adult_count <= adultCount){\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: \"成人数量上限\",\n\t\t\t\t\t\t\ticon:'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}else if(traveler.adult_type==2 && tour.buy_child_count <= childCount){\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: \"儿童数量上限\",\n\t\t\t\t\t\t\ticon:'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ttraveler.checked = !traveler.checked;\n\t\t\t\tthis.travelerList[index] = traveler;\n\t\t\t\tthis.$forceUpdate();\n\t\t\t},\n\t\t\t//添加出行\n\t\t\taddTraveler(){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/new_traveler/new_traveler',\n\t\t\t\t\tevents:{\n\t\t\t\t\t\tsetSuccess: data=>{\n\t\t\t\t\t\t\tthis.fetch();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\tbindConfirm(){\n\t\t\t\tlet travelerList = this.travelerList;\n\t\t\t\tlet lists = [];\n\t\t\t\tfor(let i=0;i<travelerList.length;i++){\n\t\t\t\t\tif(travelerList[i].checked){\n\t\t\t\t\t\tlists.push(travelerList[i]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(lists.length<=0){\n\t\t\t\t\tuni.showToast({title: \"请选择出行人\",icon:'none'});\n\t\t\t\t\treturn ;\n\t\t\t\t}\n\t\t\t\tlet tour = this.tour;\n\t\t\t\tif(lists.length<(tour.buy_adult_count+tour.buy_child_count)){\n\t\t\t\t\tuni.showModal({title: \"提示\",content:'请选择'+tour.buy_adult_count+'名成人，'+tour.buy_child_count+'名儿童'});\n\t\t\t\t\treturn ;\n\t\t\t\t}\n\t\t\t\tthis.getOpenerEventChannel().emit(\"chooseSuccess\",lists);\n\t\t\t\tuni.navigateBack({})\n\t\t\t}\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.xilu {\r\n\t\t.page-foot {\r\n\t\t\tpadding: 20rpx 65rpx;\r\n\t\t\tbackground-color: #FFF;\r\n\r\n\t\t\t.btn1 {\r\n\t\t\t\twidth: 290rpx;\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\tborder: 2rpx solid var(--normal);\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: var(--normal);\r\n\t\t\t\tline-height: 88rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\r\n\t\t\t.btn2 {\r\n\t\t\t\twidth: 290rpx;\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\tbackground: var(--normal);\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tline-height: 90rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.container {\r\n\t\t\tpadding: 30rpx 40rpx 160rpx !important;\r\n\t\t}\r\n\r\n\t\t&_traveler {\r\n\t\t\tmargin: 0 0 30rpx;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\twidth: 670rpx;\r\n\t\t\theight: 200rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tline-height: 32rpx;\r\n\r\n\t\t\t.icon_gender {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 30rpx;\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.icon_check {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./choose_traveler.vue?vue&type=style&index=0&id=64cfede6&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./choose_traveler.vue?vue&type=style&index=0&id=64cfede6&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341183\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/choose_traveler/choose_traveler.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./choose_traveler.vue?vue&type=template&id=64cfede6&scoped=true&\"\nvar renderjs\nimport script from \"./choose_traveler.vue?vue&type=script&lang=js&\"\nexport * from \"./choose_traveler.vue?vue&type=script&lang=js&\"\nimport style0 from \"./choose_traveler.vue?vue&type=style&index=0&id=64cfede6&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"64cfede6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/choose_traveler/choose_traveler.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./choose_traveler.vue?vue&type=template&id=64cfede6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./choose_traveler.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./choose_traveler.vue?vue&type=script&lang=js&\""], "sourceRoot": ""}