(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/landscape_detail/landscape_detail"],{

/***/ 78:
/*!******************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/main.js?{"page":"pages%2Flandscape_detail%2Flandscape_detail"} ***!
  \******************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 3);
__webpack_require__(/*! uni-pages */ 25);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 24));
var _landscape_detail = _interopRequireDefault(__webpack_require__(/*! ./pages/landscape_detail/landscape_detail.vue */ 79));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_landscape_detail.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["createPage"]))

/***/ }),

/***/ 79:
/*!***********************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue ***!
  \***********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _landscape_detail_vue_vue_type_template_id_740743b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./landscape_detail.vue?vue&type=template&id=740743b4&scoped=true& */ 80);
/* harmony import */ var _landscape_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./landscape_detail.vue?vue&type=script&lang=js& */ 82);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _landscape_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _landscape_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _landscape_detail_vue_vue_type_style_index_0_id_740743b4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./landscape_detail.vue?vue&type=style&index=0&id=740743b4&lang=less&scoped=true& */ 84);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 31);

var renderjs





/* normalize component */

var component = Object(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _landscape_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _landscape_detail_vue_vue_type_template_id_740743b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _landscape_detail_vue_vue_type_template_id_740743b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "740743b4",
  null,
  false,
  _landscape_detail_vue_vue_type_template_id_740743b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/landscape_detail/landscape_detail.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 80:
/*!******************************************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue?vue&type=template&id=740743b4&scoped=true& ***!
  \******************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_template_id_740743b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_detail.vue?vue&type=template&id=740743b4&scoped=true& */ 81);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_template_id_740743b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_template_id_740743b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_template_id_740743b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_template_id_740743b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 81:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue?vue&type=template&id=740743b4&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 397))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 82:
/*!************************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_detail.vue?vue&type=script&lang=js& */ 83);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 83:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      barTitle: '风景详情',
      statusBarHeight: 20,
      swiperCurrent: 0,
      tabIdx: 1,
      sceneryId: 0,
      scenery: {
        work_time: '',
        address: '',
        name: '',
        view_count: 0,
        worktime: '',
        tags: [],
        projects: [],
        coupons: []
      },
      total: 0,
      commentList: [],
      commentListMore: {
        page: 1
      },
      setCol: false,
      scrollLeft: 0
    };
  },
  onLoad: function onLoad(options) {
    this.statusBarHeight = getApp().globalData.statusBarHeight;
    this.sceneryId = options.id;
    this.fetchDetail();
    this.fetch();
  },
  onReachBottom: function onReachBottom() {
    if (this.tabIdx == 5) {
      this.fetch();
    }
  },
  onShareAppMessage: function onShareAppMessage(e) {
    var userinfo = this.$core.getUserinfo();
    var path = '/pages/landscape_detail/landscape_detail?id=' + this.sceneryId;
    if (userinfo) {
      path += '&pid=' + userinfo.pid;
    }
    return {
      title: this.scenery.name,
      path: path
      //imageUrl: this.scenery.thumb_image_text
    };
  },
  onShareTimeline: function onShareTimeline() {
    var userinfo = this.$core.getUserinfo();
    var query = "id=" + this.sceneryId;
    if (userinfo) {
      query += '&pid=' + userinfo.pid;
    }
    return {
      title: this.scenery.name,
      query: query
    };
  },
  onPageScroll: function onPageScroll(e) {
    if (e.scrollTop > 350) {
      this.setCol = true;
    } else {
      this.setCol = false;
    }
  },
  methods: {
    swiperChange: function swiperChange(e) {
      this.swiperCurrent = e.detail.current;
    },
    tabClick: function tabClick(i) {
      this.tabIdx = i;
    },
    navBack: function navBack() {
      uni.navigateBack();
    },
    fetchDetail: function fetchDetail() {
      var _this = this;
      this.$core.post({
        url: 'xilutour.scenery/detail',
        data: {
          scenery_id: this.sceneryId
        },
        loading: false,
        success: function success(ret) {
          ret.data.content = _this.$core.richTextnew(ret.data.content);
          ret.data.statement = _this.$core.richTextnew(ret.data.statement);
          ret.data.travel_know = _this.$core.richTextnew(ret.data.travel_know);
          _this.scenery = ret.data;
          _this.barTitle = ret.data.name;
        },
        fail: function fail(err) {
          console.log(err);
          uni.showModal({
            title: '提示',
            content: err.msg,
            showCancel: false,
            complete: function complete() {
              uni.navigateBack();
            }
          });
          return false;
        }
      });
    },
    fetch: function fetch() {
      var _this2 = this;
      var query = {
        scenery_id: this.sceneryId
      };
      query.pagesize = 10;
      this.$util.fetch(this, 'xilutour.scenery_comment/lists', query, 'commentListMore', 'commentList', 'data', function (data) {
        _this2.total = data.total;
      });
    },
    //拨打电话
    callphone: function callphone() {
      var tel = this.scenery.tel;
      uni.makePhoneCall({
        phoneNumber: tel
      });
    },
    //导航
    bindOpenLocation: function bindOpenLocation() {
      var scenery = this.scenery;
      var address = (scenery.city ? scenery.city.name : '') + (scenery.district ? scenery.district.name : '') + scenery.address;
      uni.openLocation({
        latitude: Number(scenery.lat),
        longitude: Number(scenery.lng),
        name: scenery.name,
        address: address
      });
    },
    //收藏
    toggleCollection: function toggleCollection() {
      var _this3 = this;
      if (!this.$core.getUserinfo(true)) {
        return;
      }
      this.$core.post({
        url: 'xilutour.scenery/toggle_collection',
        data: {
          scenery_id: this.scenery.id
        },
        success: function success(ret) {
          _this3.scenery.is_collection_count = ret.data.is_collection_count;
        },
        fail: function fail(err) {}
      });
    },
    //领取优惠券
    bindReceive: function bindReceive(index) {
      var _this4 = this;
      var coupons = this.scenery.coupons;
      var coupon = coupons[index];
      if (!this.$core.getUserinfo(true)) {
        return;
      }
      this.$core.post({
        url: 'xilutour.coupon/receive',
        data: {
          coupon_id: coupon.id
        },
        success: function success(ret) {
          uni.showToast({
            title: '领取成功'
          });
          coupons[index].is_receive_count = 1;
          _this4.scenery.coupons = coupons;
        },
        fail: function fail(err) {
          uni.showModal({
            title: '提示',
            content: err.msg
          });
          return false;
        }
      });
    },
    bindBuy: function bindBuy() {
      var _this5 = this;
      this.tabIdx = 1;
      this.scrollLeft = 0;
      var view = uni.createSelectorQuery().in(this).select(".g_tab");
      setTimeout(function () {
        uni.createSelectorQuery().selectViewport().scrollOffset(function (res) {
          console.log("竖直滚动位置" + res.scrollTop);
          view.boundingClientRect(function (data) {
            console.log("节点离页面顶部的距离为" + data.top);
            uni.pageScrollTo({
              scrollTop: res.scrollTop + data.top - _this5.statusBarHeight - 45,
              duration: 500
            });
          }).exec();
        }).exec();
      }, 500);
    },
    scrollFunc: function scrollFunc(e) {
      this.scrollLeft = e.detail.scrollLeft;
      console.log(e);
    },
    //预定
    bindAppoint: function bindAppoint(projectId) {
      if (!this.$core.getUserinfo(true)) {
        return;
      }
      uni.navigateTo({
        url: '/pages/landscape_pay/landscape_pay?scenery_id=' + this.scenery.id + '&project_id=' + projectId
      });
    },
    // 打开优惠券弹窗
    couponPopOpen: function couponPopOpen() {
      this.$refs.couponPopup.open();
    },
    // 关闭优惠券弹窗
    couponPopClose: function couponPopClose() {
      this.$refs.couponPopup.close();
    },
    bindPrev: function bindPrev(index, index2) {
      uni.previewImage({
        urls: this.commentList[index].images_text,
        current: index2
      });
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"]))

/***/ }),

/***/ 84:
/*!*********************************************************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue?vue&type=style&index=0&id=740743b4&lang=less&scoped=true& ***!
  \*********************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_style_index_0_id_740743b4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_detail.vue?vue&type=style&index=0&id=740743b4&lang=less&scoped=true& */ 85);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_style_index_0_id_740743b4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_style_index_0_id_740743b4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_style_index_0_id_740743b4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_style_index_0_id_740743b4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_landscape_detail_vue_vue_type_style_index_0_id_740743b4_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 85:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-2!./node_modules/postcss-loader/src??ref--10-oneOf-1-3!./node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue?vue&type=style&index=0&id=740743b4&lang=less&scoped=true& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[78,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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