<?php
/**
 * 小红书access_token缓存功能测试脚本
 * 
 * 使用方法：
 * php scripts/test_xhs_cache.php [action]
 * 
 * action可选值：
 * - info: 查看缓存信息
 * - clear: 清除缓存
 * - refresh: 刷新缓存
 * - test: 执行完整测试
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 获取项目根目录
$rootPath = dirname(__DIR__);

// 引入框架
define('APP_PATH', $rootPath . '/application/');
define('ROOT_PATH', $rootPath . '/');
define('EXTEND_PATH', $rootPath . '/extend/');
define('VENDOR_PATH', $rootPath . '/vendor/');
define('RUNTIME_PATH', $rootPath . '/runtime/');
define('LOG_PATH', RUNTIME_PATH . 'log/');
define('CACHE_PATH', RUNTIME_PATH . 'cache/');
define('TEMP_PATH', RUNTIME_PATH . 'temp/');
define('CONF_PATH', $rootPath . '/config/');

require_once $rootPath . '/thinkphp/base.php';

// 获取命令行参数
$action = isset($argv[1]) ? $argv[1] : 'test';

echo "=== 小红书access_token缓存功能测试 ===\n";
echo "测试动作: {$action}\n\n";

try {
    // 初始化应用
    \think\App::initCommon();
    
    // 创建小红书控制器实例
    $xhs = new \addons\xilutour\controller\XiaohongshuMini();
    
    switch ($action) {
        case 'info':
            testCacheInfo($xhs);
            break;
            
        case 'clear':
            testCacheClear($xhs);
            break;
            
        case 'refresh':
            testCacheRefresh($xhs);
            break;
            
        case 'test':
            runFullTest($xhs);
            break;
            
        default:
            echo "❌ 不支持的操作: {$action}\n";
            echo "支持的操作: info, clear, refresh, test\n";
            exit(1);
    }
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * 测试缓存信息获取
 */
function testCacheInfo($xhs)
{
    echo "1. 获取缓存信息...\n";
    $info = $xhs->getCacheInfo();
    
    echo "   缓存Key: " . $info['cache_key'] . "\n";
    echo "   是否有缓存: " . ($info['has_cache'] ? '是' : '否') . "\n";
    echo "   Token预览: " . ($info['token_preview'] ?: '无') . "\n";
    echo "   AppID: " . $info['app_id'] . "\n";
    echo "✅ 缓存信息获取成功\n";
}

/**
 * 测试缓存清除
 */
function testCacheClear($xhs)
{
    echo "1. 清除缓存...\n";
    $result = $xhs->clearAccessTokenCache();
    echo "   清除结果: " . ($result ? '成功' : '失败') . "\n";
    
    echo "2. 验证缓存已清除...\n";
    $info = $xhs->getCacheInfo();
    echo "   是否有缓存: " . ($info['has_cache'] ? '是' : '否') . "\n";
    
    if (!$info['has_cache']) {
        echo "✅ 缓存清除成功\n";
    } else {
        echo "❌ 缓存清除失败\n";
    }
}

/**
 * 测试缓存刷新
 */
function testCacheRefresh($xhs)
{
    echo "1. 清除现有缓存...\n";
    $xhs->clearAccessTokenCache();
    
    echo "2. 获取新的access_token...\n";
    try {
        $token = $xhs->getAccessToken();
        echo "   新Token预览: " . substr($token, 0, 10) . "...\n";
        
        echo "3. 验证缓存已生成...\n";
        $info = $xhs->getCacheInfo();
        echo "   是否有缓存: " . ($info['has_cache'] ? '是' : '否') . "\n";
        
        if ($info['has_cache']) {
            echo "✅ 缓存刷新成功\n";
        } else {
            echo "❌ 缓存刷新失败\n";
        }
    } catch (Exception $e) {
        echo "❌ 获取access_token失败: " . $e->getMessage() . "\n";
        echo "   请检查小红书配置是否正确\n";
    }
}

/**
 * 运行完整测试
 */
function runFullTest($xhs)
{
    echo "开始完整测试流程...\n\n";
    
    // 测试1：查看初始状态
    echo "=== 测试1：查看初始缓存状态 ===\n";
    testCacheInfo($xhs);
    echo "\n";
    
    // 测试2：清除缓存
    echo "=== 测试2：清除缓存 ===\n";
    testCacheClear($xhs);
    echo "\n";
    
    // 测试3：首次获取token（应该从API获取）
    echo "=== 测试3：首次获取token ===\n";
    try {
        $startTime = microtime(true);
        $token1 = $xhs->getAccessToken();
        $time1 = round((microtime(true) - $startTime) * 1000, 2);
        echo "   首次获取耗时: {$time1}ms\n";
        echo "   Token预览: " . substr($token1, 0, 10) . "...\n";
        echo "✅ 首次获取成功\n";
    } catch (Exception $e) {
        echo "❌ 首次获取失败: " . $e->getMessage() . "\n";
        return;
    }
    echo "\n";
    
    // 测试4：再次获取token（应该从缓存获取）
    echo "=== 测试4：再次获取token（测试缓存） ===\n";
    try {
        $startTime = microtime(true);
        $token2 = $xhs->getAccessToken();
        $time2 = round((microtime(true) - $startTime) * 1000, 2);
        echo "   缓存获取耗时: {$time2}ms\n";
        echo "   Token预览: " . substr($token2, 0, 10) . "...\n";
        echo "   Token一致性: " . ($token1 === $token2 ? '一致' : '不一致') . "\n";
        echo "   性能提升: " . round(($time1 - $time2) / $time1 * 100, 1) . "%\n";
        echo "✅ 缓存获取成功\n";
    } catch (Exception $e) {
        echo "❌ 缓存获取失败: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 测试5：最终状态检查
    echo "=== 测试5：最终状态检查 ===\n";
    testCacheInfo($xhs);
    echo "\n";
    
    echo "🎉 完整测试流程结束\n";
    echo "\n";
    echo "📊 测试总结:\n";
    echo "- 如果缓存获取耗时明显小于首次获取，说明缓存机制正常工作\n";
    echo "- 如果两次获取的Token一致，说明缓存数据正确\n";
    echo "- 建议性能提升应该在80%以上\n";
}

echo "\n=== 测试完成 ===\n";
?>
