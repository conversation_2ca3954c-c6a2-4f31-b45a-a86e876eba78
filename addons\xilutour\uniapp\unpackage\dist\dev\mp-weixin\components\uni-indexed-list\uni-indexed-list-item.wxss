@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-indexed-list__list.data-v-6a7f6b8c {
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}
.uni-indexed-list__item.data-v-6a7f6b8c {
  font-size: 14px;
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.uni-indexed-list__item-container.data-v-6a7f6b8c {
  padding-left: 15px;
  flex: 1;
  position: relative;
  display: flex;
  box-sizing: border-box;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.uni-indexed-list__item-border.data-v-6a7f6b8c {
  flex: 1;
  position: relative;
  display: flex;
  box-sizing: border-box;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  padding-left: 0;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-bottom-color: #DEDEDE;
}
.uni-indexed-list__item-border--last.data-v-6a7f6b8c {
  border-bottom-width: 0px;
}
.uni-indexed-list__item-content.data-v-6a7f6b8c {
  flex: 1;
  font-size: 14px;
  color: #191919;
}
.uni-indexed-list.data-v-6a7f6b8c {
  display: flex;
  flex-direction: row;
}
.uni-indexed-list__title-wrapper.data-v-6a7f6b8c {
  display: flex;
  width: 100%;
  background-color: #fff;
}
.uni-indexed-list__title.data-v-6a7f6b8c {
  padding-left: 30rpx;
  line-height: 24px;
  font-size: 16px;
  font-weight: 500;
}

