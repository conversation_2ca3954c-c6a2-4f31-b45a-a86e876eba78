# 小红书getUserProfile使用指南

## 问题背景

在实现小红书登录功能时，发现"获取用户信息失败"的问题。根据小红书官方文档，需要使用正确的方式调用`xhs.getUserProfile`方法。

## 官方Demo参考

根据小红书官方提供的demo，正确的调用方式如下：

```javascript
function getUserProfile() {
  return new Promise((resolve, reject) => {
    xhs.getUserProfile({
      success: (res) => {
        console.log('获取用户信息成功:', res);
        resolve(res.userInfo); // 返回用户信息
      },
      fail: (err) => {
        console.error('获取用户信息失败:', err);
        reject(err); // 返回错误信息
      },
      complete: () => {
        console.log('获取用户信息操作完成');
      }
    });
  });
}

// 调用示例
getUserProfile()
  .then((userInfo) => {
    console.log('用户信息:');
    console.log('昵称:', userInfo.nickName);
    console.log('头像 URL:', userInfo.avatarUrl);
    console.log('性别:', userInfo.gender === 1 ? '男' : userInfo.gender === 2 ? '女' : '未知');
  })
  .catch((err) => {
    console.error('操作失败:', err);
  });
```

## 关键要点

### 1. 使用Promise封装
- 小红书的API是回调形式，建议封装成Promise便于使用
- 提供`success`、`fail`、`complete`三个回调函数

### 2. 返回数据结构
```javascript
// res对象结构
{
  userInfo: {
    nickName: "用户昵称",
    avatarUrl: "用户头像URL",
    gender: 0, // 0=未知, 1=男, 2=女
    // 可能还有其他字段
  }
}
```

### 3. 错误处理
- 必须提供`fail`回调处理错误情况
- 常见错误：用户拒绝授权、网络问题、API调用限制等

## 代码实现

### 1. 在core.js中的实现

```javascript
// 小红书获取用户信息（Promise方式）
xhsGetUserProfile() {
    return new Promise((resolve, reject) => {
        xhs.getUserProfile({
            success: (res) => {
                console.log('获取用户信息成功:', res);
                resolve(res.userInfo); // 返回用户信息
            },
            fail: (err) => {
                console.error('获取用户信息失败:', err);
                reject(err); // 返回错误信息
            },
            complete: () => {
                console.log('获取用户信息操作完成');
            }
        });
    });
},

// 小红书登录流程
xhsLogin(cb) {
    let that = this;
    uni.login({
        provider: 'xiaohongshu',
        success: (auth) => {
            let code = auth.code;
            
            // 使用Promise方式获取用户信息
            that.xhsGetUserProfile()
                .then((userInfo) => {
                    // 调用登录接口
                    that.post({
                        url: 'xilutour.user/xhs_login_with_profile',
                        data: {
                            code: code,
                            userInfo: userInfo,
                            platform: 'xhs'
                        },
                        success: (ret) => {
                            // 登录成功处理
                            if(typeof cb == 'function'){
                                cb(ret.data.userinfo);
                            }
                        }
                    });
                })
                .catch((err) => {
                    console.error('获取用户信息失败:', err);
                    uni.showToast({
                        title: '获取用户信息失败，请重试',
                        icon: 'none'
                    });
                });
        }
    });
}
```

### 2. 在登录页面中的使用

```javascript
// 在登录页面的onLoad中调用
onLoad() {
    // #ifdef MP-XHS
    this.$core.xhsLogin(function(userinfo) {
        // 登录成功处理
        getApp().globalData.userinfo = userinfo;
        uni.navigateBack({});
        uni.$emit("user_update", {});
        uni.showToast({
            title: '登录成功',
        });
    });
    // #endif
}
```

## 调试和测试

### 1. 测试页面
创建了专门的测试页面：`pages/test/xhs-test.vue`

访问方式：
```javascript
uni.navigateTo({
    url: '/pages/test/xhs-test'
});
```

### 2. 调试步骤

1. **检查环境**
   ```javascript
   // 确保在小红书小程序环境中运行
   // #ifdef MP-XHS
   console.log('当前运行在小红书小程序环境');
   // #endif
   ```

2. **检查xhs对象**
   ```javascript
   if (typeof xhs === 'undefined') {
       console.error('xhs对象不存在');
       return;
   }
   ```

3. **检查getUserProfile方法**
   ```javascript
   if (typeof xhs.getUserProfile !== 'function') {
       console.error('getUserProfile方法不存在');
       return;
   }
   ```

4. **测试调用**
   ```javascript
   xhs.getUserProfile({
       success: (res) => {
           console.log('成功:', res);
       },
       fail: (err) => {
           console.error('失败:', err);
       }
   });
   ```

### 3. 常见问题排查

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| xhs对象不存在 | 不在小红书小程序环境 | 确保在小红书开发者工具中运行 |
| getUserProfile方法不存在 | 小红书SDK版本问题 | 更新小红书开发者工具 |
| 用户信息获取失败 | 用户拒绝授权 | 引导用户重新授权 |
| 网络请求失败 | 网络问题或API限制 | 检查网络连接和API配额 |

## 最佳实践

### 1. 错误处理
```javascript
that.xhsGetUserProfile()
    .then((userInfo) => {
        // 成功处理
    })
    .catch((err) => {
        // 错误处理
        let errorMsg = '获取用户信息失败';
        if (err && err.errMsg) {
            errorMsg += ': ' + err.errMsg;
        }
        uni.showToast({
            title: errorMsg,
            icon: 'none'
        });
    });
```

### 2. 用户体验优化
```javascript
// 显示加载状态
uni.showLoading({
    title: '获取用户信息中...'
});

that.xhsGetUserProfile()
    .then((userInfo) => {
        uni.hideLoading();
        // 处理成功
    })
    .catch((err) => {
        uni.hideLoading();
        // 处理失败
    });
```

### 3. 数据验证
```javascript
that.xhsGetUserProfile()
    .then((userInfo) => {
        // 验证必要字段
        if (!userInfo || !userInfo.nickName) {
            throw new Error('用户信息不完整');
        }
        
        // 处理用户信息
        console.log('昵称:', userInfo.nickName);
        console.log('头像:', userInfo.avatarUrl);
    })
    .catch((err) => {
        console.error('用户信息处理失败:', err);
    });
```

## 注意事项

1. **权限要求**
   - 需要在小红书开放平台配置相应权限
   - 确保小程序有获取用户信息的权限

2. **调用时机**
   - 建议在用户主动触发登录时调用
   - 避免在页面加载时自动调用

3. **数据安全**
   - 不要在日志中输出完整的用户信息
   - 注意保护用户隐私数据

4. **兼容性**
   - 确保在不同版本的小红书客户端中测试
   - 做好降级处理

## 测试验证

1. **在小红书开发者工具中测试**
2. **使用测试页面进行功能验证**
3. **检查控制台日志输出**
4. **验证用户信息是否正确获取**

通过以上方式，应该能够解决"获取用户信息失败"的问题，确保小红书登录功能正常工作。
