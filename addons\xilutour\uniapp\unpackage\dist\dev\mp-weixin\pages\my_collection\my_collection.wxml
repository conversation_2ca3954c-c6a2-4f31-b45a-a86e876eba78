<view class="xilu data-v-594b592a"><view class="page-head bg-white data-v-594b592a"><view class="g_tab data-v-594b592a"><view data-event-opts="{{[['tap',[['tabClick',[1]]]]]}}" class="{{['item','data-v-594b592a',(tabIdx==1)?'active':'']}}" bindtap="__e">景点</view><view data-event-opts="{{[['tap',[['tabClick',[2]]]]]}}" class="{{['item','data-v-594b592a',(tabIdx==2)?'active':'']}}" bindtap="__e">路线</view></view></view><view class="container data-v-594b592a"><block wx:if="{{tabIdx==1}}"><view class="g_landscape_list data-v-594b592a"><scenery-list vue-id="5fbf65c8-1" sceneryList="{{sceneryList}}" class="data-v-594b592a" bind:__l="__l"></scenery-list><view class="g-btn3-wrap data-v-594b592a"><view data-event-opts="{{[['tap',[['fetchScenery',['$event']]]]]}}" class="g-btn3 data-v-594b592a" bindtap="__e">{{sceneryListMore.text}}</view></view></view></block><block wx:if="{{tabIdx==2}}"><view class="g_travel_list data-v-594b592a"><tour-list vue-id="5fbf65c8-2" tourList="{{tourList}}" class="data-v-594b592a" bind:__l="__l"></tour-list><view class="g-btn3-wrap data-v-594b592a"><view data-event-opts="{{[['tap',[['fetch',['$event']]]]]}}" class="g-btn3 data-v-594b592a" bindtap="__e">{{tourListMore.text}}</view></view></view></block></view></view>