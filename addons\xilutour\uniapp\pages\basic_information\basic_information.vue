<template>
	<view class="xilu">
		<view class="page-foot" @click="bindSave()">
			<view class="g-btn1">确定</view>
		</view>
		<view class="container">
			<view class="xilu_header" @click="chooseImage()">
				<image :src="userinfo.avatar" mode="aspectFill"></image>
				<view class="mask">
					<view>更换</view>
					<view>头像</view>
				</view>
			</view>
			
			<view class="g_input_box flex-box mb30">
				<view class="fs30 col-5">姓名</view>
				<input class="flex-1 tr fs30 col-10" type="text" placeholder="请输入姓名" placeholder-class="col-10" v-model="userinfo.nickname" />
			</view>
			<view class="g_input_box flex-box mb30">
				<view class="fs30 col-5 flex-1">性别</view>
				<view class="flex-box flex-between fs30 col-10">
						<view class="flex-box pr25" @click="bindChangeGender(index)" v-for="(item,index) in genderList" :key="index">
							<image v-if="item.checked" class="icon_check" src="/static/icon/icon_checkon.png" mode="aspectFit"></image>
							<image v-else class="icon_check" src="/static/icon/icon_check.png" mode="aspectFit"></image>
							<view>{{item.name}}</view>
						</view>
					<!-- <view class="flex-box pl25">
						<image class="icon_check" src="/static/icon/icon_check.png" mode="aspectFit"></image>
						<view>女</view>
					</view> -->
				</view>
			</view>
			<view class="g_input_box flex-box mb30">
				<view class="fs30 col-5">手机号码</view>
				<input class="flex-1 tr fs30 col-10" type="number" disabled="true" placeholder="请输入手机号码" placeholder-class="col-10" v-model="userinfo.mobile"/>
			</view>

		</view>
	</view>
</template>

<script>
	var validate = require("../../xilu/validate.js");
	export default {
		data() {
			return {
				genderList:[
					{id:0,name:'女',checked:false},
					{id:1,name:'男',checked:false}
				],
				userinfo: {
				    avatar: '',
				    nickname: '',
				    mobile: ''
				},
			};
		},
		onLoad() {
			this.getUserinfo()
		},
		methods:{
			getUserinfo() {
			    this.$core.get({
			        url: 'xilutour.user/profile',
			        data: {},
			        loading: false,
			        success: ret => {
						this.genderList[ret.data.gender].checked = true;
			            this.userinfo = ret.data;
			        },
			        fail: err => {
			            console.log(err);
			        }
			    });
			},
			chooseImage() {
				let that = this;
			    uni.chooseImage({
			      count: 1,
			      success: res => {
			        let file = res.tempFiles[0];
					// #ifdef H5
					that.$core.uploadFileH5({
					  filePath: file.path,
					  success: (ret, response) => {
					    that.userinfo.avatar = ret.data.url;
					  }
					});
					//#endif
					//#ifdef MP-WEIXIN
			        that.$core.uploadFile({
			          filePath: file.path,
			          success: (ret, response) => {
			            that.userinfo.avatar = ret.data.url;
			          }
			        });
					//#endif
			      }
			    });
			},
			bindChangeGender(index){
				for(let i=0;i<this.genderList.length;i++){
					if(i==index){
						this.genderList[i].checked = true;
					}else{
						this.genderList[i].checked = false;
					}
				}
				this.userinfo.gender = index;
			},
			
			//提交
			bindSave() {
			    let formData = {
			        avatar: this.userinfo.avatar,
			        nickname: this.userinfo.nickname,
					gender: this.userinfo.gender
			    };
			
			    var rule = [{
			            name: 'avatar',
			            nameChn: '头像',
			            rules: ['require'],
			            errorMsg: {
			                require: '请上传头像'
			            }
			        },
			        {
			            name: 'nickname',
			            nameChn: '昵称',
			            rules: ['require'],
			            errorMsg: {
			                require: '请填写昵称'
			            }
			        }
			    ];
			    // 是否全部通过，返回Boolean
			
			    if (!validate.check(formData, rule)) {
			        uni.showToast({
			            title: validate.getError()[0],
			            icon: 'none'
			        });
			        return;
			    }
			    this.$core.post({url: 'xilutour.user/profile',data: formData,success: ret => {
			        let userinfo = this.$core.getUserinfo();
			        userinfo.avatar = ret.data.avatar;
			        userinfo.nickname = ret.data.nickname;
			        uni.$emit("user_update", {})
			        uni.navigateBack({});
			        uni.showToast({
			            title: '提交成功',
			            icon: 'none'
			        });
			    }})
			}
			
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		&_header {
			position: relative;
			margin: 0 auto 40rpx;
			width: 208rpx;
			height: 208rpx;
			border-radius: 50%;

			image {
				display: block;
				width: 208rpx;
				height: 208rpx;
				border-radius: 50%;
			}

			.mask {
				position: absolute;
				top: 0;
				right: 0;
				left: 0;
				bottom: 0;
				padding: 60rpx;
				width: 208rpx;
				height: 208rpx;
				border-radius: 50%;
				background: rgba(0, 0, 0, 0.4);
				font-size: 34rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 44rpx;
				text-align: center;
			}
		}

		.page-foot {
			padding: 20rpx 75rpx;
			background-color: #fff;
		}

		.container {
			padding: 40rpx 40rpx 160rpx !important;
		}
	}
</style>