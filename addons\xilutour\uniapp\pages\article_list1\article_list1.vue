<template>
	<view class="xilu">
		<view class="page-head bg-ghostWhite">
			<view class="g_tab">
				 <view class="item " @click="catagoryChange(index)" :class="{active:index==categroyIndex}" v-for="(item,index) in categoryList" :key="index">{{item.name}}</view>
			</view>
		</view>
		<view class="container plr30 pb30 bg-ghostWhite">
			
			<liu-waterfall :dataList="articleList" :column="2" :margin="0" :radius="15"></liu-waterfall>
			
			<view class="g-btn3-wrap">
				<view class="g-btn3" @click="fetch">{{articleListMore.text}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				categoryList:[],
				categroyIndex:0,
				articleList:[],
				articleListMore:{page:1},
				list:[
					{
						picUrl:'/static/img1.png',
						title:'资讯资讯资讯资讯资讯资讯资讯',
						desc:'资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯'
					},
					{
						picUrl:'/static/img1.png',
						title:'1',
						desc:'2'
					},	{
						picUrl:'/static/logo.png',
						title:'1',
						desc:'2'
					},
					{
						picUrl:'/static/img1.png',
						title:'1',
						desc:'2'
					}
				]
			};
		},
		onLoad() {
			this.statusBarHeight = app.globalData.statusBarHeight;
			this.refreshPage();
		},
		onReachBottom() {
			this.fetch();
		},
		onShareAppMessage() {
			
		},
		onShareTimeline() {
			
		},
		methods:{
			refreshPage(){
				 //分类
				 this.$core.get({url:'xilutour.common/article_category',data:{},loading:false,success:(ret)=>{
				 	this.categoryList = ret.data;
					//列表
					this.refresh();
				  }});
				  
				  uni.stopPullDownRefresh();
			},
			catagoryChange(index){
				this.categroyIndex = index;
				this.refresh();
			},
			refresh(){
				this.articleList = [];
				this.articleListMore = {page:1};
				this.fetch();
			},
			fetch(){
				let categoryList = this.categoryList;
				let category_id = categoryList[this.categroyIndex].id;
				this.$util.fetch(this, 'xilutour.article/lists', {pagesize:10,category_id: category_id}, 'articleListMore', 'articleList', 'data', data=>{
				  
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.bg-ghostWhite{
		background: #F7F9FB;
	}
.container{
	padding-top: 100rpx;
}

</style>
