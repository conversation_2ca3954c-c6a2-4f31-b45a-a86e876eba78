<template>
	<view class="xilu">
		<view class="page-foot bg-white">
			<view class="ptb20" @click="bindSave()">
				<view class="g-btn1" style="width: 670rpx;">确定提交</view>
			</view>
		</view>
		<view class="container">
			<view class="xilu_goods flex-box mb40">
				<image class="img" :src="order.order_tour.thumb_image" mode="aspectFill"></image>
				<view class="flex-1">
					<view class="m-ellipsis fs36 col-10 mb20">{{order.order_tour.tour_name}}</view>
					<view class="flex-box col-3 mb40">
						<text class="fs24">¥</text>
						<text class="fs30 flex-1">{{order.order_tour.tour_date_salesprice}}</text>
						<view class="fs30 col-89 pr40">数量 {{order.total_count}}</view>
					</view>
					<view class="flex-box">
						<view class="flex-1">
							<text class="fs30 col-89">实付款 </text>
							<text class="fs30 col-price">¥</text>
							<text class="fs40 col-price">{{order.pay_price}}</text>
						</view>
					</view>
				</view>
			</view>
			
			<view class="xilu_box1">
				<view class="flex-box flex-center">
						
						<uni-rate :value="comment.star" color="#F8E9D0" active-color="#FFAB29" :size="36" :margin="10" @change="changeRate"/>
				</view>
				<view class="fs30 col-89 tc mt25 pb30">星级选择</view>
			</view>

			<view class="xilu_box1">
				<textarea class="textarea" v-model="comment.content" placeholder="请输入评价内容"></textarea>
			</view>

			<view class="xilu_box1">
				<view class="fs30 col-5 mb30">上传图片</view>
				<view class="flex-box flex-wrap">
					<view class="upload" v-for="(img,index) in images" :key="index">
						<image :src="img" mode="aspectFill" class="img"></image>
						<image src="/static/icon/icon_close.png" mode="aspectFit" @click="bindDel(index)" class="del"></image>
					</view>
					<view class="upload" v-if="images.length<9" @click="chooseImages()">
						<image src="../../static/icon/icon_upload.png" mode="aspectFill" class="img"></image>
					</view>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
	var validate = require("../../xilu/validate.js");
	export default {
		data() {
			return {
				order:{
					state_text:'',
					order_no:'',
					total_count: 0,
					createtime_text: '',
					total_price: 0,
					pay_price: 0,
					
					order_tour:{
						thumb_image: '',
						tour_name: '',
						tour_date_salesprice: ''
					},
				},
				images: [],
				comment:{
					star: 5,
					content: ''
				}
			};
		},
		onLoad(options) {
			let page = this;
			this.getOpenerEventChannel().on("addComment",function(data){
				page.order = data
			})
		},
		methods:{
			changeRate(e){
				this.comment.star = e.value;
			},
			chooseImages(){
				let that = this;
				let images = that.images;
				uni.chooseImage({
					count: 9-images.length,
					success: res => {
						let files = res.tempFiles;
						files.map(item => {
							// #ifdef H5
							that.$core.uploadFileH5({
								filePath: item.path,
								success: (ret, response) => {
									images.push(ret.data.url);
									that.images = images;
								}
							});
							// #endif
							
							// #ifdef MP-WEIXIN
							that.$core.uploadFile({
								filePath: item.path,
								success: (ret, response) => {
									images.push(ret.data.url);
									that.images = images;
								}
							});
							// #endif
						});
					}
				});
			},
			bindDel(index){
				this.images.splice(index,1);
			},
			bindSave(){
				let formData = this.comment;
				formData.order_id = this.order.id;
				formData.tour_id = this.order.order_tour.tour_id;
				formData.images = this.images.length>0?this.images.join(','):'';
				var rule = [
					{name: 'content',nameChn: '评价内容',rules: ['require','max:200'],errorMsg: {require: '请填写评价内容',max: '评价内容最多200'}},
				];
				// 是否全部通过，返回Boolean
				if (!validate.check(formData, rule)) {
				    uni.showToast({
				        title: validate.getError()[0],
				        icon: 'none'
				    });
				    return;
				}
				this.$core.post({url: 'xilutour.tour_comment/add_comment',data: formData,success: ret => {
				    this.getOpenerEventChannel().emit("commentSuccess",{})
				    uni.navigateBack({});
				    uni.showToast({
				        title: '提交成功',
				        icon: 'none'
				    });
				}})
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		&_goods {
			.img {
				margin-right: 30rpx;
				display: block;
				width: 180rpx;
				height: 180rpx;
				border-radius: 15rpx;
			}
		}

		&_box1 {
			margin: 0 0 30rpx;
			padding: 40rpx 30rpx 0;
			width: 670rpx;
			background: #F7F9FB;
			border-radius: 30rpx;

			.textarea {
				padding: 0 0 10rpx;
				width: 100%;
				height: 210rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #555555;
				line-height: 34rpx;
			}

			.upload {
				position: relative;
				margin: 0 30rpx 30rpx 0;
				width: 150rpx;
				height: 150rpx;
				border-radius: 15rpx;

				.img {
					display: block;
					width: 150rpx;
					height: 150rpx;
					border-radius: 15rpx;
				}
				.del{
					position: absolute;
					right: -18rpx;
					top: -18rpx;
					display: block;
					width: 36rpx;
					height: 36rpx;
				}
			}
		}

		.container {
			padding: 30rpx 40rpx 160rpx !important;
		}

	}
</style>