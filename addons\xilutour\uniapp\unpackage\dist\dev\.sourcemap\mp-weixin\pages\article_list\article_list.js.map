{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/article_list/article_list.vue?66e2", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/article_list/article_list.vue?3217", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/article_list/article_list.vue?1296", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/article_list/article_list.vue?675b", "uni-app:///pages/article_list/article_list.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/article_list/article_list.vue?dffb", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/article_list/article_list.vue?54fe"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "categoryList", "categroyIndex", "articleList", "articleListMore", "page", "list", "picUrl", "title", "desc", "onLoad", "onReachBottom", "onShareAppMessage", "onShareTimeline", "methods", "refreshPage", "url", "loading", "success", "uni", "catagoryChange", "refresh", "fetch", "pagesize", "category_id"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmBz1B;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC,OACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC,iDAEA;EACAC,6CAEA;EACAC;IACAC;MAAA;MACA;MACA;QAAAC;QAAAhB;QAAAiB;QAAAC;UACA;UACA;UACA;QACA;MAAA;MAEAC;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QAAAhB;MAAA;MACA;IACA;IACAiB;MACA;MACA;MACA;QAAAC;QAAAC;MAAA,8DAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3FA;AAAA;AAAA;AAAA;AAAwjD,CAAgB,w7CAAG,EAAC,C;;;;;;;;;;;ACA5kD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/article_list/article_list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/article_list/article_list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./article_list.vue?vue&type=template&id=0354b366&scoped=true&\"\nvar renderjs\nimport script from \"./article_list.vue?vue&type=script&lang=js&\"\nexport * from \"./article_list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./article_list.vue?vue&type=style&index=0&id=0354b366&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0354b366\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/article_list/article_list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./article_list.vue?vue&type=template&id=0354b366&scoped=true&\"", "var components\ntry {\n  components = {\n    liuWaterfall: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/liu-waterfall/components/liu-waterfall/liu-waterfall\" */ \"@/uni_modules/liu-waterfall/components/liu-waterfall/liu-waterfall.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./article_list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./article_list.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"xilu\">\n\t\t<view class=\"page-head bg-ghostWhite\">\n\t\t\t<view class=\"g_tab\">\n\t\t\t\t <view class=\"item \" @click=\"catagoryChange(index)\" :class=\"{active:index==categroyIndex}\" v-for=\"(item,index) in categoryList\" :key=\"index\">{{item.name}}</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"container plr30 pb30 bg-ghostWhite\">\n\t\t\t\n\t\t\t<liu-waterfall :dataList=\"articleList\" :column=\"2\" :margin=\"0\" :radius=\"15\"></liu-waterfall>\n\t\t\t\n\t\t\t<view class=\"g-btn3-wrap\">\n\t\t\t\t<view class=\"g-btn3\" @click=\"fetch\">{{articleListMore.text}}</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\tconst app = getApp();\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcategoryList:[],\n\t\t\t\tcategroyIndex:0,\n\t\t\t\tarticleList:[],\n\t\t\t\tarticleListMore:{page:1},\n\t\t\t\tlist:[\n\t\t\t\t\t{\n\t\t\t\t\t\tpicUrl:'/static/img1.png',\n\t\t\t\t\t\ttitle:'资讯资讯资讯资讯资讯资讯资讯',\n\t\t\t\t\t\tdesc:'资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯资讯'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tpicUrl:'/static/img1.png',\n\t\t\t\t\t\ttitle:'1',\n\t\t\t\t\t\tdesc:'2'\n\t\t\t\t\t},\t{\n\t\t\t\t\t\tpicUrl:'/static/logo.png',\n\t\t\t\t\t\ttitle:'1',\n\t\t\t\t\t\tdesc:'2'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tpicUrl:'/static/img1.png',\n\t\t\t\t\t\ttitle:'1',\n\t\t\t\t\t\tdesc:'2'\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t};\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.statusBarHeight = app.globalData.statusBarHeight;\n\t\t\tthis.refreshPage();\n\t\t},\n\t\tonReachBottom() {\n\t\t\tthis.fetch();\n\t\t},\n\t\tonShareAppMessage() {\n\t\t\t\n\t\t},\n\t\tonShareTimeline() {\n\t\t\t\n\t\t},\n\t\tmethods:{\n\t\t\trefreshPage(){\n\t\t\t\t //分类\n\t\t\t\t this.$core.get({url:'xilutour.common/article_category',data:{},loading:false,success:(ret)=>{\n\t\t\t\t \tthis.categoryList = ret.data;\n\t\t\t\t\t//列表\n\t\t\t\t\tthis.refresh();\n\t\t\t\t  }});\n\t\t\t\t  \n\t\t\t\t  uni.stopPullDownRefresh();\n\t\t\t},\n\t\t\tcatagoryChange(index){\n\t\t\t\tthis.categroyIndex = index;\n\t\t\t\tthis.refresh();\n\t\t\t},\n\t\t\trefresh(){\n\t\t\t\tthis.articleList = [];\n\t\t\t\tthis.articleListMore = {page:1};\n\t\t\t\tthis.fetch();\n\t\t\t},\n\t\t\tfetch(){\n\t\t\t\tlet categoryList = this.categoryList;\n\t\t\t\tlet category_id = categoryList[this.categroyIndex].id;\n\t\t\t\tthis.$util.fetch(this, 'xilutour.article/lists', {pagesize:10,category_id: category_id}, 'articleListMore', 'articleList', 'data', data=>{\n\t\t\t\t  \n\t\t\t\t})\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.bg-ghostWhite{\n\t\tbackground: #F7F9FB;\n\t}\n.container{\n\tpadding-top: 100rpx;\n}\n\n</style>\n", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./article_list.vue?vue&type=style&index=0&id=0354b366&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./article_list.vue?vue&type=style&index=0&id=0354b366&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494342451\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}