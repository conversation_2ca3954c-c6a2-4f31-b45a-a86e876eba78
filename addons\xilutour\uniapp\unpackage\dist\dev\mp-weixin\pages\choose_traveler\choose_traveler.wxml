<view class="xilu data-v-64cfede6"><view class="page-foot flex-box flex-between data-v-64cfede6"><view data-event-opts="{{[['tap',[['addTraveler']]]]}}" class="btn1 data-v-64cfede6" bindtap="__e">新建出行人</view><view data-event-opts="{{[['tap',[['bindConfirm']]]]}}" class="btn2 data-v-64cfede6" bindtap="__e">确定</view></view><view class="container data-v-64cfede6"><block wx:for="{{travelerList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="xilu_traveler flex-box data-v-64cfede6"><view class="flex-1 data-v-64cfede6"><view class="flex-box mb30 data-v-64cfede6"><view class="col-10 mr20 data-v-64cfede6">{{item.username}}</view><block wx:if="{{item.gender==1}}"><image class="icon_gender data-v-64cfede6" src="/static/icon/icon_gender1.png" mode="aspectFit"></image></block><block wx:else><image class="icon_gender data-v-64cfede6" src="/static/icon/icon_gender2.png" mode="aspectFit"></image></block></view><view class="flex-box mb20 data-v-64cfede6"><view class="col-9 mr15 data-v-64cfede6">身份证号</view><view class="col-3 flex-1 data-v-64cfede6">{{item.idcard}}</view></view><view class="flex-box data-v-64cfede6"><view class="col-9 mr15 data-v-64cfede6">手机号码</view><view class="col-3 flex-1 data-v-64cfede6">{{item.mobile}}</view></view></view><block wx:if="{{item.checked}}"><image class="icon_check data-v-64cfede6" src="/static/icon/icon_checkon.png" mode="aspectFit" data-event-opts="{{[['tap',[['bindChoose',[index]]]]]}}" bindtap="__e"></image></block><block wx:else><image class="icon_check data-v-64cfede6" src="/static/icon/icon_check.png" mode="aspectFit" data-event-opts="{{[['tap',[['bindChoose',[index]]]]]}}" bindtap="__e"></image></block></view></block></view></view>