{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/basic_information/basic_information.vue?3b70", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/basic_information/basic_information.vue?8c57", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/basic_information/basic_information.vue?3e5d", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/basic_information/basic_information.vue?b410", "uni-app:///pages/basic_information/basic_information.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/basic_information/basic_information.vue?ee69", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/basic_information/basic_information.vue?82bf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "genderList", "id", "name", "checked", "userinfo", "avatar", "nickname", "mobile", "onLoad", "methods", "getUserinfo", "url", "loading", "success", "fail", "console", "chooseImage", "uni", "count", "that", "filePath", "bind<PERSON>hangeGender", "bindSave", "gender", "nameChn", "rules", "errorMsg", "require", "title", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,0BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACqE;AACL;AACsC;;;AAGtG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA00B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0C91B;AAAA,eACA;EACAC;IACA;MACAC,aACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAC;QACAZ;QACAa;QACAC;UACA;UACA;QACA;QACAC;UACAC;QACA;MACA;IACA;IACAC;MACA;MACAC;QACAC;QACAL;UACA;UAUAM;YACAC;YACAP;cACAM;YACA;UACA;QAEA;MACA;IACA;IACAE;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAjB;QACAC;QACAiB;MACA;MAEA;QACArB;QACAsB;QACAC;QACAC;UACAC;QACA;MACA,GACA;QACAzB;QACAsB;QACAC;QACAC;UACAC;QACA;MACA,EACA;MACA;;MAEA;QACAV;UACAW;UACAC;QACA;QACA;MACA;MACA;QAAAlB;QAAAZ;QAAAc;UACA;UACAT;UACAA;UACAa;UACAA;UACAA;YACAW;YACAC;UACA;QACA;MAAA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC/JA;AAAA;AAAA;AAAA;AAA6hD,CAAgB,85CAAG,EAAC,C;;;;;;;;;;;ACAjjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/basic_information/basic_information.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/basic_information/basic_information.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./basic_information.vue?vue&type=template&id=4e94c084&scoped=true&\"\nvar renderjs\nimport script from \"./basic_information.vue?vue&type=script&lang=js&\"\nexport * from \"./basic_information.vue?vue&type=script&lang=js&\"\nimport style0 from \"./basic_information.vue?vue&type=style&index=0&id=4e94c084&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4e94c084\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/basic_information/basic_information.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./basic_information.vue?vue&type=template&id=4e94c084&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./basic_information.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./basic_information.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"page-foot\" @click=\"bindSave()\">\r\n\t\t\t<view class=\"g-btn1\">确定</view>\r\n\t\t</view>\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"xilu_header\" @click=\"chooseImage()\">\r\n\t\t\t\t<image :src=\"userinfo.avatar\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"mask\">\r\n\t\t\t\t\t<view>更换</view>\r\n\t\t\t\t\t<view>头像</view>\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"g_input_box flex-box mb30\">\r\n\t\t\t\t<view class=\"fs30 col-5\">姓名</view>\r\n\t\t\t\t<input class=\"flex-1 tr fs30 col-10\" type=\"text\" placeholder=\"请输入姓名\" placeholder-class=\"col-10\" v-model=\"userinfo.nickname\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"g_input_box flex-box mb30\">\r\n\t\t\t\t<view class=\"fs30 col-5 flex-1\">性别</view>\r\n\t\t\t\t<view class=\"flex-box flex-between fs30 col-10\">\r\n\t\t\t\t\t\t<view class=\"flex-box pr25\" @click=\"bindChangeGender(index)\" v-for=\"(item,index) in genderList\" :key=\"index\">\r\n\t\t\t\t\t\t\t<image v-if=\"item.checked\" class=\"icon_check\" src=\"/static/icon/icon_checkon.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t<image v-else class=\"icon_check\" src=\"/static/icon/icon_check.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class=\"flex-box pl25\">\r\n\t\t\t\t\t\t<image class=\"icon_check\" src=\"/static/icon/icon_check.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<view>女</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"g_input_box flex-box mb30\">\r\n\t\t\t\t<view class=\"fs30 col-5\">手机号码</view>\r\n\t\t\t\t<input class=\"flex-1 tr fs30 col-10\" type=\"number\" disabled=\"true\" placeholder=\"请输入手机号码\" placeholder-class=\"col-10\" v-model=\"userinfo.mobile\"/>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\n\tvar validate = require(\"../../xilu/validate.js\");\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\n\t\t\t\tgenderList:[\n\t\t\t\t\t{id:0,name:'女',checked:false},\n\t\t\t\t\t{id:1,name:'男',checked:false}\n\t\t\t\t],\r\n\t\t\t\tuserinfo: {\n\t\t\t\t    avatar: '',\n\t\t\t\t    nickname: '',\n\t\t\t\t    mobile: ''\n\t\t\t\t},\r\n\t\t\t};\r\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.getUserinfo()\n\t\t},\n\t\tmethods:{\n\t\t\tgetUserinfo() {\n\t\t\t    this.$core.get({\n\t\t\t        url: 'xilutour.user/profile',\n\t\t\t        data: {},\n\t\t\t        loading: false,\n\t\t\t        success: ret => {\n\t\t\t\t\t\tthis.genderList[ret.data.gender].checked = true;\n\t\t\t            this.userinfo = ret.data;\n\t\t\t        },\n\t\t\t        fail: err => {\n\t\t\t            console.log(err);\n\t\t\t        }\n\t\t\t    });\n\t\t\t},\n\t\t\tchooseImage() {\n\t\t\t\tlet that = this;\n\t\t\t    uni.chooseImage({\n\t\t\t      count: 1,\n\t\t\t      success: res => {\n\t\t\t        let file = res.tempFiles[0];\n\t\t\t\t\t// #ifdef H5\n\t\t\t\t\tthat.$core.uploadFileH5({\n\t\t\t\t\t  filePath: file.path,\n\t\t\t\t\t  success: (ret, response) => {\n\t\t\t\t\t    that.userinfo.avatar = ret.data.url;\n\t\t\t\t\t  }\n\t\t\t\t\t});\n\t\t\t\t\t//#endif\n\t\t\t\t\t//#ifdef MP-WEIXIN\n\t\t\t        that.$core.uploadFile({\n\t\t\t          filePath: file.path,\n\t\t\t          success: (ret, response) => {\n\t\t\t            that.userinfo.avatar = ret.data.url;\n\t\t\t          }\n\t\t\t        });\n\t\t\t\t\t//#endif\n\t\t\t      }\n\t\t\t    });\n\t\t\t},\n\t\t\tbindChangeGender(index){\n\t\t\t\tfor(let i=0;i<this.genderList.length;i++){\n\t\t\t\t\tif(i==index){\n\t\t\t\t\t\tthis.genderList[i].checked = true;\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.genderList[i].checked = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.userinfo.gender = index;\n\t\t\t},\n\t\t\t\n\t\t\t//提交\n\t\t\tbindSave() {\n\t\t\t    let formData = {\n\t\t\t        avatar: this.userinfo.avatar,\n\t\t\t        nickname: this.userinfo.nickname,\n\t\t\t\t\tgender: this.userinfo.gender\n\t\t\t    };\n\t\t\t\n\t\t\t    var rule = [{\n\t\t\t            name: 'avatar',\n\t\t\t            nameChn: '头像',\n\t\t\t            rules: ['require'],\n\t\t\t            errorMsg: {\n\t\t\t                require: '请上传头像'\n\t\t\t            }\n\t\t\t        },\n\t\t\t        {\n\t\t\t            name: 'nickname',\n\t\t\t            nameChn: '昵称',\n\t\t\t            rules: ['require'],\n\t\t\t            errorMsg: {\n\t\t\t                require: '请填写昵称'\n\t\t\t            }\n\t\t\t        }\n\t\t\t    ];\n\t\t\t    // 是否全部通过，返回Boolean\n\t\t\t\n\t\t\t    if (!validate.check(formData, rule)) {\n\t\t\t        uni.showToast({\n\t\t\t            title: validate.getError()[0],\n\t\t\t            icon: 'none'\n\t\t\t        });\n\t\t\t        return;\n\t\t\t    }\n\t\t\t    this.$core.post({url: 'xilutour.user/profile',data: formData,success: ret => {\n\t\t\t        let userinfo = this.$core.getUserinfo();\n\t\t\t        userinfo.avatar = ret.data.avatar;\n\t\t\t        userinfo.nickname = ret.data.nickname;\n\t\t\t        uni.$emit(\"user_update\", {})\n\t\t\t        uni.navigateBack({});\n\t\t\t        uni.showToast({\n\t\t\t            title: '提交成功',\n\t\t\t            icon: 'none'\n\t\t\t        });\n\t\t\t    }})\n\t\t\t}\n\t\t\t\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.xilu {\r\n\t\t&_header {\r\n\t\t\tposition: relative;\r\n\t\t\tmargin: 0 auto 40rpx;\r\n\t\t\twidth: 208rpx;\r\n\t\t\theight: 208rpx;\r\n\t\t\tborder-radius: 50%;\r\n\r\n\t\t\timage {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 208rpx;\r\n\t\t\t\theight: 208rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t}\r\n\r\n\t\t\t.mask {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tright: 0;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\tpadding: 60rpx;\r\n\t\t\t\twidth: 208rpx;\r\n\t\t\t\theight: 208rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tbackground: rgba(0, 0, 0, 0.4);\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tline-height: 44rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.page-foot {\r\n\t\t\tpadding: 20rpx 75rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t}\r\n\r\n\t\t.container {\r\n\t\t\tpadding: 40rpx 40rpx 160rpx !important;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./basic_information.vue?vue&type=style&index=0&id=4e94c084&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./basic_information.vue?vue&type=style&index=0&id=4e94c084&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341235\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}