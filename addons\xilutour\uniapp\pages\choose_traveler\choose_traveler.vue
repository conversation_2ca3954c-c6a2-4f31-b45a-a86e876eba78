<template>
	<view class="xilu">
		<view class="page-foot flex-box flex-between">
			<view class="btn1" @click="addTraveler()">新建出行人</view>
			<view class="btn2" @click="bindConfirm()">确定</view>
		</view>
		<view class="container">
			<view class="xilu_traveler flex-box" v-for="(item,index) in travelerList" :key="index">
				<view class="flex-1">
					<view class="flex-box mb30">
						<view class="col-10 mr20">{{item.username}}</view>
						<image v-if="item.gender == 1" class="icon_gender" src="/static/icon/icon_gender1.png" mode="aspectFit"></image>
						<image v-else class="icon_gender" src="/static/icon/icon_gender2.png" mode="aspectFit"></image>
					</view>
					<view class="flex-box mb20">
						<view class="col-9 mr15">身份证号</view>
						<view class="col-3 flex-1">{{item.idcard}}</view>
					</view>
					<view class="flex-box">
						<view class="col-9 mr15">手机号码</view>
						<view class="col-3 flex-1">{{item.mobile}}</view>
					</view>
				</view>
				<image @click="bindChoose(index)" v-if="item.checked" class="icon_check" src="/static/icon/icon_checkon.png" mode="aspectFit"></image>
				<image @click="bindChoose(index)" v-else class="icon_check" src="/static/icon/icon_check.png" mode="aspectFit"></image>
				
			</view>
			
			<!-- <view class="xilu_traveler flex-box">
				<view class="flex-1">
					<view class="flex-box mb30">
						<view class="col-10 mr20">王一一</view>
						<image class="icon_gender" src="/static/icon/icon_gender2.png" mode="aspectFit"></image>
					</view>
					<view class="flex-box mb20">
						<view class="col-9 mr15">身份证号</view>
						<view class="col-3 flex-1">211224188801019225</view>
					</view>
					<view class="flex-box">
						<view class="col-9 mr15">手机号码</view>
						<view class="col-3 flex-1">13501010201</view>
					</view>
				</view>
				<image class="icon_check" src="/static/icon/icon_check.png" mode="aspectFit"></image>
			</view> -->

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tour:{
					tour_id:0,
					buy_adult_count:0,
					buy_child_count:0,
					tour_date_id:0,
				},
				travelerList:[],
			};
		},
		onLoad() {
			let page = this;
			this.getOpenerEventChannel().on("travelTransfor",function(data){
				if(Object.keys(data).length>0){
					page.tour = data;
				}
			})
			this.fetch();
		},
		methods:{
			fetch() {
				this.$core.get({url: 'xilutour.traveler/lists',data: this.tour,success: ret => {
						this.travelerList = ret.data;
					},
					fail: err => {
						
					}
				});
			},
			//选择出行
			bindChoose(index){
				let travelerList = this.travelerList;
				let adultCount = 0,childCount = 0;
				for(let i=0;i<travelerList.length;i++){
					if(travelerList[i].adult_type == 1 && travelerList[i].checked){
						adultCount+=1;
					}else if(travelerList[i].adult_type == 2 && travelerList[i].checked){
						childCount += 1;
					}
				}
				let tour = this.tour;
				let traveler = travelerList[index];
				if(!traveler.checked){
					if(traveler.adult_type==1 && tour.buy_adult_count <= adultCount){
						uni.showToast({
							title: "成人数量上限",
							icon:'none'
						})
						return false;
					}else if(traveler.adult_type==2 && tour.buy_child_count <= childCount){
						uni.showToast({
							title: "儿童数量上限",
							icon:'none'
						})
						return false;
					}
				}
				traveler.checked = !traveler.checked;
				this.travelerList[index] = traveler;
				this.$forceUpdate();
			},
			//添加出行
			addTraveler(){
				uni.navigateTo({
					url: '/pages/new_traveler/new_traveler',
					events:{
						setSuccess: data=>{
							this.fetch();
						}
					}
				})
			},
			
			bindConfirm(){
				let travelerList = this.travelerList;
				let lists = [];
				for(let i=0;i<travelerList.length;i++){
					if(travelerList[i].checked){
						lists.push(travelerList[i]);
					}
				}
				if(lists.length<=0){
					uni.showToast({title: "请选择出行人",icon:'none'});
					return ;
				}
				let tour = this.tour;
				if(lists.length<(tour.buy_adult_count+tour.buy_child_count)){
					uni.showModal({title: "提示",content:'请选择'+tour.buy_adult_count+'名成人，'+tour.buy_child_count+'名儿童'});
					return ;
				}
				this.getOpenerEventChannel().emit("chooseSuccess",lists);
				uni.navigateBack({})
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		.page-foot {
			padding: 20rpx 65rpx;
			background-color: #FFF;

			.btn1 {
				width: 290rpx;
				height: 90rpx;
				border-radius: 30rpx;
				border: 2rpx solid var(--normal);
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: var(--normal);
				line-height: 88rpx;
				text-align: center;
			}

			.btn2 {
				width: 290rpx;
				height: 90rpx;
				border-radius: 30rpx;
				background: var(--normal);
				font-size: 32rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 90rpx;
				text-align: center;
			}
		}

		.container {
			padding: 30rpx 40rpx 160rpx !important;
		}

		&_traveler {
			margin: 0 0 30rpx;
			padding: 0 30rpx;
			width: 670rpx;
			height: 200rpx;
			background: #F7F9FB;
			border-radius: 20rpx;
			font-size: 30rpx;
			line-height: 32rpx;

			.icon_gender {
				display: block;
				width: 30rpx;
				height: 30rpx;
			}

			.icon_check {
				display: block;
				width: 40rpx;
				height: 40rpx;
			}
		}
	}
</style>