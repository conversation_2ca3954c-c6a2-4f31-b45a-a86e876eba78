<?php

namespace addons\xilutour\controller;

use app\common\model\xilutour\Config;
use app\common\model\xilutour\scenery\Scenery;
use app\common\model\xilutour\scenery\SceneryProjects;
use app\common\model\xilutour\tour\Tour;
use app\common\model\xilutour\tour\TourDate;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\Hook;

/**
 *  @title   定时任务
 */
class Time extends \think\addons\Controller
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();
        if (!$this->request->isCli()) {
            $this->error('只允许在终端进行操作!');
        }
    }

    // 失效景点订单-取消
    public function scenery_order_cancel(){
        $lists = \app\common\model\xilutour\order\SceneryOrder::where('pay_status', '0')
            ->where('order_status','1')
            ->where('expiretime', 'lt', time())
            ->select();
        if(!$lists) return 'ok';
        Db::startTrans();
        try {
            foreach ($lists as $list){
                $list->order_status = '2';
                $list->canceltime = time();
                $list->save();
                //同时减少预约数量
                SceneryProjects::where('id',$list['order_project']['project_id'])->where('appoint_count','>',$list['total_count'])->update(['appoint_count'=>Db::raw("appoint_count-".$list['total_count'])]);
                Scenery::where('id',$list['order_project']['scenery_id'])->where('appoint_count','>',$list['total_count'])->update(['appoint_count'=>Db::raw("appoint_count-".$list['total_count'])]);
            }
        }catch (Exception|PDOException $e){
            Db::rollback();
            return $e->getMessage();
        }
        Db::commit();
        return '';
    }

    // 失效线路订单-取消
    public function tour_order_cancel(){
        $lists = \app\common\model\xilutour\order\TourOrder::where('pay_status', '0')
            ->where('order_status','1')
            ->where('expiretime', 'lt', time())
            ->select();
        if(!$lists) return 'ok';
        Db::startTrans();
        try {
            foreach ($lists as $row){
                $row->order_status = '2';
                $row->canceltime = time();
                $row->save();
                //同时减少预约数量
                TourDate::where('id',$row['order_tour']['tour_date_id'])->where('appoint_count','>',$row['total_count'])->update(['appoint_count'=>Db::raw("appoint_count-".$row['total_count'])]);
                Tour::where('id',$row['order_tour']['tour_id'])->where('appoint_count','>',$row['total_count'])->update(['appoint_count'=>Db::raw("appoint_count-".$row['total_count'])]);
            }
        }catch (Exception|PDOException $e){
            Db::rollback();
            return $e->getMessage();
        }
        Db::commit();
        return 'ok';
    }

    // 线路超出时间后佣金解冻
    public function tour_unfreeze(){
        $lists = \app\common\model\xilutour\order\TourOrder::with(['orderTour'])
            ->where('pay_status', '1')
            ->where('order_status','1')
            ->whereIn('refund_status',['0','4'])
            ->where('appoint_date', 'lt', strtotime(date("Y-m-d")))
            ->select();
        if(!$lists) return 'ok';
        Db::startTrans();
        try {
            foreach ($lists as $list){
                //解冻
                Hook::listen("xilutour_tour_unfreeze",$list);
                //Hook::listen("xilutour_tour_message",$order);
            }
        }catch (Exception|PDOException $e){
            Db::rollback();
            return $e->getMessage();
        }
        Db::commit();
        return 'ok';
    }
}
