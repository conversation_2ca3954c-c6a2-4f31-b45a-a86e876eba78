<template>
	<view class="container">
		<view class="header">
			<text class="title">小红书登录测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">1. 测试获取用户信息</text>
			<button @click="testGetUserProfile" class="test-btn">获取用户信息</button>
			<view v-if="userInfo" class="user-info">
				<text>昵称: {{ userInfo.nickName }}</text>
				<text>性别: {{ genderText }}</text>
				<image v-if="userInfo.avatarUrl" :src="userInfo.avatarUrl" class="avatar"></image>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">2. 测试完整登录流程</text>
			<button @click="testFullLogin" class="test-btn">完整登录测试</button>
			<view v-if="loginResult" class="login-result">
				<text>登录状态: {{ loginResult.success ? '成功' : '失败' }}</text>
				<text v-if="loginResult.message">消息: {{ loginResult.message }}</text>
				<text v-if="loginResult.userinfo">用户ID: {{ loginResult.userinfo.id }}</text>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">3. 调试信息</text>
			<view class="debug-info">
				<text v-for="(log, index) in debugLogs" :key="index" class="debug-log">{{ log }}</text>
			</view>
			<button @click="clearLogs" class="clear-btn">清除日志</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: null,
			loginResult: null,
			debugLogs: []
		}
	},
	computed: {
		genderText() {
			if (!this.userInfo) return '';
			switch(this.userInfo.gender) {
				case 1: return '男';
				case 2: return '女';
				default: return '未知';
			}
		}
	},
	methods: {
		addLog(message) {
			const timestamp = new Date().toLocaleTimeString();
			this.debugLogs.unshift(`[${timestamp}] ${message}`);
			console.log(message);
		},
		
		clearLogs() {
			this.debugLogs = [];
		},
		
		// 测试获取用户信息
		testGetUserProfile() {
			this.addLog('开始测试获取用户信息...');
			
			// 检查xhs对象是否存在
			if (typeof xhs === 'undefined') {
				this.addLog('错误: xhs对象不存在，请确认在小红书小程序环境中运行');
				uni.showToast({
					title: 'xhs对象不存在',
					icon: 'none'
				});
				return;
			}
			
			// 检查getUserProfile方法是否存在
			if (typeof xhs.getUserProfile !== 'function') {
				this.addLog('错误: xhs.getUserProfile方法不存在');
				uni.showToast({
					title: 'getUserProfile方法不存在',
					icon: 'none'
				});
				return;
			}
			
			this.addLog('调用xhs.getUserProfile...');
			
			xhs.getUserProfile({
				success: (res) => {
					this.addLog('获取用户信息成功: ' + JSON.stringify(res));
					this.userInfo = res.userInfo;
					uni.showToast({
						title: '获取用户信息成功',
						icon: 'success'
					});
				},
				fail: (err) => {
					this.addLog('获取用户信息失败: ' + JSON.stringify(err));
					uni.showToast({
						title: '获取用户信息失败',
						icon: 'none'
					});
				},
				complete: () => {
					this.addLog('获取用户信息操作完成');
				}
			});
		},
		
		// 测试完整登录流程
		testFullLogin() {
			this.addLog('开始测试完整登录流程...');
			this.loginResult = null;
			
			// 先测试uni.login
			uni.login({
				provider: 'xiaohongshu',
				success: (auth) => {
					this.addLog('uni.login成功，code: ' + auth.code);
					
					// 然后获取用户信息
					this.$core.xhsGetUserProfile()
						.then((userInfo) => {
							this.addLog('获取用户信息成功: ' + JSON.stringify(userInfo));
							
							// 调用登录接口
							this.$core.post({
								url: 'xilutour.user/xhs_login_with_profile',
								loading: true,
								data: {
									code: auth.code,
									userInfo: userInfo,
									platform: 'xhs',
									puser_id: 0
								},
								success: (ret) => {
									this.addLog('登录接口调用成功: ' + JSON.stringify(ret));
									this.loginResult = {
										success: true,
										message: ret.msg,
										userinfo: ret.data.userinfo
									};
									uni.showToast({
										title: '登录成功',
										icon: 'success'
									});
								},
								fail: (ret) => {
									this.addLog('登录接口调用失败: ' + JSON.stringify(ret));
									this.loginResult = {
										success: false,
										message: ret.msg || '登录失败'
									};
									uni.showToast({
										title: '登录失败',
										icon: 'none'
									});
								}
							});
						})
						.catch((err) => {
							this.addLog('获取用户信息失败: ' + JSON.stringify(err));
							this.loginResult = {
								success: false,
								message: '获取用户信息失败'
							};
							uni.showToast({
								title: '获取用户信息失败',
								icon: 'none'
							});
						});
				},
				fail: (err) => {
					this.addLog('uni.login失败: ' + JSON.stringify(err));
					this.loginResult = {
						success: false,
						message: '登录授权失败'
					};
					uni.showToast({
						title: '登录授权失败',
						icon: 'none'
					});
				}
			});
		}
	},
	
	onLoad() {
		this.addLog('小红书登录测试页面加载完成');
		
		// 检查运行环境
		// #ifdef MP-XHS
		this.addLog('当前运行在小红书小程序环境');
		// #endif
		
		// #ifndef MP-XHS
		this.addLog('警告: 当前不在小红书小程序环境，某些功能可能无法正常使用');
		// #endif
	}
}
</script>

<style scoped>
.container {
	padding: 20px;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30px;
}

.title {
	font-size: 24px;
	font-weight: bold;
	color: #333;
}

.test-section {
	background-color: white;
	border-radius: 10px;
	padding: 20px;
	margin-bottom: 20px;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 15px;
}

.test-btn {
	background-color: #ff2442;
	color: white;
	border: none;
	border-radius: 5px;
	padding: 10px 20px;
	font-size: 16px;
	margin-bottom: 15px;
}

.clear-btn {
	background-color: #999;
	color: white;
	border: none;
	border-radius: 5px;
	padding: 8px 16px;
	font-size: 14px;
}

.user-info {
	background-color: #f9f9f9;
	border-radius: 5px;
	padding: 15px;
}

.user-info text {
	display: block;
	margin-bottom: 10px;
	color: #666;
}

.avatar {
	width: 60px;
	height: 60px;
	border-radius: 30px;
	margin-top: 10px;
}

.login-result {
	background-color: #f0f8ff;
	border-radius: 5px;
	padding: 15px;
}

.login-result text {
	display: block;
	margin-bottom: 8px;
	color: #333;
}

.debug-info {
	background-color: #f5f5f5;
	border-radius: 5px;
	padding: 15px;
	max-height: 300px;
	overflow-y: auto;
	margin-bottom: 10px;
}

.debug-log {
	display: block;
	font-size: 12px;
	color: #666;
	margin-bottom: 5px;
	word-break: break-all;
}
</style>
