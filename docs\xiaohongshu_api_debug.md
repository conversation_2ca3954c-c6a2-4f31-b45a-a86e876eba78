# 小红书API调试指南

## 问题现象

根据日志显示的错误信息：
```
[ info ] 小红书access_token缓存不存在，重新获取
[ log ] http request : {"ret":"{\"success\":false,\"msg\":\"参数不合法\",\"data\":null,\"code\":1}","err":"","url":"https:\/\/miniapp-sandbox.xiaohongshu.com\/api\/rmp\/token"}
[ error ] 获取小红书access_token失败：获取小红书access_token失败
```

## 可能的原因

1. **API URL错误**：使用了沙箱环境URL，但可能需要使用正式环境
2. **请求参数格式错误**：小红书API可能需要特定的参数格式
3. **AppID/AppSecret错误**：配置的应用信息不正确
4. **请求头格式错误**：Content-Type或其他请求头不正确
5. **网络连接问题**：防火墙或网络限制

## 快速诊断步骤

### 1. 检查配置信息

首先确认小红书配置是否正确：

```bash
# 使用API接口检查
POST /api/xilutour.user/xhs_cache_manage
{
    "action": "test"
}
```

或者运行调试脚本：
```bash
php scripts/debug_xhs_api.php
```

### 2. 手动测试API调用

使用curl命令直接测试小红书API：

```bash
# 测试正式环境
curl -X POST 'https://miniapp.xiaohongshu.com/api/rmp/token' \
     -H 'Content-Type: application/x-www-form-urlencoded' \
     -d 'appid=YOUR_APPID&secret=YOUR_APPSECRET'

# 测试沙箱环境
curl -X POST 'https://miniapp-sandbox.xiaohongshu.com/api/rmp/token' \
     -H 'Content-Type: application/x-www-form-urlencoded' \
     -d 'appid=YOUR_APPID&secret=YOUR_APPSECRET'
```

### 3. 检查网络连接

```bash
# 测试网络连通性
curl -I https://miniapp.xiaohongshu.com/
curl -I https://miniapp-sandbox.xiaohongshu.com/
```

## 解决方案

### 方案1：切换到正式环境

如果您的应用已经上线，应该使用正式环境而不是沙箱环境：

1. 在管理后台的小红书配置中，确保使用正式环境的AppID和AppSecret
2. 代码会自动根据`app_debug`配置选择环境

### 方案2：检查参数格式

小红书API可能需要特定的参数格式：

```php
// 尝试不同的参数组合
$params = [
    'appid' => $this->appId,
    'secret' => $this->appSecret
];

// 或者添加grant_type
$params = [
    'appid' => $this->appId,
    'secret' => $this->appSecret,
    'grant_type' => 'client_credentials'
];
```

### 方案3：修改请求头

尝试不同的Content-Type：

```php
// 方式1：form-data (默认)
$response = Http::post($url, $params);

// 方式2：JSON格式
$response = Http::post($url, json_encode($params), [
    'Content-Type: application/json'
]);

// 方式3：明确指定form格式
$response = Http::post($url, $params, [
    'Content-Type: application/x-www-form-urlencoded'
]);
```

### 方案4：检查小红书开发者平台配置

1. 登录小红书开放平台
2. 检查应用状态是否正常
3. 确认AppID和AppSecret是否正确
4. 检查API权限是否已开通
5. 确认回调域名配置是否正确

## 调试工具使用

### 1. API测试接口

```bash
# 测试连接
POST /api/xilutour.user/xhs_cache_manage
{"action": "test"}

# 查看缓存信息
POST /api/xilutour.user/xhs_cache_manage
{"action": "info"}

# 清除缓存重试
POST /api/xilutour.user/xhs_cache_manage
{"action": "clear"}
```

### 2. 调试脚本

```bash
# 运行完整的API调试
php scripts/debug_xhs_api.php

# 运行缓存测试
php scripts/test_xhs_cache.php test
```

## 常见错误码

| 错误码 | 错误信息 | 解决方案 |
|--------|----------|----------|
| 1 | 参数不合法 | 检查AppID、AppSecret格式和参数名称 |
| 40001 | access_token missing | 检查access_token参数是否传递 |
| 40014 | invalid access_token | access_token无效，需要重新获取 |
| 42001 | access_token expired | access_token过期，需要刷新 |

## 日志分析

关键日志信息：
```
[ info ] 小红书access_token缓存不存在，重新获取
[ log ] 小红书API请求: URL
[ log ] 小红书API响应: 响应内容
[ error ] 获取小红书access_token失败：错误信息
```

## 联系支持

如果以上方案都无法解决问题：

1. 收集完整的错误日志
2. 记录使用的AppID（不要包含AppSecret）
3. 记录测试的API URL和参数
4. 联系小红书技术支持或查看官方文档

## 临时解决方案

如果API调用持续失败，可以考虑：

1. **降级处理**：暂时禁用小红书登录功能
2. **手动配置**：在配置中添加一个固定的access_token用于测试
3. **延迟重试**：增加重试间隔和次数
4. **备用方案**：提供其他登录方式

## 成功标志

当问题解决后，您应该看到类似的日志：
```
[ info ] 小红书access_token缓存不存在，重新获取
[ log ] 小红书API请求: https://miniapp.xiaohongshu.com/api/rmp/token
[ log ] 小红书API响应: {"success":true,"data":{"access_token":"AT_xxx","expires_in":7200}}
[ info ] 小红书access_token获取并缓存成功，有效期：7140秒
```
