<view class="xilu data-v-032f11f4"><view class="page-head bg-white data-v-032f11f4"><view class="g_tab flex-box data-v-032f11f4"><view data-event-opts="{{[['tap',[['tabClick',[0]]]]]}}" class="{{['item','flex-1','data-v-032f11f4',(tabIdx==0)?'active':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['tabClick',[1]]]]]}}" class="{{['item','flex-1','data-v-032f11f4',(tabIdx==1)?'active':'']}}" bindtap="__e">待使用</view><view data-event-opts="{{[['tap',[['tabClick',[2]]]]]}}" class="{{['item','flex-1','data-v-032f11f4',(tabIdx==2)?'active':'']}}" bindtap="__e">已使用</view><view data-event-opts="{{[['tap',[['tabClick',[3]]]]]}}" class="{{['item','flex-1','data-v-032f11f4',(tabIdx==3)?'active':'']}}" bindtap="__e">已过期</view></view></view><view class="container data-v-032f11f4"><block xhs:for="{{couponList}}" xhs:for-item="coupon" xhs:for-index="index" xhs:key="index"><view class="{{['xilu_coupon','data-v-032f11f4',coupon.state_text==1?'':'disabled']}}"><image class="bg data-v-032f11f4" src="/static/icon/icon_coupon_bg1.png" mode="aspectFill"></image><view class="inner flex-box data-v-032f11f4"><view class="left data-v-032f11f4"><view class="fwb mb20 data-v-032f11f4"><text class="fs24 data-v-032f11f4">¥</text><text class="fs50 data-v-032f11f4">{{coupon.coupon.money}}</text></view><view class="man data-v-032f11f4">{{"满"+coupon.coupon.at_least+"可用"}}</view></view><view class="right flex-1 flex-box data-v-032f11f4"><view class="flex-1 data-v-032f11f4"><view class="fs30 mb20 data-v-032f11f4">{{coupon.coupon.name}}</view><view class="fs24 data-v-032f11f4">{{coupon.endtime_text+"到期"}}</view></view><block xhs:if="{{coupon.state_text==1}}"><view class="use data-v-032f11f4">使用</view></block><block xhs:else><block xhs:if="{{coupon.state_text==2}}"><image class="icon_disabled data-v-032f11f4" src="../../static/icon/icon_coupon1.png" mode="aspectFill"></image></block><block xhs:else><image class="icon_disabled data-v-032f11f4" src="../../static/icon/icon_coupon2.png" mode="aspectFill"></image></block></block></view></view></view></block><view class="g-btn3-wrap data-v-032f11f4"><view data-event-opts="{{[['tap',[['fetch',['$event']]]]]}}" class="g-btn3 data-v-032f11f4" bindtap="__e">{{couponListMore.text}}</view></view></view></view>