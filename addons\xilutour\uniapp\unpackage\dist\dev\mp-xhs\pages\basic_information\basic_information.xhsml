<view class="xilu data-v-4e94c084"><view data-event-opts="{{[['tap',[['bindSave']]]]}}" class="page-foot data-v-4e94c084" bindtap="__e"><view class="g-btn1 data-v-4e94c084">确定</view></view><view class="container data-v-4e94c084"><view data-event-opts="{{[['tap',[['chooseImage']]]]}}" class="xilu_header data-v-4e94c084" bindtap="__e"><image src="{{userinfo.avatar}}" mode="aspectFill" class="data-v-4e94c084"></image><view class="mask data-v-4e94c084"><view class="data-v-4e94c084">更换</view><view class="data-v-4e94c084">头像</view></view></view><view class="g_input_box flex-box mb30 data-v-4e94c084"><view class="fs30 col-5 data-v-4e94c084">姓名</view><input class="flex-1 tr fs30 col-10 data-v-4e94c084" type="text" placeholder="请输入姓名" placeholder-class="col-10" data-event-opts="{{[['input',[['__set_model',['$0','nickname','$event',[]],['userinfo']]]]]}}" value="{{userinfo.nickname}}" bindinput="__e"/></view><view class="g_input_box flex-box mb30 data-v-4e94c084"><view class="fs30 col-5 flex-1 data-v-4e94c084">性别</view><view class="flex-box flex-between fs30 col-10 data-v-4e94c084"><block xhs:for="{{genderList}}" xhs:for-item="item" xhs:for-index="index" xhs:key="index"><view data-event-opts="{{[['tap',[['bindChangeGender',[index]]]]]}}" class="flex-box pr25 data-v-4e94c084" bindtap="__e"><block xhs:if="{{item.checked}}"><image class="icon_check data-v-4e94c084" src="/static/icon/icon_checkon.png" mode="aspectFit"></image></block><block xhs:else><image class="icon_check data-v-4e94c084" src="/static/icon/icon_check.png" mode="aspectFit"></image></block><view class="data-v-4e94c084">{{item.name}}</view></view></block></view></view><view class="g_input_box flex-box mb30 data-v-4e94c084"><view class="fs30 col-5 data-v-4e94c084">手机号码</view><input class="flex-1 tr fs30 col-10 data-v-4e94c084" type="number" disabled="true" placeholder="请输入手机号码" placeholder-class="col-10" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['userinfo']]]]]}}" value="{{userinfo.mobile}}" bindinput="__e"/></view></view></view>