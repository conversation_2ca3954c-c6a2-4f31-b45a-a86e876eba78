<template>
	<view class="xilu">
		<view class="container">
			<image class="m-backdrop" src="/static/icon/icon_bg1.png" mode="widthFix"></image>
			<view class="m-header">
				<image class="m-backdrop" src="/static/icon/icon_bg1.png" mode="widthFix"></image>
				<view class="g-custom-nav flex-box plr30"
					:style="{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }">
					<image @click="navBack" src="/static/icon/icon_back.png" mode="aspectFit" class="icon_back"></image>
					<view class="search_box flex-box">
						<view class="addr m-ellipsis" @click="bindCityChange">{{currentCity?currentCity.name:''}}</view>
						<image class="icon_arrow" src="/static/icon/icon_arrow.png"></image>
						<view class="line"></view>
						<image class="icon_search" src="/static/icon/icon_search.png"></image>
						<input class="input flex-1" type="text" v-model="query.q" placeholder="景点名称" confirm-type="search" @confirm="search" placeholder-class="cola" />
					</view>
				</view>
				<view class="g_tab">
					<view class="item" :class="{'active': query.category_id == -1}" @click="tabClick(-1)">推荐</view>
					<view class="item" :class="{'active': query.category_id == item.id}" @click="tabClick(item.id)"  v-for="(item,index) in categoryList">{{item.name}}</view>
				</view>
				<view class="g_select flex-box">
					<view class="flex-box flex-center flex-1" @click="bindChangeSearchTab(0)" :class="{active: searchTab==0}">
						<view>综合</view>
						<image :src="'/static/icon/icon_arrow1' + (searchTab==0 ? 'on' : '') + '.png'" mode="aspectFit"></image>
					</view>
					<view class="flex-box flex-center flex-1" @click="bindChangeSearchTab(1)" :class="{active: searchTab==1}">
						<view>价格</view>
						<image class="icon_price" :class="{'rotate': searchTab==1 && query.order=='desc'}" src="/static/icon/icon_arrow3.png" mode="aspectFit"></image>
					</view>
					<view class="flex-box flex-center flex-1" @click="bindChangeSearchTab(2)" :class="{active: searchTab==2}">
						<view>热门</view>
						<image :src="'/static/icon/icon_arrow1' + (searchTab==2 ? 'on' : '') + '.png'" mode="aspectFit"></image>
					</view>
					<view class="flex-box flex-center flex-1" @click="bindChangeSearchTab(3)" :class="{active: searchTab==3}">
						<view>筛选</view>
						<image src="/static/icon/icon_arrow1.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="g_select_wrap" v-show="isShowSelect">
					<block v-if="searchTab == 0">
						<view class="item" :class="{active:query.sort=='weigh'}" @click="bindChangeSort('weigh')">综合</view>
						<view class="item" :class="{active:query.sort=='points'}" @click="bindChangeSort('points')">推荐</view>
					</block>
					<block v-if="searchTab == 2">
						<view class="item" :class="{active:query.sort=='updatetime'}" @click="bindChangeSort('updatetime')">最新</view>
						<view class="item" :class="{active:query.sort=='hot_status'}" @click="bindChangeSort('hot_status')">热门</view>
					</block>
				</view>
			</view>
			
			<view class="g_mask" v-show="isShowSelect" @click="closeSelect"></view>

			<view class="pr plr40 pb40" :style="{ paddingTop: 'calc(316rpx + ' + statusBarHeight + 'px)' }">
				
				<view class="g_landscape_list">
					<scenery-list :sceneryList="sceneryList"></scenery-list>
					<view class="g-btn3-wrap">
						<view class="g-btn3" @click="fetch">{{sceneryListMore.text}}</view>
					</view>
				</view>

			</view>
		</view>
	</view>
</template>

<script>
	import sceneryList from '@/components/scenery-list/scenery-list.vue';
	const app = getApp();
	export default {
		components: {
			sceneryList
		},
		data() {
			return {
				statusBarHeight: 20,
				categoryList:[],
				bannerList:[],
				currentCity:null,
				sceneryList:[],
				sceneryListMore:{page:1},
				query:{q:'',sort: 'weigh',order: 'desc',category_id: -1},
				searchTab: 0,
				isShowSelect: false
			};
		},
		onLoad(options) {
			this.statusBarHeight = getApp().globalData.statusBarHeight;
			if(options.category_id){
				this.query.category_id = options.category_id;
			}
			if(options.tag_id){
				this.query.tag_ids = options.tag_id;
			}
			let page = this;
			this.currentCity = this.$core.getCurrentCity();
			page.refreshPage();
			uni.$on(app.globalData.Event.CurrentCityChange, function(currentCity) {
				page.currentCity = currentCity;
				page.refresh();
			})
		},
		onShareAppMessage() {
			
		},
		onShareTimeline() {
			
		},
		onReachBottom() {
			this.fetch();
		},
		onUnload() {
			uni.$off(app.globalData.Event.CurrentCityChange,this);
		},
		methods: {
			navBack(){
				uni.navigateBack()
			},
			tabClick(id) {
				this.query.category_id = id;
				this.refresh();
			},
			//更换城市
			bindCityChange(){
				uni.navigateTo({
				    url: '/pages/change_city/change_city'
				})
			},
			search(){
				this.refresh()
			},
			//排序,0=综合（弹窗），1=价格，2=热门（弹窗)
			bindChangeSearchTab(searchTabIndex){
				if(searchTabIndex == 0 || searchTabIndex == 2){
					this.isShowSelect = this.isShowSelect&&this.searchTab == searchTabIndex?false:true;
				}else if(searchTabIndex == 1){
					this.query.sort = 'salesprice';
					this.query.order = this.query.order == 'asc'?'desc':'asc';
					this.isShowSelect = false;
					this.refresh();
				}else if(searchTabIndex == 3){
					let query = this.query;
					uni.navigateTo({
						url:'/pages/filtrate/filtrate?type=scenery',
						events:{
							searchSuccess: data=>{
								this.query = data;
								this.refresh();
							},
						},
						success(res) {
							res.eventChannel.emit("searchTransform",query)
						}
					})
				}
				this.searchTab = searchTabIndex;
			},
			//二级排序
			bindChangeSort(sort){
				this.query.sort = sort;
				this.query.order = 'desc';
				this.isShowSelect = false;
				this.refresh();
			},
			refreshPage(){
				//轮播
				this.$core.get({url:'xilutour.banner/index',data:{group:'scenery_index'},loading:false,success:(ret)=>{
					this.bannerList = ret.data;
				 }});
				 //分类
				 this.$core.get({url:'xilutour.common/scenery_category',data:{},loading:false,success:(ret)=>{
				 	this.categoryList = ret.data;
				  }});
				  //列表
				  this.refresh();
				  
				  uni.stopPullDownRefresh();
			},
			refresh(cacheQuery=false){
				this.sceneryList = [];
				this.sceneryListMore = {page:1};
				if(cacheQuery) this.query = {};
				this.fetch();
			},
			fetch(){
				let query = this.query;
				query.pagesize = 10;
				this.$util.fetch(this, 'xilutour.scenery/lists', query, 'sceneryListMore', 'sceneryList', 'data', data=>{
				  
				})
			},
			closeSelect() {
				this.isShowSelect = false
			}
		}
	}
</script>

<style lang="less" scoped>

</style>