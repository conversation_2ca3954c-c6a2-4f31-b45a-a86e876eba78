{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue?0945", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue?3c92", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue?9b5e", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue?8a68", "uni-app:///pages/landscape_detail/landscape_detail.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue?a529", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_detail/landscape_detail.vue?cee1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "bar<PERSON>itle", "statusBarHeight", "swiper<PERSON><PERSON>rent", "tabIdx", "sceneryId", "scenery", "work_time", "address", "name", "view_count", "worktime", "tags", "projects", "coupons", "total", "commentList", "commentListMore", "page", "setCol", "scrollLeft", "onLoad", "onReachBottom", "onShareAppMessage", "path", "title", "onShareTimeline", "query", "onPageScroll", "methods", "swiper<PERSON><PERSON>e", "tabClick", "navBack", "uni", "fetchDetail", "url", "scenery_id", "loading", "success", "ret", "fail", "console", "content", "showCancel", "complete", "fetch", "callphone", "phoneNumber", "bindOpenLocation", "latitude", "longitude", "toggleCollection", "bindReceive", "coupon_id", "<PERSON><PERSON><PERSON>", "setTimeout", "createSelectorQuery", "selectViewport", "scrollOffset", "view", "boundingClientRect", "scrollTop", "duration", "exec", "scrollFunc", "bindAppoint", "couponPopOpen", "couponPopClose", "bindPrev", "urls", "current"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACsC;;;AAGrG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAAy0B,CAAgB,yyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8M71B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACAC;IACA;IACA;MACAC;MACAD;MACA;IACA;EACA;EACAE;IACA;IACA;IACA;MACAC;IACA;IACA;MACAF;MACAE;IACA;EACA;EACAC;IACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAC;IACA;IACAC;MAAA;MACA;QACAC;QACAnC;UACAoC;QACA;QACAC;QACAC;UACAC;UACAA;UACAA;UACA;UACA;QACA;QACAC;UACAC;UACAR;YACAR;YACAiB;YACAC;YACAC;cACAX;YACA;UACA;UACA;QACA;MACA;IACA;IACAY;MAAA;MACA;QACAT;MACA;MACAT;MACA,0GACA;QACA;MACA;IACA;IACA;IACAmB;MACA;MACAb;QACAc;MACA;IACA;IACA;IACAC;MACA;MACA,0GACA1C;MACA2B;QACAgB;QACAC;QACAzC;QACAD;MACA;IACA;IACA;IACA2C;MAAA;MACA;QACA;MACA;MACA;QACAhB;QACAnC;UACAoC;QACA;QACAE;UACA;QACA;QACAE;MACA;IACA;IACA;IACAY;MAAA;MACA;MACA;MACA;QACA;MACA;MACA;QACAjB;QACAnC;UACAqD;QACA;QACAf;UACAL;YACAR;UACA;UACAX;UACA;QACA;QACA0B;UACAP;YACAR;YACAiB;UACA;UACA;QACA;MACA;IACA;IACAY;MAAA;MACA;MACA;MACA;MACAC;QACAtB,IACAuB,sBACAC,iBACAC;UACAjB;UACAkB,KACAC;YACAnB;YAEAR;cACA4B;cACAC;YACA;UAEA,GACAC;QACA,GACAA;MACA;IACA;IACAC;MACA;MACAvB;IACA;IACA;IACAwB;MACA;QACA;MACA;MACAhC;QACAE;MACA;IACA;IACA;IACA+B;MACA;IACA;IACA;IACAC;MACA;IACA;IAEAC;MACAnC;QACAoC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvbA;AAAA;AAAA;AAAA;AAA4hD,CAAgB,65CAAG,EAAC,C;;;;;;;;;;;ACAhjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/landscape_detail/landscape_detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/landscape_detail/landscape_detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./landscape_detail.vue?vue&type=template&id=740743b4&scoped=true&\"\nvar renderjs\nimport script from \"./landscape_detail.vue?vue&type=script&lang=js&\"\nexport * from \"./landscape_detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./landscape_detail.vue?vue&type=style&index=0&id=740743b4&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"740743b4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/landscape_detail/landscape_detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_detail.vue?vue&type=template&id=740743b4&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.scenery.tags.length\n  var g1 = _vm.scenery.coupons.length\n  var l0 =\n    _vm.tabIdx == 1\n      ? _vm.__map(_vm.scenery.projects, function (project, index) {\n          var $orig = _vm.__get_orig(project)\n          var g2 = _vm.scenery.projects.length\n          var g3 = g2 > 0 ? project.tags.length : null\n          return {\n            $orig: $orig,\n            g2: g2,\n            g3: g3,\n          }\n        })\n      : null\n  var l1 =\n    _vm.tabIdx == 5\n      ? _vm.__map(_vm.commentList, function (comment, index) {\n          var $orig = _vm.__get_orig(comment)\n          var g4 = comment.images_text.length\n          return {\n            $orig: $orig,\n            g4: g4,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"page-foot\">\r\n\t\t\t<view class=\"g_order_foot1 flex-box\">\r\n\t\t\t\t<navigator class=\"nav\" url=\"/pages/index/index\" open-type=\"switchTab\" hover-class=\"none\">\r\n\t\t\t\t\t<image src=\"/static/icon/icon_btn1.png\" mode=\"aspectFit\" class=\"icon\"></image>\r\n\t\t\t\t\t<view>首页</view>\r\n\t\t\t\t</navigator>\r\n\t\t\t\t<button class=\"nav\" hover-class=\"none\" open-type=\"contact\">\r\n\t\t\t\t\t<image src=\"/static/icon/icon_btn2.png\" mode=\"aspectFit\" class=\"icon\"></image>\r\n\t\t\t\t\t<view>客服</view>\r\n\t\t\t\t</button>\r\n\t\t\t\t<view class=\"nav\" @click=\"toggleCollection()\">\r\n\t\t\t\t\t<image :src=\"'/static/icon/icon_btn3'+(scenery.is_collection_count == 1 ? 'on' : '')+'.png'\" mode=\"aspectFit\"\r\n\t\t\t\t\t\tclass=\"icon\"></image>\r\n\t\t\t\t\t<view>{{scenery.is_collection_count == 1?'已收藏':'收藏'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @click=\"bindBuy()\" class=\"btn1\">立即购买</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"m-header\" :style=\"{ background: setCol ? 'var(--normal)' : 'unset'}\">\r\n\t\t\t\t<view class=\"g-custom-nav flex-box plr30\"\r\n\t\t\t\t\t:style=\"{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }\">\r\n\t\t\t\t\t<image @click=\"navBack\" src=\"/static/icon/icon_back1.png\" mode=\"aspectFit\" class=\"icon_back\"></image>\r\n\t\t\t\t\t<view class=\"flex-1 pr35 mr30 fs26 col-f tc\">{{barTitle}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_swiper\">\r\n\t\t\t\t<swiper class=\"swiper\" :current=\"swiperCurrent\" circular @change=\"swiperChange\">\r\n\t\t\t\t\t<swiper-item v-for=\"(img,index) in scenery.images_text\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"nav\" :class=\"{'scale': swiperCurrent !==index}\">\r\n\t\t\t\t\t\t\t<image :src=\"img\" mode=\"aspectFill\" class=\"img\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t\t<view class=\"swiper_dots flex-box flex-center\">\r\n\t\t\t\t\t<view class=\"dots\" v-for=\"(img,index) in scenery.images_text\" :key=\"index\"\r\n\t\t\t\t\t\t:class=\"{'active': swiperCurrent == index}\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_info_wrap\">\r\n\t\t\t\t<view class=\"g_travel_list\">\r\n\t\t\t\t\t<view class=\"flex-box mb30\">\r\n\t\t\t\t\t\t<view class=\"flex-1 col-price\" v-if=\"scenery.min_project\">\r\n\t\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t\t<text class=\"fs40\">{{scenery.min_project.salesprice}}</text>\r\n\t\t\t\t\t\t\t<text class=\"fs30\">起</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"fs28 col-a\">{{scenery.view_count}}人浏览</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box flex-wrap pb10\" v-if=\"scenery.tags.length>0\">\r\n\t\t\t\t\t\t<view class=\"label\" v-for=\"(tag,index) in scenery.tags\" :key=\"index\">{{tag.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mb20\">\r\n\t\t\t\t\t\t<text class=\"level\" v-if=\"scenery.level\">{{scenery.level.name}}</text>\r\n\t\t\t\t\t\t<text class=\"fs36 col-10\">{{scenery.name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"desc\">{{scenery.introduce}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_coupon_box\">\r\n\t\t\t\t<view class=\"opening_box\">\r\n\t\t\t\t\t<view class=\"flex-box mb30\">\r\n\t\t\t\t\t\t<view class=\"col-normal fs30 mr30\">开放时间</view>\r\n\t\t\t\t\t\t<view class=\"fs30 col-5 mr15\">{{scenery.work_time}}</view>\r\n\t\t\t\t\t\t<image class=\"icon\" v-if=\"scenery.tel\" @click=\"callphone()\" src=\"/static/icon/icon_phone.png\"\r\n\t\t\t\t\t\t\tmode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box flex-align-start\">\r\n\t\t\t\t\t\t<view class=\"flex-1 mr10 fs30 col-5\">\r\n\t\t\t\t\t\t\t{{scenery.city?scenery.city.name:''}}{{scenery.district?scenery.district.name:''}}{{scenery.address}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<image v-if=\"scenery.lat\" @click=\"bindOpenLocation()\" class=\"icon\" src=\"/static/icon/icon_go.png\"\r\n\t\t\t\t\t\t\tmode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"scenery.coupons.length>0\" class=\"flex-box ptb25 m-hairline--top\" @click=\"couponPopOpen\">\r\n\t\t\t\t\t<view class=\"col-price fs30 mr20\">优惠券</view>\r\n\t\t\t\t\t<view class=\"coupon_list\">\r\n\t\t\t\t\t\t<view class=\"coupon\" v-for=\"(item,index) in scenery.coupons\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_coupon_bg3.png\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t<view class=\"inner\">{{item.name}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image src=\"/static/icon/icon_arrow2.png\" mode=\"aspectFit\" class=\"g-icon30\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<scroll-view class=\"g_tab\" :scroll-x=\"true\" :scroll-y=\"false\" :scroll-left=\"scrollLeft\" @scroll=\"scrollFunc\">\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 1}\" @click=\"tabClick(1)\">景区项目</view>\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 2}\" @click=\"tabClick(2)\">景区详情</view>\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 3}\" @click=\"tabClick(3)\">费用说明</view>\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 4}\" @click=\"tabClick(4)\">行程必看</view>\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 5}\" @click=\"tabClick(5)\">用户评价({{total}})</view>\r\n\t\t\t</scroll-view>\r\n\r\n\t\t\t<view class=\"xilu_detail_box\">\r\n\t\t\t\t<view v-if=\"tabIdx == 1\">\r\n\t\t\t\t\t<view class=\"project flex-box\" v-if=\"scenery.projects.length>0\" v-for=\"(project,index) in scenery.projects\"\r\n\t\t\t\t\t\t:key=\"index\">\r\n\t\t\t\t\t\t<image :src=\"project.thumb_image_text\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t<view class=\"fs34 col-10 mb20\">{{project.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"label_wrap pb10\" v-if=\"project.tags.length>0\">\r\n\t\t\t\t\t\t\t\t<view class=\"label\" v-for=\"tag in project.tags_text\">{{tag}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex-box flex-align-end\">\r\n\t\t\t\t\t\t\t\t<view class=\"flex-1 col-price\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"fs30\">低至¥</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"fs40\">{{project.salesprice}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"btn\" @click=\"bindAppoint(project.id)\">预定</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view v-if=\"tabIdx == 2\">\r\n\t\t\t\t\t<rich-text :nodes=\"scenery.content\"></rich-text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view v-if=\"tabIdx == 3\">\r\n\t\t\t\t\t<view class=\"detail fs30 col-3\">\r\n\t\t\t\t\t\t<view class=\"fs36 col-10 mb30\">费用包含</view>\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<rich-text :nodes=\"scenery.statement\"></rich-text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view v-if=\"tabIdx == 4\">\r\n\t\t\t\t\t<view class=\"detail fs30 col-3\">\r\n\t\t\t\t\t\t<rich-text :nodes=\"scenery.travel_know\"></rich-text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view v-if=\"tabIdx == 5\">\r\n\t\t\t\t\t<view class=\"g_comment\" v-for=\"(comment,index) in commentList\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"flex-box\">\r\n\t\t\t\t\t\t\t<image :src=\"comment.user.avatar\" mode=\"aspectFill\" class=\"head\"></image>\r\n\t\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t\t<view class=\"fs30 col-5 mb20\">{{comment.user.nickname}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"fs24 col-89\">{{comment.createtime_text}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-rate :disabled=\"true\" :readonly=\"true\" :value=\"4\" :count=\"4\" :size=\"18\" :gutter=\"0\"\r\n\t\t\t\t\t\t\t\tactive-icon=\"/static/icon/icon_star.png\"></u-rate>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text1 pt25\">{{comment.content}}</view>\r\n\t\t\t\t\t\t<view class=\"flex-box flex-wrap\" v-if=\"comment.images_text.length>0\">\r\n\t\t\t\t\t\t\t<image @click=\"bindPrev(index,index2)\" v-for=\"(img,index2) in comment.images_text\" :src=\"img\"\r\n\t\t\t\t\t\t\t\tmode=\"aspectFill\" class=\"img1\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"g-btn3-wrap\">\r\n\t\t\t\t\t\t<view class=\"g-btn3\" @click=\"fetch\">{{commentListMore.text}}</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<uni-popup ref=\"couponPopup\" type=\"bottom\">\r\n\t\t\t\t<view class=\"g_coupon_pop\">\r\n\t\t\t\t\t<view class=\"fs30 col-10 tc mb30\">优惠券</view>\r\n\t\t\t\t\t<image src=\"/static/icon/icon_close.png\" mode=\"aspectFit\" class=\"icon_close\" @click=\"couponPopClose\"></image>\r\n\r\n\t\t\t\t\t<view class=\"pop_coupon_wrap\">\r\n\t\t\t\t\t\t<view class=\"pop_coupon\" v-for=\"(coupon,index) in scenery.coupons\" :key=\"index\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_coupon_bg1.png\" mode=\"aspectFill\" class=\"bg\"></image>\r\n\t\t\t\t\t\t\t<view class=\"inner flex-box\">\r\n\t\t\t\t\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"fwb mb20\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"fs24\">¥</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"fs50\">{{coupon.money}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"man\">满{{coupon.at_least}}可用</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"right flex-1 flex-box\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"fs30 mb20\">{{coupon.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"fs24\">{{coupon.use_end_time_text}}到期</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"use\" v-if=\"coupon.is_receive_count == 1\">已领取</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"use\" @click=\"bindReceive(index)\" v-else>领取</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\n\t\t\t\tbarTitle: '风景详情',\r\n\t\t\t\tstatusBarHeight: 20,\r\n\t\t\t\tswiperCurrent: 0,\r\n\t\t\t\ttabIdx: 1,\r\n\t\t\t\tsceneryId: 0,\r\n\t\t\t\tscenery: {\r\n\t\t\t\t\twork_time: '',\r\n\t\t\t\t\taddress: '',\r\n\t\t\t\t\tname: '',\r\n\t\t\t\t\tview_count: 0,\r\n\t\t\t\t\tworktime: '',\r\n\t\t\t\t\ttags: [],\r\n\t\t\t\t\tprojects: [],\r\n\t\t\t\t\tcoupons: []\r\n\t\t\t\t},\r\n\t\t\t\ttotal: 0,\r\n\t\t\t\tcommentList: [],\r\n\t\t\t\tcommentListMore: {\r\n\t\t\t\t\tpage: 1\r\n\t\t\t\t},\r\n\t\t\t\tsetCol: false,\r\n\t\t\t\tscrollLeft:0\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.statusBarHeight = getApp().globalData.statusBarHeight;\r\n\t\t\tthis.sceneryId = options.id;\r\n\t\t\tthis.fetchDetail();\r\n\t\t\tthis.fetch();\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tif (this.tabIdx == 5) {\r\n\t\t\t\tthis.fetch();\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShareAppMessage(e) {\r\n\t\t\tlet userinfo = this.$core.getUserinfo();\r\n\t\t\tlet path = '/pages/landscape_detail/landscape_detail?id=' + this.sceneryId;\r\n\t\t\tif (userinfo) {\r\n\t\t\t\tpath += '&pid=' + userinfo.pid\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\ttitle: this.scenery.name,\r\n\t\t\t\tpath: path,\r\n\t\t\t\t//imageUrl: this.scenery.thumb_image_text\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShareTimeline() {\r\n\t\t\tlet userinfo = this.$core.getUserinfo();\r\n\t\t\tlet query = \"id=\" + this.sceneryId\r\n\t\t\tif (userinfo) {\r\n\t\t\t\tquery += '&pid=' + userinfo.pid\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\ttitle: this.scenery.name,\r\n\t\t\t\tquery: query\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPageScroll(e) {\r\n\t\t\tif (e.scrollTop > 350) {\r\n\t\t\t\tthis.setCol = true\r\n\t\t\t} else {\r\n\t\t\t\tthis.setCol = false\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tswiperChange(e) {\r\n\t\t\t\tthis.swiperCurrent = e.detail.current\r\n\t\t\t},\r\n\t\t\ttabClick(i) {\r\n\t\t\t\tthis.tabIdx = i;\r\n\t\t\t},\r\n\t\t\tnavBack() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\t\t\tfetchDetail() {\r\n\t\t\t\tthis.$core.post({\r\n\t\t\t\t\turl: 'xilutour.scenery/detail',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tscenery_id: this.sceneryId\r\n\t\t\t\t\t},\r\n\t\t\t\t\tloading: false,\r\n\t\t\t\t\tsuccess: ret => {\r\n\t\t\t\t\t\tret.data.content = this.$core.richTextnew(ret.data.content);\r\n\t\t\t\t\t\tret.data.statement = this.$core.richTextnew(ret.data.statement);\r\n\t\t\t\t\t\tret.data.travel_know = this.$core.richTextnew(ret.data.travel_know);\r\n\t\t\t\t\t\tthis.scenery = ret.data;\n\t\t\t\t\t\tthis.barTitle = ret.data.name;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: err => {\r\n\t\t\t\t\t\tconsole.log(err);\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle:'提示',\n\t\t\t\t\t\t\tcontent: err.msg,\n\t\t\t\t\t\t\tshowCancel:false,\n\t\t\t\t\t\t\tcomplete() {\n\t\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tfetch() {\r\n\t\t\t\tlet query = {\r\n\t\t\t\t\tscenery_id: this.sceneryId\r\n\t\t\t\t};\r\n\t\t\t\tquery.pagesize = 10;\r\n\t\t\t\tthis.$util.fetch(this, 'xilutour.scenery_comment/lists', query, 'commentListMore', 'commentList', 'data',\r\n\t\t\t\t\tdata => {\r\n\t\t\t\t\t\tthis.total = data.total;\r\n\t\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//拨打电话\r\n\t\t\tcallphone() {\r\n\t\t\t\tlet tel = this.scenery.tel;\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: tel\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//导航\r\n\t\t\tbindOpenLocation() {\r\n\t\t\t\tlet scenery = this.scenery;\r\n\t\t\t\tlet address = (scenery.city ? scenery.city.name : '') + (scenery.district ? scenery.district.name : '') + (\r\n\t\t\t\t\tscenery.address)\r\n\t\t\t\tuni.openLocation({\r\n\t\t\t\t\tlatitude: Number(scenery.lat),\r\n\t\t\t\t\tlongitude: Number(scenery.lng),\r\n\t\t\t\t\tname: scenery.name,\r\n\t\t\t\t\taddress: address\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//收藏\r\n\t\t\ttoggleCollection() {\r\n\t\t\t\tif (!this.$core.getUserinfo(true)) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$core.post({\r\n\t\t\t\t\turl: 'xilutour.scenery/toggle_collection',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tscenery_id: this.scenery.id\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: ret => {\r\n\t\t\t\t\t\tthis.scenery.is_collection_count = ret.data.is_collection_count;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: err => {}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//领取优惠券\r\n\t\t\tbindReceive(index) {\r\n\t\t\t\tlet coupons = this.scenery.coupons;\r\n\t\t\t\tlet coupon = coupons[index];\r\n\t\t\t\tif (!this.$core.getUserinfo(true)) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$core.post({\r\n\t\t\t\t\turl: 'xilutour.coupon/receive',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tcoupon_id: coupon.id\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: ret => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '领取成功',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tcoupons[index].is_receive_count = 1;\r\n\t\t\t\t\t\tthis.scenery.coupons = coupons;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: err => {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\tcontent: err.msg,\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tbindBuy() {\r\n\t\t\t\tthis.tabIdx = 1;\r\n\t\t\t\tthis.scrollLeft = 0\r\n\t\t\t\tlet view = uni.createSelectorQuery().in(this).select(\".g_tab\");\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni\r\n\t\t\t\t\t\t.createSelectorQuery()\r\n\t\t\t\t\t\t.selectViewport()\r\n\t\t\t\t\t\t.scrollOffset((res) => {\r\n\t\t\t\t\t\t\tconsole.log(\"竖直滚动位置\" + res.scrollTop);\r\n\t\t\t\t\t\t\tview\r\n\t\t\t\t\t\t\t\t.boundingClientRect((data) => {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"节点离页面顶部的距离为\" + data.top);\r\n\r\n\t\t\t\t\t\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\t\t\t\t\t\tscrollTop: res.scrollTop + data.top - this.statusBarHeight - 45,\r\n\t\t\t\t\t\t\t\t\t\tduration: 500\r\n\t\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t.exec();\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.exec();\r\n\t\t\t\t}, 500)\r\n\t\t\t},\r\n\t\t\tscrollFunc(e){\r\n\t\t\t\tthis.scrollLeft = e.detail.scrollLeft;\r\n\t\t\t\tconsole.log(e);\r\n\t\t\t},\r\n\t\t\t//预定\r\n\t\t\tbindAppoint(projectId) {\r\n\t\t\t\tif (!this.$core.getUserinfo(true)) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/landscape_pay/landscape_pay?scenery_id=' + this.scenery.id + '&project_id=' + projectId\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 打开优惠券弹窗\r\n\t\t\tcouponPopOpen() {\r\n\t\t\t\tthis.$refs.couponPopup.open();\r\n\t\t\t},\r\n\t\t\t// 关闭优惠券弹窗\r\n\t\t\tcouponPopClose() {\r\n\t\t\t\tthis.$refs.couponPopup.close();\r\n\t\t\t},\r\n\r\n\t\t\tbindPrev(index, index2) {\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\turls: this.commentList[index].images_text,\r\n\t\t\t\t\tcurrent: index2\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.g_order_foot1 {\r\n\t\tmargin: 0 auto 40rpx;\r\n\t}\r\n\r\n\t.xilu {\r\n\t\t&_detail_box {\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\r\n\t\t\t.img_detail {\r\n\t\t\t\tmargin: 0 0 20rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\r\n\t\t\t.step_wrap {\r\n\t\t\t\t.step {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tmargin: 0 0 30rpx 50rpx;\r\n\t\t\t\t\tpadding: 30rpx 30rpx 40rpx;\r\n\t\t\t\t\twidth: 640rpx;\r\n\t\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\t\tbox-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);\r\n\t\t\t\t\tborder-radius: 30rpx;\r\n\r\n\t\t\t\t\t.img_step {\r\n\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\tmax-width: 100%;\r\n\t\t\t\t\t\tmargin: 30rpx 0 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.step::before {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tleft: -40rpx;\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\twidth: 2rpx;\r\n\t\t\t\t\theight: calc(100% + 30rpx);\r\n\t\t\t\t\tbackground-color: rgba(255, 171, 41, 0.2);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.step::after {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 38rpx;\r\n\t\t\t\t\tleft: -50rpx;\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\twidth: 22rpx;\r\n\t\t\t\t\theight: 22rpx;\r\n\t\t\t\t\tbackground: #FFAB29;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.detail {\r\n\t\t\t\tpadding: 30rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tbox-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\tline-height: 44rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.project {\r\n\t\t\t\tpadding: 30rpx;\r\n\t\t\t\tmargin: 0 0 30rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tbox-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);\r\n\t\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\tmargin-right: 30rpx;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\twidth: 200rpx;\r\n\t\t\t\t\theight: 200rpx;\r\n\t\t\t\t\tborder-radius: 25rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.label_wrap {\r\n\t\t\t\t\toverflow-x: scroll;\r\n\t\t\t\t\toverflow-y: hidden;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\tfont-size: 0;\r\n\r\n\t\t\t\t\t.label {\r\n\t\t\t\t\t\tmargin: 0 20rpx 0 0;\r\n\t\t\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\t\tbackground: rgba(255, 171, 41, 0.1);\r\n\t\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #EB9003;\r\n\t\t\t\t\t\tline-height: 48rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btn {\r\n\t\t\t\t\twidth: 100rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tbackground: var(--normal);\r\n\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\tline-height: 60rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t&_regis {\r\n\t\t\tpadding: 0 40rpx;\r\n\t\t\toverflow-x: scroll;\r\n\t\t\toverflow-y: hidden;\r\n\t\t\twhite-space: nowrap;\r\n\r\n\t\t\t.item {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin: 0 30rpx 0 0;\r\n\t\t\t\tpadding: 20rpx 0 0;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 270rpx;\r\n\t\t\t\theight: 312rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tborder-radius: 25rpx;\r\n\t\t\t\tborder: 1px solid #EEEEEE;\r\n\r\n\t\t\t\t.line {\r\n\t\t\t\t\twidth: 12rpx;\r\n\t\t\t\t\theight: 2rpx;\r\n\t\t\t\t\tbackground-color: var(--normal);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btn {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\twidth: 270rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tbackground-color: var(--normal);\r\n\t\t\t\t\tborder-radius: 2rpx 2rpx 25rpx 25rpx;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\tline-height: 60rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_coupon_box {\r\n\t\t\tmargin: 0 30rpx 15rpx;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\twidth: 690rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t.coupon_list {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\toverflow-x: scroll;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\toverflow-y: hidden;\r\n\t\t\t\theight: 56rpx;\r\n\r\n\t\t\t\t.coupon {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\tdisplay: inline-block;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\twidth: 180rpx;\r\n\t\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.inner {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #D91B00;\r\n\t\t\t\t\t\tline-height: 56rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.opening_box {\r\n\t\t\t\tpadding: 30rpx 0;\r\n\t\t\t\tbackground: #F7F9FB;\r\n\r\n\t\t\t\t.icon {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\twidth: 34rpx;\r\n\t\t\t\t\theight: 34rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t\t&_info_wrap {\r\n\t\t\tposition: relative;\r\n\t\t\tmargin-top: -50rpx;\r\n\t\t\tpadding: 40rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 50rpx 50rpx 0 0;\r\n\r\n\t\t\t.desc {\r\n\t\t\t\tline-height: 42rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.label {\r\n\t\t\t\tmargin: 0 20rpx 20rpx 0;\r\n\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\theight: 48rpx;\r\n\t\t\t\tbackground: rgba(255, 171, 41, 0.1);\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #EB9003;\r\n\t\t\t\tline-height: 48rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_swiper {\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t.swiper {\r\n\t\t\t\twidth: 750rpx;\r\n\t\t\t\theight: 860rpx;\r\n\r\n\t\t\t\t.nav {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\twidth: 750rpx;\r\n\t\t\t\t\theight: 860rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.img {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\twidth: 750rpx;\r\n\t\t\t\t\theight: 860rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.swiper_dots {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 80rpx;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright: 0;\r\n\r\n\t\t\t\t.dots {\r\n\t\t\t\t\tmargin: 0 4rpx;\r\n\t\t\t\t\twidth: 14rpx;\r\n\t\t\t\t\theight: 4rpx;\r\n\t\t\t\t\tbackground: #D8D8D8;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.dots.active {\r\n\t\t\t\t\tbackground: var(--normal);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_detail.vue?vue&type=style&index=0&id=740743b4&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_detail.vue?vue&type=style&index=0&id=740743b4&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341181\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}