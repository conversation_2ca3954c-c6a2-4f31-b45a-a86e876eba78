-- 为用户表添加来源字段的迁移脚本
-- 执行时间: 2025-08-01

-- 检查字段是否已存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'fa_user' 
     AND COLUMN_NAME = 'source') = 0,
    'ALTER TABLE `fa_user` ADD COLUMN `source` varchar(30) DEFAULT ''web'' COMMENT ''用户来源:web=网站,wxmini=微信小程序,xhs=小红书'' AFTER `birthday`;',
    'SELECT ''Column source already exists'' as message;'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有用户设置默认来源为web
UPDATE `fa_user` SET `source` = 'web' WHERE `source` = '' OR `source` IS NULL;
