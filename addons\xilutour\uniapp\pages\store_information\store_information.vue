<template>
	<view class="xilu">
		<view class="container" style="padding-bottom: 30rpx;">

			<view class="pr">
				

				<view class="xilu_select">
					<view class="g_tab">
						<view class="item" :class="{'active': query.category_id == -1}" @click="tabClick(-1)">全部</view>
						<view class="item" :class="{'active': query.category_id == item.id}" @click="tabClick(item.id)" v-for="(item,index) in categoryList">{{item.name}}</view>
					</view>
				</view>

				<view class="p40">
					<view class="g_landscape_list">
						
						<scenery-list :sceneryList="sceneryList"></scenery-list>
						<view class="g-btn3-wrap">
							<view class="g-btn3" @click="fetch">{{sceneryListMore.text}}</view>
						</view>
					</view>
				</view>

			</view>

		</view>
	</view>
</template>

<script>
	import sceneryList from '@/components/scenery-list/scenery-list.vue'
	const app = getApp();
	export default {
		components: {
			sceneryList
		},
		data() {
			return {
				statusBarHeight: 20,
				categoryList:[],
				sceneryList:[],
				sceneryListMore:{page:1},
				query:{category_id: -1}
			};
		},
		onLoad() {
			this.refreshPage();
		},
		onPullDownRefresh() {
			this.refreshPage();
		},
		onReachBottom() {
			this.fetch();
		},
		onUnload() {
		},
		methods: {
			//切换分类
			tabClick(id) {
				this.query.category_id = id;
				this.refresh();
			},
			refreshPage(){
				 //分类
				 this.$core.get({url:'xilutour.common/scenery_category',data:{},loading:false,success:(ret)=>{
				 	this.categoryList = ret.data;
				  }});
				  //列表
				  this.refresh();
				  uni.stopPullDownRefresh();
			},
			refresh(){
				this.sceneryList = [];
				this.sceneryListMore = {page:1};
				this.fetch();
			},
			fetch(){
				let query = this.query;
				query.pagesize = 10;
				this.$util.fetch(this, 'xilutour.user/verifier_shop', query, 'sceneryListMore', 'sceneryList', 'data', data=>{
				  
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		&_swiper {
			position: relative;
			padding: 30rpx 40rpx 40rpx;

			.swiper {
				display: block;
				width: 670rpx;
				height: 320rpx;

				.nav {
					position: relative;
					width: 670rpx;
					height: 320rpx;
					border-radius: 15rpx;
				}

				.img {
					margin: 0 auto;
					display: block;
					width: 670rpx;
					height: 320rpx;
					border-radius: 15rpx;
				}
			}

			.swiper_dots {
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;

				.dots {
					margin: 0 4rpx;
					width: 14rpx;
					height: 4rpx;
					background: #D8D8D8;
				}

				.dots.active {
					background: #333333;
				}
			}
		}
		
		&_select{
			position: sticky;
			left: 0;
			right: 0;
			z-index: 10;
			background-color: #fff;
		}
		.container{
			overflow-y: unset;
		}
	}
</style>