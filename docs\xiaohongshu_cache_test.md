# 小红书access_token缓存功能测试文档

## 功能概述

为了提高小红书登录的性能和稳定性，我们实现了access_token的缓存机制：

1. **自动缓存**：获取access_token后自动缓存，避免频繁请求
2. **智能过期**：缓存时间比实际过期时间提前60秒，避免边界问题
3. **自动重试**：当access_token过期时，自动清除缓存并重新获取
4. **日志记录**：详细记录缓存的获取、使用和清除过程

## 缓存机制说明

### 缓存Key规则
```
xhs_access_token_{md5(appid)}
```

### 缓存时间
- 默认缓存时间：`expires_in - 60` 秒
- 最小缓存时间：60秒
- 目的：避免在token即将过期时使用

### 自动重试机制
当API返回以下错误码时，自动清除缓存并重试：
- `40001`: access_token missing
- `40014`: invalid access_token
- `42001`: access_token expired

## API接口测试

### 1. 查看缓存信息
```bash
POST /api/xilutour.user/xhs_cache_manage
Content-Type: application/json

{
    "action": "info"
}
```

响应示例：
```json
{
    "code": 1,
    "msg": "缓存信息获取成功",
    "data": {
        "cache_key": "xhs_access_token_abc123...",
        "has_cache": true,
        "token_preview": "AT_abc123...",
        "app_id": "your_app_id"
    }
}
```

### 2. 清除缓存
```bash
POST /api/xilutour.user/xhs_cache_manage
Content-Type: application/json

{
    "action": "clear"
}
```

响应示例：
```json
{
    "code": 1,
    "msg": "缓存清除成功",
    "data": {
        "cleared": true
    }
}
```

### 3. 强制刷新缓存
```bash
POST /api/xilutour.user/xhs_cache_manage
Content-Type: application/json

{
    "action": "refresh"
}
```

响应示例：
```json
{
    "code": 1,
    "msg": "缓存刷新成功",
    "data": {
        "new_token_preview": "AT_def456...",
        "cache_info": {
            "cache_key": "xhs_access_token_abc123...",
            "has_cache": true,
            "token_preview": "AT_def456...",
            "app_id": "your_app_id"
        }
    }
}
```

## 缓存性能测试

### 测试场景1：首次获取token
1. 清除所有缓存
2. 调用小红书登录接口
3. 观察日志，应该看到"重新获取"的记录
4. 检查缓存信息，应该显示有缓存

### 测试场景2：使用缓存的token
1. 确保缓存中有有效token
2. 再次调用小红书登录接口
3. 观察日志，应该看到"从缓存获取成功"的记录
4. 响应时间应该明显更快

### 测试场景3：token过期自动重试
1. 手动设置一个过期的token到缓存中
2. 调用小红书登录接口
3. 观察日志，应该看到"可能过期，清除缓存重试"的记录
4. 最终应该成功获取新token

## 日志监控

### 关键日志信息
```
小红书access_token从缓存获取成功
小红书access_token缓存不存在，重新获取
小红书access_token获取并缓存成功，有效期：7140秒
小红书access_token可能过期，清除缓存重试
获取小红书access_token失败：错误信息
```

### 日志查看方法
```bash
# 查看最近的小红书相关日志
tail -f runtime/log/202X/XX/XX.log | grep "小红书"

# 查看错误日志
tail -f runtime/log/202X/XX/XX_error.log | grep "小红书"
```

## 缓存配置优化

### 1. Redis缓存配置
如果使用Redis作为缓存驱动，建议配置：
```php
// config/cache.php
'redis' => [
    'type' => 'redis',
    'host' => '127.0.0.1',
    'port' => 6379,
    'password' => '',
    'select' => 0,
    'timeout' => 0,
    'expire' => 0,
    'persistent' => false,
    'prefix' => 'xilutour:',
],
```

### 2. 文件缓存配置
如果使用文件缓存：
```php
// config/cache.php
'file' => [
    'type' => 'file',
    'path' => CACHE_PATH,
    'prefix' => '',
    'expire' => 0,
],
```

## 性能对比

### 无缓存情况
- 每次登录都需要请求access_token
- 平均响应时间：500-1000ms
- 增加小红书API调用频率

### 有缓存情况
- 首次获取后缓存2小时
- 平均响应时间：50-100ms
- 减少90%的API调用

## 故障排查

### 1. 缓存不生效
检查项：
- 缓存驱动配置是否正确
- 缓存目录是否有写权限
- Redis服务是否正常运行

### 2. Token频繁过期
检查项：
- 系统时间是否正确
- 小红书API返回的expires_in是否正常
- 缓存时间计算是否正确

### 3. 登录失败
检查项：
- 小红书配置是否正确
- 网络连接是否正常
- API接口是否有变更

## 监控建议

### 1. 缓存命中率监控
定期统计缓存命中率，理想情况下应该在90%以上。

### 2. API调用频率监控
监控小红书API的调用频率，避免超出配额限制。

### 3. 错误率监控
监控登录失败率，及时发现和解决问题。

## 安全注意事项

1. **缓存管理接口安全**：生产环境应该移除或限制缓存管理接口的访问
2. **Token安全**：确保缓存中的token不会被恶意获取
3. **日志安全**：避免在日志中记录完整的token信息

## 后续优化建议

1. **分布式缓存**：在多服务器环境下使用Redis等分布式缓存
2. **缓存预热**：在系统启动时预先获取和缓存token
3. **监控告警**：当缓存失效率过高时发送告警
4. **自动清理**：定期清理过期的缓存数据
