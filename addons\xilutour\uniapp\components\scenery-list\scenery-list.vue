<template>
	<view>
		<navigator class="item" :url="'/pages/landscape_detail/landscape_detail?id='+item.id" hover-class="none" v-for="(item,index) in sceneryList">
			<image :src="item.thumb_image_text" mode="aspectFill" class="img"></image>
			<view class="mb20">
				<text class="level" v-if="item.level">{{item.level.name}}</text>
					<text class="g_feng">{{item.points}}</text>
				<text class="fs36 col-10">{{item.name}}</text>
			</view>
			<view class="desc m-ellipsis mb15">{{item.introduce}}</view>
			<view class="flex-box">
				<view class="flex-1 col-price" v-if="item.project">
					<text class="fs30">¥</text>
					<text class="fs40">{{item.project.salesprice}}</text>
					<text class="fs30">起</text>
				</view>
				<view class="fs28 col-a">{{item.view_count}}人浏览</view>
			</view>
		</navigator>
		
	</view>
</template>

<script>
	export default {
		name:"scenery-list",
		props:{
			sceneryList:{
				type: Array,
				default:[]
			}
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style>

</style>