<view class="xilu data-v-176b31c6"><view data-event-opts="{{[['tap',[['bindSave']]]]}}" class="page-foot data-v-176b31c6" bindtap="__e"><view class="g-btn1 data-v-176b31c6">确定</view></view><view class="container data-v-176b31c6"><view class="g_input_box flex-box mb30 data-v-176b31c6"><view class="fs30 col-5 data-v-176b31c6">姓名</view><input class="flex-1 tr fs30 col-10 data-v-176b31c6" type="text" placeholder="请输入姓名" placeholder-class="col-10" data-event-opts="{{[['input',[['__set_model',['$0','username','$event',[]],['traveler']]]]]}}" value="{{traveler.username}}" bindinput="__e"/></view><view class="g_input_box flex-box mb30 data-v-176b31c6"><view class="fs30 col-5 flex-1 data-v-176b31c6">性别</view><view class="flex-box flex-between flex-1 fs30 col-10 data-v-176b31c6"><block xhs:for="{{genderList}}" xhs:for-item="item" xhs:for-index="index" xhs:key="index"><view data-event-opts="{{[['tap',[['bindGenderChange',['$0'],[[['genderList','',index,'id']]]]]]]}}" class="flex-box data-v-176b31c6" bindtap="__e"><block xhs:if="{{item.id==traveler.gender}}"><image class="icon_check data-v-176b31c6" src="/static/icon/icon_checkon.png" mode="aspectFit"></image></block><block xhs:else><image class="icon_check data-v-176b31c6" src="/static/icon/icon_check.png" mode="aspectFit"></image></block><view class="data-v-176b31c6">{{item.name}}</view></view></block></view></view><view class="g_input_box flex-box mb30 data-v-176b31c6"><view class="fs30 col-5 flex-1 data-v-176b31c6">类型</view><view class="flex-box flex-between flex-1 fs30 col-10 data-v-176b31c6"><block xhs:for="{{adultTypeList}}" xhs:for-item="item" xhs:for-index="index" xhs:key="index"><view data-event-opts="{{[['tap',[['bindAdultTypeChange',['$0'],[[['adultTypeList','',index,'id']]]]]]]}}" class="flex-box data-v-176b31c6" bindtap="__e"><block xhs:if="{{item.id==traveler.adult_type}}"><image class="icon_check data-v-176b31c6" src="/static/icon/icon_checkon.png" mode="aspectFit"></image></block><block xhs:else><image class="icon_check data-v-176b31c6" src="/static/icon/icon_check.png" mode="aspectFit"></image></block><view class="data-v-176b31c6">{{item.name}}</view></view></block></view></view><view class="g_input_box flex-box mb30 data-v-176b31c6"><view class="fs30 col-5 data-v-176b31c6">身份证号</view><input class="flex-1 tr fs30 col-10 data-v-176b31c6" maxlength="18" type="idcard" placeholder="请输入身份证号" placeholder-class="col-10" data-event-opts="{{[['input',[['__set_model',['$0','idcard','$event',[]],['traveler']]]]]}}" value="{{traveler.idcard}}" bindinput="__e"/></view><view class="g_input_box flex-box mb30 data-v-176b31c6"><view class="fs30 col-5 data-v-176b31c6">手机号码</view><input class="flex-1 tr fs30 col-10 data-v-176b31c6" maxlength="11" type="number" placeholder="请输入手机号码" placeholder-class="col-10" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['traveler']]]]]}}" value="{{traveler.mobile}}" bindinput="__e"/></view></view></view>