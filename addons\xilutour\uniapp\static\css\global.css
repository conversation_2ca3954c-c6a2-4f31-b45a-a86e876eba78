page,view,scroll-view,swiper,swiper-item,movable-area,movable-view,cover-view,cover-image,icon,text,rich-text,progress,button,checkbox-group,checkbox,form,input,label,picker,picker-view,radio-group,radio,slider,switch,textarea,navigator,functional-page-navigator,image,video,camera,live-player,live-pusher,map,canvas,open-data,web-view,ad{box-sizing: border-box;}
page{word-break: break-all;
  --normal: #05B9AE;
  --border: #E1E1E1;
  --arrow: #999999;
  --nothing: #AAAAAA;
  --red: #F35F4B;
  --blue: #2F5380;
  --green: #08B266;
  --yellow: #EB9003;
  --black: #000000;
  --darkGray: #666666;
  --gray: #999999;
  --ghostWhite: #F2F2F2;
  --white: #ffffff;
	--placeholder:#D7D7D7;
	--bg1: rgba(5,185,174,0.3);
	--bg2: rgba(5,185,174,0.5);
	--price: #D91B00;
}

/* 文章详情正文统一32rpx */
/* 页面布局
    <view class="page-head"></view>
    <view class="page-foot"></view>
    <view class="container"></view> 
*/
page{
	max-width: 800px;
	margin-left: auto;
	margin-right: auto;
}
.container{width: 100%;font-family:'PingFang SC','Microsoft Yahei';line-height: 1.4;font-size: 28rpx;color: var(--black);background-color: Var(--white);overflow-y: scroll;min-height: 100vh;}
.page-head{ position: fixed; left: 0; right: 0; top: 0; z-index: 99;overflow: hidden;}
.page-foot{ position: fixed; left: 0; right: 0; bottom: 0; z-index: 99;}
.page-foot::after{
	content: '';
	position: absolute;
	left: 0;
	right: 0;
	bottom: -60rpx;
	width: 100%;
	height: 60rpx;
	background-color: #fff;
}
.page-foot ~ .container{ padding-bottom: 130rpx;}
/*IPhoneX适配*/
@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
    .container{
        padding-bottom: calc(68rpx/2);
        padding-bottom: calc(constant(safe-area-inset-bottom)/2);
        padding-bottom: calc(env(safe-area-inset-bottom)/2);
    }
		.page-foot{
				bottom: calc(68rpx/2);
        bottom: calc(constant(safe-area-inset-bottom)/2);
        bottom: calc(env(safe-area-inset-bottom)/2);
		}
    .page-foot ~ .container{
        padding-bottom: calc(130rpx + 68rpx);
        padding-bottom: calc(constant(safe-area-inset-bottom) + 130rpx);
        padding-bottom: calc(env(safe-area-inset-bottom) + 130rpx);
    }
}
/* 相对定位 */
.pr{position: relative}
/* 左右浮动 */
.fl{float:left;}
.fr{float:right;}
/* 组件 - 模拟表格布局 */
.table{width:100%;display:table;table-layout:fixed;}
.table-cell{display:table-cell;vertical-align:middle;}
.table-cell.vt{vertical-align:top;}
.table-cell.vb{vertical-align:bottom;}
/*flex布局*/
.flex{display: flex;}
.flex-box{display: flex;align-items: center;}
.flex-1{flex: 1;overflow: hidden;}
.flex-wrap{ flex-wrap: wrap;}
.flex-between{ justify-content: space-between;}
.flex-center{ justify-content: center;}
.flex-end{ justify-content: flex-end;}
.flex-col{flex-direction: column;}
.flex-col-reserve{flex-direction: column-reverse;}
.flex-row{flex-direction: row;}
.flex-row-reserve{flex-direction: row-reverse;}
.flex-align-start{align-items: flex-start;}
.flex-align-end{align-items: flex-end;}
/*清除浮动*/
.clearfix{display:block;}
.clearfix:after{content:" ";display:block;height:0;clear:both;visibility:hidden;overflow:hidden;}
/*1像素边框*/
/* 用法 外层class="m-hairline" 需要设置边框的元素class="m-hairline--left" */
.m-hairline,.m-hairline--bottom,.m-hairline--left,.m-hairline--right,.m-hairline--surround,.m-hairline--top,.m-hairline--top-bottom{position:relative;}
.m-hairline--bottom:after,.m-hairline--left:after,.m-hairline--right:after,.m-hairline--surround:after,.m-hairline--top-bottom:after,.m-hairline--top:after,.m-hairline:after{content:" ";position:absolute;top:0;left:0;width:200%;height:200%;border:0 var(--border) solid;-webkit-transform:scale(.5);transform:scale(.5);-webkit-transform-origin:0 0;transform-origin:0 0;pointer-events:none;box-sizing:border-box;}
.m-hairline--top:after{border-top-width:1px;}
.m-hairline--left:after{border-left-width:1px;}
.m-hairline--right:after{border-right-width:1px;}
.m-hairline--bottom:after{border-bottom-width:1px;}
.m-hairline--top-bottom:after{border-width:1px 0;}
.m-hairline--surround:after{border-width:1px;}
/*阴影*/
.m-shadow{box-shadow:0 1rpx 18rpx 0 rgba(0,0,0,0.06);}
/*分割线*/
.m-baseline{ display: flex; align-items: center; justify-content: center; padding: 30rpx; font-size: 20rpx; color: #e1e1e1; }
.m-baseline:before,
.m-baseline:after{ content: ""; display: block; flex: 1; height: 1px; background-color: currentColor; transform: scaleY(0.5); }
.m-baseline:before{ margin-right: 20rpx; }
.m-baseline:after{ margin-left: 20rpx; }
/*箭头*/
.m-arrow-right,
.m-arrow-down,
.m-arrow-up {position: relative;padding-right: 20rpx;}
.m-arrow-right:after,
.m-arrow-down:after,
.m-arrow-up:after {content: "";display: inline-block;height: 14rpx;width: 14rpx;border-width: 3rpx 3rpx 0 0;border-color: var(--arrow);border-style: solid;position: absolute;top: 50%;right: 4rpx;box-sizing: border-box;}
.m-arrow-right:after {transform: matrix(.71, .71, -.71, .71, 0, 0);margin-top: -6rpx;}
.m-arrow-down:after {transform: matrix(-.71, 0.71, -.71, -.71, 0, 0);margin-top: -15rpx;}
.m-arrow-up:after {transform: matrix(0.71, -.71, 0.71, 0.71, 0, 0);margin-top: -3rpx;}
/*文本省略*/
.m-ellipsis{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}
.m-ellipsis-l2,.m-ellipsis-l3{display:-webkit-box;overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;}
.m-ellipsis-l2{-webkit-line-clamp:2;}
.m-ellipsis-l3{-webkit-line-clamp:3;}
/* 组件 - 表单 */
.form-unify{display:block;width:100%;height:auto;padding:20rpx 24rpx;border:0;background-color:transparent;background-image:none;font-family:'Microsoft YaHei';line-height:1.4;-webkit-border-radius:4px;border-radius:4px;box-sizing:border-box;}
/* 组件 - 提示 */
.hint-num{ position: relative;}
.hint-num text{ display: block; min-width:17rpx; padding: 6rpx 8rpx;  background: var(--red); font-style: normal; text-align: center; font-size: 20rpx; line-height: 20rpx; color: var(--white); border-radius: 100rpx; -webkit-border-radius: 100rpx; position: absolute; top: 0; right: 40rpx; additive-symbols:border-box; }
.hint-txt:after{ content: ""; display: inline-block; width: 12rpx; height: 12rpx; background-color: var(--red);border-radius: 50%; -webkit-border-radius: 50%; vertical-align: top; margin-left: 10rpx; }
/* 红色星号*必填项 */
.m-must::after{ content: "*"; display: inline-block; vertical-align: middle; margin-left: 10rpx; color: var(--red);}
/* 无数据 */
.nothing{padding: 60rpx 0;text-align: center;}
.nothing image{display: block;width: 370rpx;height: 200rpx;margin: 0 auto;}
.nothing text{display: block;margin-top: 30rpx;text-align: center;line-height: 34rpx;font-size: 30rpx;color: var(--nothing);}
.nothing.hide{display: none;}
/* 通用样式 */
.fwb{font-weight: bold}
.tc{text-align: center}
.tr{text-align: right}
.tl{text-align: left}
/* 圆角 */
.br5{border-radius: 5rpx}
.br10{border-radius: 10rpx}
.br15{border-radius: 15rpx}
.br20{border-radius: 20rpx}
.br30{border-radius: 30rpx}
.br40{border-radius: 40rpx}
.br50{border-radius: 50rpx}
.brarc{border-radius: 50%}

/* 字体大小 */
.fs10{font-size: 10rpx}
.fs12{font-size: 12rpx}
.fs14{font-size: 14rpx}
.fs16{font-size:16rpx}
.fs18{font-size:18rpx}
.fs20{font-size: 20rpx}
.fs22{font-size:22rpx}
.fs24{font-size: 24rpx}
.fs26{font-size: 26rpx}
.fs28{font-size: 28rpx}
.fs30{font-size: 30rpx;line-height: 34rpx;}
.fs32{font-size: 32rpx}
.fs34{font-size: 34rpx}
.fs36{font-size: 36rpx;line-height: 40rpx;}
.fs38{font-size: 38rpx}
.fs40{font-size: 40rpx;line-height: 42rpx;}
.fs42{font-size: 42rpx}
.fs44{font-size: 44rpx}
.fs46{font-size: 46rpx}
.fs48{font-size: 48rpx}
.fs50{font-size: 50rpx;}
/* padding */
.p5{padding: 5rpx;}
.p10{padding: 10rpx;}
.p15{padding: 15rpx;}
.p20{padding: 20rpx;}
.p25{padding: 25rpx;}
.p30{padding: 30rpx;}
.p35{padding: 35rpx;}
.p40{padding: 40rpx;}
.p50{padding: 50rpx;}
.pb5{padding-bottom: 5rpx}
.pb10{padding-bottom: 10rpx}
.pb15{padding-bottom: 15rpx}
.pb20{padding-bottom: 20rpx}
.pb25{padding-bottom: 25rpx}
.pb30{padding-bottom: 30rpx}
.pb35{padding-bottom: 35rpx}
.pb40{padding-bottom: 40rpx}
.pt5{padding-top: 5rpx}
.pt10{padding-top: 10rpx}
.pt15{padding-top: 15rpx}
.pt20{padding-top: 20rpx}
.pt25{padding-top: 25rpx}
.pt30{padding-top: 30rpx}
.pt35{padding-top: 35rpx}
.pt40{padding-top: 40rpx}
.pt50{padding-top: 50rpx}
.pt55{padding-top: 55rpx}
.pl5{padding-left: 5rpx}
.pl10{padding-left: 10rpx}
.pl15{padding-left: 15rpx}
.pl20{padding-left: 20rpx}
.pl25{padding-left: 25rpx}
.pl30{padding-left: 30rpx}
.pl35{padding-left: 35rpx}
.pl40{padding-left: 40rpx}
.pr5{padding-right: 5rpx}
.pr10{padding-right: 10rpx}
.pr15{padding-right: 15rpx}
.pr20{padding-right: 20rpx}
.pr25{padding-right: 25rpx}
.pr30{padding-right: 30rpx}
.pr35{padding-right: 35rpx}
.pr40{padding-right: 40rpx}
.pr45{padding-right: 45rpx}
.plr5{padding-left: 5rpx;padding-right: 5rpx}
.plr10{padding-left: 10rpx;padding-right:10rpx}
.plr15{padding-left: 15rpx;padding-right:15rpx}
.plr20{padding-left: 20rpx;padding-right:20rpx}
.plr25{padding-left: 25rpx;padding-right:25rpx}
.plr30{padding-left: 30rpx;padding-right:30rpx}
.plr35{padding-left: 35rpx;padding-right:35rpx}
.plr40{padding-left: 40rpx;padding-right:40rpx}
.plr45{padding-left: 45rpx;padding-right:45rpx}
.plr50{padding-left: 50rpx;padding-right:50rpx}
.ptb5{padding-top: 5rpx;padding-bottom: 5rpx}
.ptb10{padding-top: 10rpx;padding-bottom:10rpx}
.ptb15{padding-top: 15rpx;padding-bottom:15rpx}
.ptb20{padding-top: 20rpx;padding-bottom:20rpx}
.ptb25{padding-top: 25rpx;padding-bottom:25rpx}
.ptb30{padding-top: 30rpx;padding-bottom:30rpx}
.ptb35{padding-top: 35rpx;padding-bottom:35rpx}
.ptb40{padding-top: 40rpx;padding-bottom:40rpx}
.ptb50{padding-top: 50rpx;padding-bottom:50rpx}
/* margin */
.m5{margin: 5rpx;}
.m10{margin: 10rpx;}
.m15{margin: 15rpx;}
.m20{margin: 20rpx;}
.m25{margin: 25rpx;}
.m30{margin: 30rpx;}
.m35{margin: 35rpx;}
.m40{margin: 40rpx;}
.mb5{margin-bottom: 5rpx}
.mb10{margin-bottom: 10rpx}
.mb15{margin-bottom: 15rpx}
.mb20{margin-bottom: 20rpx}
.mb25{margin-bottom: 25rpx}
.mb30{margin-bottom: 30rpx}
.mb35{margin-bottom: 35rpx}
.mb40{margin-bottom: 40rpx}
.mb45{margin-bottom: 45rpx}
.mb50{margin-bottom: 50rpx}
.mb55{margin-bottom: 55rpx}
.mb70{margin-bottom: 70rpx}
.mt5{margin-top: 5rpx}
.mt10{margin-top: 10rpx}
.mt15{margin-top: 15rpx}
.mt20{margin-top: 20rpx}
.mt25{margin-top: 25rpx}
.mt30{margin-top: 30rpx}
.mt35{margin-top: 35rpx}
.mt40{margin-top: 40rpx}
.mt50{margin-top: 50rpx}
.ml5{margin-left: 5rpx}
.ml10{margin-left: 10rpx}
.ml15{margin-left: 15rpx}
.ml20{margin-left: 20rpx}
.ml25{margin-left: 25rpx}
.ml30{margin-left: 30rpx}
.ml35{margin-left: 35rpx}
.ml40{margin-left: 40rpx}
.mr5{margin-right: 5rpx}
.mr10{margin-right: 10rpx}
.mr15{margin-right: 15rpx}
.mr20{margin-right: 20rpx}
.mr25{margin-right: 25rpx}
.mr30{margin-right: 30rpx}
.mr35{margin-right: 35rpx}
.mr40{margin-right: 40rpx}
.mr45{margin-right: 45rpx}
.mr50{margin-right: 50rpx}
.mlrauto{margin-left: auto;margin-right: auto;}
.mlr5{margin-left: 5rpx;margin-right: 5rpx}
.mlr10{margin-left: 10rpx;margin-right:10rpx}
.mlr15{margin-left: 15rpx;margin-right:15rpx}
.mlr20{margin-left: 20rpx;margin-right:20rpx}
.mlr25{margin-left: 25rpx;margin-right:25rpx}
.mlr30{margin-left: 30rpx;margin-right:30rpx}
.mlr35{margin-left: 35rpx;margin-right:35rpx}
.mlr40{margin-left: 40rpx;margin-right:40rpx}
.mtb5{margin-top: 5rpx;margin-bottom: 5rpx}
.mtb10{margin-top: 10rpx;margin-bottom:10rpx}
.mtb15{margin-top: 15rpx;margin-bottom:15rpx}
.mtb20{margin-top: 20rpx;margin-bottom:20rpx}
.mtb25{margin-top: 25rpx;margin-bottom:25rpx}
.mtb30{margin-top: 30rpx;margin-bottom:30rpx}
.mtb35{margin-top: 35rpx;margin-bottom:35rpx}
.mtb40{margin-top: 40rpx;margin-bottom:40rpx}

/* 字体颜色 */
.col-normal {color: var(--normal);}
.col-red {color: var(--red);}
.col-blue {color: var(--blue);}
.col-green {color: var(--green);}
.col-yellow {color: var(--yellow);}
.col-black {color: var(--black);}
.col-darkGray {color: var(--darkGray);}
.col-gray {color: var(--gray);}
.col-f{color: #fff}
.col-c{color: #ccc}
.col-0{color: #000}
.col-3{color: #333}
.col-5{color: #555}
.col-6{color: #666}
.col-89{color: #898989;}
.col-9{color: #999}
.col-10{color: #101010;}
.col-d{color: #ddd}
.col-a{color:#aaa}
.col-price{color: var(--price);}
/* 宽度 */
.w25{width: 25%;}

/* 按钮 */
.container button.m-button{padding: 0;margin: 0;text-align: center;background-color: transparent;border: none;width: auto;}
.container button.m-button::after{border: none;}
/* btn 字体大小颜色 按钮背景颜色 margin */
.g-btn1{margin-left: auto;margin-right: auto; width: 600rpx; height: 100rpx; background: var(--normal); color: var(--white);border-radius: 30rpx;line-height: 100rpx;text-align: center;font-size: 32rpx;}
.g-btn1.middle{width: 315rpx;border-radius: 99rpx;height: 80rpx;line-height: 80rpx;}
.g-btn1.mini{width: 250rpx;border-radius: 99rpx;}

.g-btn2{height: 80rpx;background: var(--bg1);color: var(--white);border-radius: 99rpx;line-height: 80rpx;text-align: center;}


.g-btn3-wrap{
	margin-left: auto;
	margin-right: auto;
	background-color: unset;
	text-align: center;
}

.g-btn3{color: #888;font-size: 26rpx;line-height:87rpx;width: 100%;height: 87rpx;text-align: center;background: unset;} 
.g-btn3.nomore{color: #888;background: unset;}

.g-btn4{background-color: var(--btn4);color: var(--white);} 


/* 背景颜色 (根据项目自行添加) */
.bg-white{background-color: var(--white);}
.bg-ghostWhite{background-color: var(--ghostWhite);}
.bg-1{background: var(--bg1);}
.bg-2{background: var(--bg2);}

/* 自己额外定义的公共样式 */
::-webkit-scrollbar{
	width: 0;
	height: 0;
}

.m-backdrop {
  position: absolute;
  top: 0;
  left: 50%;
	transform: translateX(-50%);
  display: block;
  width: 100%;
  height: auto;
	max-width:800px;
}

.m-header {
  position: fixed;
  top: 0;
  left: 50%;
	transform: translateX(-50%);
  z-index: 99;
  width: 100%;
	max-width:800px;
  overflow: hidden;
}

.g-icon30{
	display: block;
	width: 30rpx;
	height: 30rpx;
}

.g-icon-bg1 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	display: block;
	width: 100%;
	height: auto;
	max-width: 800px;
}

.g-custom-nav {
	position: relative;
}

	.g-custom-nav .message {
		position: relative;
		margin-right: 20rpx;
		font-size: 24rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #555555;
		line-height: 26rpx;
	}

	.g-custom-nav .message.active::after {
		position: absolute;
		right: 0;
		top: 0;
		content: '';
		width: 10rpx;
		height: 10rpx;
		background: #F35F4B;
		border-radius: 50%;
	}

	.g-custom-nav .message image {
		margin: 0 auto 8rpx;
		display: block;
		width: 40rpx;
		height: 40rpx;
	}

	.g-custom-nav .search_box {
		padding: 0 20rpx;
		width: 438rpx;
		height: 65rpx;
		background: #FFFFFF;
		box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
		border-radius: 22rpx;
		border: 2rpx solid #555555;
	}

	.g-custom-nav .addr {
		margin-right: 10rpx;
		font-size: 26rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		color: #101010;
		line-height: 28rpx;
		max-width: 4em;
	}

	.g-custom-nav .icon_arrow {
		display: block;
		width: 18rpx;
		height: 18rpx;
	}

	.g-custom-nav .line {
		margin: 0 20rpx;
		width: 1px;
		height: 20rpx;
		background: #E5E5E5;
	}

	.g-custom-nav .icon_search {
		margin-right: 10rpx;
		display: block;
		width: 26rpx;
		height: 26rpx;
	}

	.g-custom-nav .input {
		font-size: 26rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #333333;
		line-height: 28rpx;
	}

.g-custom-nav .icon_back{
	margin: 0 30rpx 0 0;
	padding: 10rpx 0;
	display: block;
	width: 35rpx;
	height: 55rpx;
}

/*  */

.pay-pop {
	padding: 50rpx 0 0;
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
}
.pay-pop .pay-item {
	padding: 50rpx 30rpx;
}
.pay-pop .icon1 {
	display: block;
	width: 62rpx;
	height: 60rpx;
}
.pay-pop .icon2 {
	width: 38rpx;
	height: 38rpx;
	border-radius: 50%;
}
.pay-pop .pay-item .normal{
	display: block;
}
.pay-pop .pay-item .selected{
	display: none;
}
.pay-pop .pay-item.active .normal{
	display: none;
}
.pay-pop .pay-item.active .selected{
	display: block;
}


.g-success-pop{
	position: relative;
	padding: 195rpx 0 0;
	width: 100vw;
	height: 100vh;
	background-color: #ffffff;
}
.g-success-pop .icon-success{
	margin: 0 auto 35rpx;
	display: block;
	width: 172rpx;
	height: 172rpx;
}
.g-success-pop .g-btn1{
	position: absolute;
	bottom: 60rpx;
	left: 30rpx;
	right: 30rpx;
	width: 690rpx;
}

.foot-line{
	position: fixed;bottom: 2rpx;left: 0;right: 0;height: 1rpx;z-index: 90;
}

@media only screen and (min-width: 800px) {
	.page-foot{
		width: 800px;
		left: 50%;
		right: unset;
		transform: translateX(-50%);
	}
	
	.g-icon-bg1{
		left: 50%;
		right: unset;
		transform: translateX(-50%);
	}
	
	.uni-page-head{
		max-width: 800px;
		width: 800px;
		left:50%!important;
		transform: translateX(-50%);
	}
	
	.uni-tabbar{
		width: 100%;
		max-width: 800px;
		left: 50%!important;
		right:unset!important;
		transform: translateX(-50%)!important;
	}

}

/* 新 */
	.g_input_box {
		padding: 0 30rpx;
		width: 670rpx;
		height: 110rpx;
		background: #F7F9FB;
		border-radius: 30rpx;
	}
	.g_input_box .icon_check{
		margin-right: 15rpx;
		display: block;
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
	}
	
	.g_order_info {
		padding: 40rpx 35rpx 50rpx;
		width: 670rpx;
		background: #F7F9FB;
		border-radius: 25rpx;
	}
	
	/*  */
	
	.g_order_foot1 {
		padding: 20rpx 30rpx 20rpx 40rpx;
		margin: 0 auto;
		width: 690rpx;
		height: 130rpx;
		background: #FFFFFF;
		box-shadow: 0 4rpx 30rpx 5rpx rgba(183, 189, 202, 0.2);
		border-radius: 42rpx;
	}
	.g_order_foot1 .btn1{
		margin: 0 0 0 auto;
		width: 325rpx;
		height: 90rpx;
		background: var(--normal);
		border-radius: 29rpx;
		font-size: 32rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #FFFFFF;
		line-height: 90rpx;
		text-align: center;
	}
	.g_order_foot1 .btn2{
		margin:0 0 0 20rpx;
		padding: 0;
		width: 180rpx;
		height: 80rpx;
		background: rgba(5,185,174,0.1);
		border-radius: 22rpx;
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: var(--normal);
		line-height: 80rpx;
		text-align: center;
	}
	.g_order_foot1 .btn2::after{
		content: none;
	}
	
	.g_order_foot1 .btn3{
		margin:0 0 0 20rpx;
		width: 180rpx;
		height: 80rpx;
		background: var(--normal);
		border-radius: 22rpx;
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #FFFFFF;
		line-height: 80rpx;
		text-align: center;
	}
	
	.g_order_foot1 .nav{
		margin: 0 40rpx 0 0;
		padding: 0;
		font-size: 22rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #898989;
		line-height: 24rpx;
		background-color: #fff;
	}
	.g_order_foot1 .nav::after{
		content: none;
	}
	.g_order_foot1 .icon{
		margin: 0 auto 10rpx;
		display: block;
		width: 45rpx;
		height: 45rpx;
	}
	
	.g_order_foot1 	.btn_apply {
				margin:0 0 0 20rpx;
				width: 180rpx;
				height: 80rpx;
				border-radius: 25rpx;
				border: 1px solid var(--normal);
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: var(--normal);
				line-height: 80rpx;
				text-align: center;
			}
	
	/*  */
	
	.g_order1 {
		margin: 0 0 30rpx;
		padding: 30rpx;
		background: #FFFFFF;
		box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
		border-radius: 20rpx;
	}
	.g_order1 .img{
		margin-right: 30rpx;
		display: block;
		width: 180rpx;
		height: 180rpx;
		border-radius: 15rpx;
	}
	
	.g_order1 .foot_btn1{
		margin:0 0 0 30rpx;
		padding: 0;
		width: 180rpx;
		height: 80rpx;
		background: rgba(5,185,174,0.1);
		border-radius: 22rpx;
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: var(--normal);
		line-height: 80rpx;
		text-align: center;
	}
	.g_order1 .foot_btn1::after{
		content: none;
	}
	
	.g_order1 .foot_btn2{
		margin:0 0 0 30rpx;
		width: 180rpx;
		height: 80rpx;
		background: var(--normal);
		border-radius: 22rpx;
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #FFFFFF;
		line-height: 80rpx;
		text-align: center;
	}
	
	/*  */
	
	.g_tab {
		height: 96rpx;
		overflow-x: scroll;
		white-space: nowrap;
		font-size: 0;
		height: 96rpx;
		overflow-y: hidden;
	}
	::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
	}
	.g_tab::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
	}
	.g_tab .item {
		position: relative;
		padding: 0 40rpx;
		display: inline-block;
		height: 96rpx;
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #101010;
		line-height: 96rpx;
		text-align: center;
	}
	
	.g_tab .item.active {
		font-size: 36rpx;
		font-weight: 500;
		color: var(--normal);
	}
	
	.g_tab .item::after {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 2rpx;
		height: 20rpx;
		background-color: #E5E5E5;
	}
	
	.g_tab .item:first-of-type::after {
		display: none;
	}
	
	/*  */
	
	.g_landscape_list .item {
		margin: 0 0 50rpx;
	}
	
	.g_landscape_list .img {
		position: relative;
		margin: 0 0 30rpx;
		display: block;
		width: 670rpx;
		height: 350rpx;
		border-radius: 25rpx;
	}
	
	.g_landscape_list .img .img1{
		display: block;
		width: 670rpx;
		height: 350rpx;
		border-radius: 25rpx;
	}
	.g_landscape_list .icon_play{
		position: absolute;
		right: 30rpx;
		bottom: 30rpx;
		display: block;
		width: 45rpx;
		height: 45rpx;
	}
	
	.g_landscape_list .level {
		margin-right: 15rpx;
		padding: 0 10rpx;
		height: 40rpx;
		background: #05B9AE;
		border-radius: 10rpx;
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		line-height: 40rpx;
		text-align: center;
	}
	.g_landscape_list .desc{
		font-size: 28rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #5F7997;
		line-height: 30rpx;
	}
	
	.g_feng{
		margin-right: 15rpx;
		padding: 0 10rpx;
		height: 40rpx;
		background: #FFAB29;
		border-radius: 10rpx;
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		line-height: 40rpx;
	}
	
	/*  */
	.g_travel_list .item {
		margin: 0 0 50rpx;
	}
	
	.g_travel_list .img1 {
		margin: 0 20rpx 0 0;
		display: block;
		width: 438rpx;
		height: 438rpx;
		border-radius: 25rpx;
	}
	
	.g_travel_list .img2 {
		display: block;
		width: 210rpx;
		height: 210rpx;
		border-radius: 25rpx;
	}
	
	.g_travel_list .img2+.img2 {
		margin: 20rpx 0 0;
	}
	
	.g_travel_list .level {
		margin-right: 15rpx;
		padding: 0 10rpx;
		height: 40rpx;
		background: #05B9AE;
		border-radius: 10rpx;
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		line-height: 40rpx;
		text-align: center;
	}
	
	.g_travel_list .desc {
		font-size: 28rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #5F7997;
		line-height: 30rpx;
	}
	
	/*  */
	.g_select {
		position: relative;
		width: 750rpx;
		height: 90rpx;
		background: #F7F9FB;
		font-size: 28rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #555555;
		line-height: 90rpx;
	}
	
	.g_select image {
		margin: 0 0 0 5rpx;
		display: block;
		width: 20rpx;
		height: 20rpx;
	}
	.g_select .icon_price{
		height: 22rpx;
	}
	.g_select .active{
		color: var(--normal);
	}
	.g_select .rotate{
		transform: rotate(180deg);
	}
	
	/* 评论 */
	.g_comment {
		margin: 0 0 30rpx;
		padding: 30rpx;
		width: 690rpx;
		background: #FFFFFF;
		box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
		border-radius: 30rpx;
	}
	
	.g_comment .head {
		margin-right: 30rpx;
		display: block;
		width: 90rpx;
		height: 90rpx;
		border-radius: 50%;
	}
	
	.g_comment .text1 {
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #101010;
		line-height: 42rpx;
	}
	
	.g_comment .img1{
		margin: 20rpx 20rpx 0 0;
		display: block;
		width: 196rpx;
		height: 196rpx;
		border-radius: 15rpx;
	}
	.g_comment .img1:nth-of-type(3n){
		margin: 20rpx 0 0;
	}
	
	/*  */
	
	.g_coupon_pop {
		position: relative;
		padding: 30rpx 40rpx;
		width: 750rpx;
		background: #FFFFFF;
		border-radius: 50rpx 50rpx 0 0;
	}
	
	.g_coupon_pop .icon_close {
		position: absolute;
		right: 30rpx;
		top: 20rpx;
		padding: 10rpx;
		display: block;
		width: 44rpx;
		height: 44rpx;
		z-index: 10;
	}
	
	.g_coupon_pop .pop_coupon_wrap{
		max-height: 650rpx;
		overflow-y: scroll;
	}
	
	.g_coupon_pop .pop_coupon {
		position: relative;
		margin: 0 0 30rpx;
		width: 670rpx;
		height: 194rpx;
		color: #FFF;
		
	}
	
	.g_coupon_pop .pop_coupon .bg {
		display: block;
		width: 670rpx;
		height: 194rpx;
	}
	
	.g_coupon_pop .pop_coupon .inner {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
	}
	
	.g_coupon_pop .pop_coupon .left {
		padding: 30rpx 30rpx 0;
		width: 218rpx;
		height: 194rpx;
		text-align: center;
	}
	
	.g_coupon_pop .pop_coupon .man {
		width: 156rpx;
		height: 44rpx;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 22rpx;
		font-size: 24rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #FFFFFF;
		line-height: 44rpx;
	}
	
	.g_coupon_pop .pop_coupon .right {
		padding: 0 30rpx;
		height: 194rpx;
		color: #FDFEFE;
	}
	
	.g_coupon_pop .pop_coupon .use {
		margin: 0 0 0 25rpx;
		width: 120rpx;
		height: 80rpx;
		background: rgba(255, 255, 255, 0.3);
		border-radius: 34rpx;
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #FFFFFF;
		line-height: 80rpx;
		text-align: center;
	}
	
	.g_coupon_pop .pop_coupon .use.active{
		background: #FFFFFF;
	}
	
	.g_coupon_pop .pop_coupon .use.active image{
		margin: 20rpx auto 0;
		display: block;
		width: 40rpx;
		height: 40rpx;
	}
	
	.g_select_wrap{
		position: relative;
		padding: 10rpx 30rpx;
		width: 750rpx;
		max-height: 400rpx;
		background: #F7F9FB;
		overflow-y: scroll;
	}
	.g_select_wrap .item{
		padding: 30rpx 0;
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #101010;
		line-height: 32rpx;
		background: #F7F9FB;
	}
	.g_select_wrap .item.active{
		color: var(--normal);
	}
	.g_mask{
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	background: rgba(0,0,0,0.4);
	height: 100vh;
	z-index: 20;
	}
	
	.rotate{
		transform: rotate(180deg);
	}