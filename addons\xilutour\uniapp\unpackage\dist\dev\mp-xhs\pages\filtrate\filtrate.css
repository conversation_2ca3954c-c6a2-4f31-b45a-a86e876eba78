.xilu .page-foot.data-v-53ec0234 {
  padding: 10rpx 85rpx;
  background-color: #FFF;
}
.xilu .page-foot .btn1.data-v-53ec0234 {
  width: 250rpx;
  height: 90rpx;
  border-radius: 30rpx;
  border: 2rpx solid var(--normal);
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: var(--normal);
  line-height: 88rpx;
  text-align: center;
}
.xilu .page-foot .btn2.data-v-53ec0234 {
  width: 250rpx;
  height: 90rpx;
  border-radius: 30rpx;
  background: var(--normal);
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 90rpx;
  text-align: center;
}
.xilu .container.data-v-53ec0234 {
  padding: 30rpx 40rpx 160rpx !important;
}
.xilu_filtrate.data-v-53ec0234 {
  padding: 0 0 20rpx;
}
.xilu_filtrate .item.data-v-53ec0234 {
  margin: 0 30rpx 30rpx 0;
  padding: 0 6rpx;
  width: 202rpx;
  height: 90rpx;
  background: #F7F9FB;
  border-radius: 20rpx;
  font-size: 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 90rpx;
  text-align: center;
}
.xilu_filtrate .item.data-v-53ec0234:nth-of-type(3n) {
  margin: 0 0 30rpx;
}
.xilu_filtrate .item.active.data-v-53ec0234 {
  background: rgba(5, 185, 174, 0.1);
  border: 2rpx solid var(--normal);
  font-weight: 500;
  color: var(--normal);
}

