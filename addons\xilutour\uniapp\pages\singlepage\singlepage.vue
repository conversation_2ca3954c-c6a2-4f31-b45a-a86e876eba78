<template>
	<view class="xilu">
		<view class="container">
			<view class="title">{{qa.name}}</view>
			<view class="mb30"><rich-text :nodes="qa.content"></rich-text></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				type:'',
				qa:{name: '',view_num: 0, content:'',createtime_text:''}
			};
		},
		onLoad(options) {
			let title = '';
			this.type = options.type;
			if(options.type == "user_agreement"){
				title = "用户协议"
			}else if(options.type == 'privacy_agreement'){
				title = "隐私政策"
			}
			uni.setNavigationBarTitle({
				title: title
			})
			this.fetchDetail()
		},
		methods:{
			fetchDetail(){
				this.$core.get({url: 'xilutour.singlepage/config_page',data: {},loading: false,success: ret => {
						this.qa = this.type == 'user_agreement'?ret.data.user_agreement: ret.data.privacy_agreement;
					},fail: err => {
						console.log(err);
					}
				});
			}
		}
	}
</script>

<style lang="less" scoped>
.xilu {
	.container{
		padding: 30rpx;
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #333333;
		line-height: 44rpx;
	}
	.title{
		margin: 0 0 40rpx;
		font-size: 40rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		color: #101010;
		line-height: 42rpx;
	}
}
</style>
