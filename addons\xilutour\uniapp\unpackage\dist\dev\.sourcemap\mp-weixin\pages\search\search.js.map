{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/search/search.vue?23f5", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/search/search.vue?c369", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/search/search.vue?512c", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/search/search.vue?7648", "uni-app:///pages/search/search.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/search/search.vue?5251", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/search/search.vue?0422"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tourList", "sceneryList", "data", "statusBarHeight", "tabIdx", "showSearch", "currentCity", "query", "q", "searchHistoryList", "total", "tourTotal", "sceneryTotal", "tourListMore", "page", "sceneryListMore", "onLoad", "uni", "onReachBottom", "onUnload", "methods", "navBack", "bindCityChange", "url", "searchConfirm", "title", "icon", "searchHistory", "searchFocus", "searchClick", "tabClick", "refresh", "fetchTourList", "fetchSceneryList"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA+zB,CAAgB,+xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4En1B;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QAAAC;MAAA;MACAC;MAEAC;MACAC;MACAC;MACAZ;MACAa;QAAAC;MAAA;MACAb;MACAc;QAAAD;MAAA;IACA;EACA;EACAE;IACA;IACA;IACA;IACAC;MACAH;MACAA;IACA;IACA;EACA;EACAI;IACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACAF;EACA;EACAG;IACAC;MACAJ;IACA;IACA;IACAK;MACAL;QACAM;MACA;IACA;IACA;IACAC;MACA;MACA;QACAP;UAAAQ;UAAAC;QAAA;QACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACAA;MACAV;MACA;MACA;MACA;IACA;IACAW;MACA;IACA;IACAC;MACA;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;MACA;QAAAjB;MAAA;MACA;MACA;QAAAA;MAAA;MACA;MACA;IACA;IACA;IACAkB;MAAA;MACA;MACAzB;MACA;QACA;QACA;MACA;IACA;IACA;IACA0B;MAAA;MACA;MACA1B;MACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7LA;AAAA;AAAA;AAAA;AAAkhD,CAAgB,m5CAAG,EAAC,C;;;;;;;;;;;ACAtiD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/search/search.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/search/search.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./search.vue?vue&type=template&id=4cedc0c6&scoped=true&\"\nvar renderjs\nimport script from \"./search.vue?vue&type=script&lang=js&\"\nexport * from \"./search.vue?vue&type=script&lang=js&\"\nimport style0 from \"./search.vue?vue&type=style&index=0&id=4cedc0c6&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4cedc0c6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/search/search.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=template&id=4cedc0c6&scoped=true&\"", "var components\ntry {\n  components = {\n    tourList: function () {\n      return import(\n        /* webpackChunkName: \"components/tour-list/tour-list\" */ \"@/components/tour-list/tour-list.vue\"\n      )\n    },\n    sceneryList: function () {\n      return import(\n        /* webpackChunkName: \"components/scenery-list/scenery-list\" */ \"@/components/scenery-list/scenery-list.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"xilu\">\n\t\t<view class=\"container\">\n\t\t\t<view class=\"m-header bg-white\">\n\t\t\t\t<view class=\"g-custom-nav flex-box plr30\"\n\t\t\t\t\t:style=\"{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }\">\n\t\t\t\t\t<image @click=\"navBack\" src=\"/static/icon/icon_back.png\" mode=\"aspectFit\" class=\"icon_back\"></image>\n\t\t\t\t\t<view class=\"search_box flex-box\">\n\t\t\t\t\t\t<view class=\"addr m-ellipsis\" @click=\"bindCityChange()\">{{currentCity?currentCity.name:''}}</view>\n\t\t\t\t\t\t<image class=\"icon_arrow\" src=\"/static/icon/icon_arrow.png\"></image>\n\t\t\t\t\t\t<view class=\"line\"></view>\n\t\t\t\t\t\t<image class=\"icon_search\" src=\"/static/icon/icon_search.png\"></image>\n\t\t\t\t\t\t<input class=\"input flex-1 col-normal\" confirm-type=\"search\" type=\"text\" @confirm=\"searchConfirm()\" v-model=\"query.q\" @focus=\"searchFocus()\" placeholder=\"出发城市/目的地\" placeholder-class=\"cola\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"pr\" :style=\"{ paddingTop: 'calc(90rpx + ' + statusBarHeight + 'px)' }\">\n\t\t\t\t<view class=\"p40\">\n\n\t\t\t\t\t\n\t\t\t\t\t<block v-if=\"showSearch\">\n\t\t\t\t\t\t<!-- <view class=\"xilu_title\">热门搜索</view>\n\t\t\t\t\t\t<view class=\"flex-box flex-wrap flex-align-start\">\n\t\t\t\t\t\t\t<view class=\"xilu_label1\">自然风光</view>\n\t\t\t\t\t\t\t<view class=\"xilu_label1\">山川河流</view>\n\t\t\t\t\t\t\t<view class=\"xilu_label1\">家庭游玩</view>\n\t\t\t\t\t\t\t<view class=\"xilu_label1\">新疆</view>\n\t\t\t\t\t\t\t<view class=\"xilu_label1\">特色木屋</view>\n\t\t\t\t\t\t\t<view class=\"xilu_label1\">自然风光</view>\n\t\t\t\t\t\t\t<view class=\"xilu_label1\">山川河流</view>\n\t\t\t\t\t\t</view> -->\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"xilu_title\">历史搜索</view>\n\t\t\t\t\t\t<view class=\"flex-box flex-wrap flex-align-start\">\n\t\t\t\t\t\t\t<view class=\"xilu_label2\" @click=\"searchClick(item)\" v-for=\"(item,index) in searchHistoryList\" :key=\"index\">{{item}}</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t</block>\n\n\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t<view class=\"xilu_top_box\">\n\t\t\t\t\t\t\t<view class=\"fs30 col-5\">共<text class=\"col-normal\">{{total}}</text>个内容</view>\n\t\t\t\t\t\t\t<view class=\"g_tab flex-box\">\n\t\t\t\t\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 1}\" @click=\"tabClick(1)\">路线</view>\n\t\t\t\t\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 2}\" @click=\"tabClick(2)\">景点</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<view class=\"g_travel_list\" v-if=\"tabIdx==1\">\n\t\t\t\t\t\t\t<tour-list :tourList=\"tourList\"></tour-list>\n\t\t\t\t\t\t\t<view class=\"g-btn3-wrap\">\n\t\t\t\t\t\t\t\t<view class=\"g-btn3\" @click=\"fetchTourList\">{{tourListMore.text}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<view class=\"g_travel_list\" v-if=\"tabIdx==2\">\n\t\t\t\t\t\t\t<scenery-list :sceneryList=\"sceneryList\"></scenery-list>\n\t\t\t\t\t\t\t<view class=\"g-btn3-wrap\">\n\t\t\t\t\t\t\t\t<view class=\"g-btn3\" @click=\"fetchSceneryList\">{{sceneryListMore.text}}</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\n\t\t\t\t\t</block>\n\n\n\t\t\t\t</view>\n\n\t\t\t</view>\n\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\tconst app = getApp();\n\timport tourList from '@/components/tour-list/tour-list.vue';\n\timport sceneryList from '@/components/scenery-list/scenery-list.vue'\n\texport default {\n\t\tcomponents: {\n\t\t\ttourList,\n\t\t\tsceneryList\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tstatusBarHeight: 20,\n\t\t\t\ttabIdx: 1,\n\t\t\t\tshowSearch:true,\n\t\t\t\tcurrentCity: null,\n\t\t\t\tquery:{q:''},\n\t\t\t\tsearchHistoryList: [],\n\t\t\t\t\n\t\t\t\ttotal:0,\n\t\t\t\ttourTotal: 0,\n\t\t\t\tsceneryTotal:0,\n\t\t\t\ttourList: [],\n\t\t\t\ttourListMore: {page: 1},\n\t\t\t\tsceneryList:[],\n\t\t\t\tsceneryListMore:{page:1},\n\t\t\t};\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.statusBarHeight = getApp().globalData.statusBarHeight;\n\t\t\tlet page = this;\n\t\t\tthis.currentCity = this.$core.getCurrentCity();\n\t\t\tuni.$on(app.globalData.Event.CurrentCityChange, function(currentCity) {\n\t\t\t\tpage.currentCity = currentCity;\n\t\t\t\tpage.refresh();\n\t\t\t});\n\t\t\tthis.searchHistoryList = uni.getStorageSync('search_history') || [];\n\t\t},\n\t\tonReachBottom() {\n\t\t\tif(this.tabIdx == 1){\n\t\t\t\tthis.fetchTourList();\n\t\t\t}else if(this.tabIdx == 2){\n\t\t\t\tthis.fetchSceneryList();\n\t\t\t}\n\t\t},\n\t\tonUnload() {\n\t\t\tuni.$off(app.globalData.Event.CurrentCityChange,this);\n\t\t},\n\t\tmethods: {\n\t\t\tnavBack(){\n\t\t\t\tuni.navigateBack({})\n\t\t\t},\n\t\t\t//更换城市\n\t\t\tbindCityChange(){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t    url: '/pages/change_city/change_city'\n\t\t\t\t})\n\t\t\t},\n\t\t\t//搜索\n\t\t\tsearchConfirm() {\n\t\t\t\tlet q = this.query.q.trim();\n\t\t\t\tif (q == '') {\n\t\t\t\t\tuni.showToast({title:\"请输入搜索内容\",icon:'none'});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t//搜索列表存缓存\n\t\t\t\tlet searchHistory = this.searchHistoryList;\n\t\t\t\tlet index = searchHistory.indexOf(q);\n\t\t\t\tif (index !== -1) {\n\t\t\t\t\tsearchHistory.splice(index, 1);\n\t\t\t\t}\n\t\t\t\tsearchHistory.unshift(q.toString());\n\t\t\t\tuni.setStorageSync('search_history', searchHistory);\n\t\t\t\tthis.searchHistoryList = searchHistory;\n\t\t\t\tthis.showSearch = false;\n\t\t\t\tthis.refresh();\n\t\t\t},\n\t\t\tsearchFocus(){\n\t\t\t\tthis.showSearch = true;\n\t\t\t},\n\t\t\tsearchClick(e){\n\t\t\t\tthis.query.q = e;\n\t\t\t\tthis.searchConfirm();\n\t\t\t},\n\t\t\t\n\t\t\ttabClick(i) {\n\t\t\t\tthis.tabIdx = i;\n\t\t\t},\n\t\t\trefresh(){\n\t\t\t\tthis.tourList = [];\n\t\t\t\tthis.tourListMore = {page:1};\n\t\t\t\tthis.sceneryList = [];\n\t\t\t\tthis.sceneryListMore = {page:1};\n\t\t\t\tthis.fetchTourList();\n\t\t\t\tthis.fetchSceneryList();\n\t\t\t},\n\t\t\t//线路\n\t\t\tfetchTourList() {\n\t\t\t\tlet query = this.query;\n\t\t\t\tquery.pagesize = 10;\n\t\t\t\tthis.$util.fetch(this, 'xilutour.tour/lists', query, 'tourListMore', 'tourList', 'data', data => {\n\t\t\t\t\tthis.tourTotal = data.total;\n\t\t\t\t\tthis.total = this.tourTotal+ this.sceneryTotal;\n\t\t\t\t})\n\t\t\t},\n\t\t\t//景点\n\t\t\tfetchSceneryList() {\n\t\t\t\tlet query = this.query;\n\t\t\t\tquery.pagesize = 10;\n\t\t\t\tthis.$util.fetch(this, 'xilutour.scenery/lists', query, 'sceneryListMore', 'sceneryList', 'data', data=>{\n\t\t\t\t\tthis.sceneryTotal = data.total;\n\t\t\t\t\tthis.total = this.tourTotal+ this.sceneryTotal;\n\t\t\t\t})\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"less\" scoped>\n\t.g-custom-nav .input {\n\t\tcolor: var(--normal);\n\t}\n\n\t.g_tab {\n\t\tmargin-left: -40rpx;\n\t}\n\n\t.xilu {\n\t\t.xilu_title {\n\t\t\tmargin: 0 0 30rpx;\n\t\t\tfont-size: 34rpx;\n\t\t\tfont-family: PingFangSC, PingFang SC;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #101010;\n\t\t\tline-height: 36rpx;\n\t\t}\n\n\t\t.xilu_label1 {\n\t\t\tmargin: 0 30rpx 30rpx 0;\n\t\t\tpadding: 0 15rpx;\n\t\t\theight: 58rpx;\n\t\t\tbackground: rgba(5, 185, 174, 0.1);\n\t\t\tborder-radius: 8rpx;\n\t\t\tfont-size: 30rpx;\n\t\t\tfont-family: PingFangSC, PingFang SC;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: var(--normal);\n\t\t\tline-height: 58rpx;\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t.xilu_label2 {\n\t\t\tmargin: 0 30rpx 30rpx 0;\n\t\t\tpadding: 0 15rpx;\n\t\t\theight: 58rpx;\n\t\t\tbackground: #F7F9FB;\n\t\t\tborder-radius: 8rpx;\n\t\t\tfont-size: 30rpx;\n\t\t\tfont-family: PingFangSC, PingFang SC;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #555555;\n\t\t\tline-height: 58rpx;\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t.xilu_top_box {}\n\t}\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&id=4cedc0c6&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&id=4cedc0c6&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341199\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}