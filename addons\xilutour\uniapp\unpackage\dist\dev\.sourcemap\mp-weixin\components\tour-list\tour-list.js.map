{"version": 3, "sources": ["webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/components/tour-list/tour-list.vue?ce37", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/components/tour-list/tour-list.vue?6ab2", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/components/tour-list/tour-list.vue?e988", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/components/tour-list/tour-list.vue?9655", "uni-app:///components/tour-list/tour-list.vue"], "names": ["name", "props", "tourList", "type", "default", "data"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;;;AAGxD;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAk0B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8Bt1B;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA,QAEA;EACA;AACA;AAAA,2B", "file": "components/tour-list/tour-list.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tour-list.vue?vue&type=template&id=7ec7086c&\"\nvar renderjs\nimport script from \"./tour-list.vue?vue&type=script&lang=js&\"\nexport * from \"./tour-list.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/tour-list/tour-list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tour-list.vue?vue&type=template&id=7ec7086c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.tourList, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.images_text.length\n    var g1 = g0 > 0 ? item.images_text.length : null\n    var g2 = g0 > 0 && g1 > 1 ? item.images_text.length : null\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n      g2: g2,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tour-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tour-list.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<navigator class=\"item\" :url=\"'/pages/travel_detail/travel_detail?id='+item.id\" hover-class=\"none\" v-for=\"item in tourList\">\n\t\t\t\t<view class=\"mb20\">\n\t\t\t\t\t<text class=\"level\" v-if=\"item.level\">{{item.level.name}}</text>\n\t\t\t\t\t<text class=\"g_feng\">{{item.points}}</text>\n\t\t\t\t\t<text class=\"fs36 col-10\">{{item.name}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"desc m-ellipsis mb15\">{{item.sub_name}}</view>\n\t\t\t\t<view class=\"flex-box mb20\">\n\t\t\t\t\t<view class=\"flex-1 col-price\" v-if=\"item.tour_date\">\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\n\t\t\t\t\t\t<text class=\"fs40\">{{item.tour_date.salesprice}}</text>\n\t\t\t\t\t\t<text class=\"fs30\">起</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"fs28 col-a\">{{item.view_count}}人浏览</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"img_wrap flex-box flex-align-start\" v-if=\"item.images_text.length>0\">\n\t\t\t\t\t<image :src=\"item.images_text[0]\" mode=\"aspectFill\" class=\"img1\"></image>\n\t\t\t\t\t<view class=\"flex-1\" v-if=\"item.images_text.length>1\">\n\t\t\t\t\t\t<image :src=\"item.images_text[1]\" mode=\"aspectFill\" class=\"img2\"></image>\n\t\t\t\t\t\t<image  v-if=\"item.images_text.length>2\" :src=\"item.images_text[2]\" mode=\"aspectFill\" class=\"img2\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</navigator>\n\t\t\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tname:\"tour-list\",\n\t\tprops:{\n\t\t\ttourList:{\n\t\t\t\ttype: Array,\n\t\t\t\tdefault:[]\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t};\n\t\t}\n\t}\n</script>\n\n<style>\n\n</style>"], "sourceRoot": ""}