(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["uni_modules/uni-transition/components/uni-transition/uni-transition"],{

/***/ 459:
/*!***************************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-transition/components/uni-transition/uni-transition.vue ***!
  \***************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _uni_transition_vue_vue_type_template_id_6369f8c4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./uni-transition.vue?vue&type=template&id=6369f8c4& */ 460);
/* harmony import */ var _uni_transition_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./uni-transition.vue?vue&type=script&lang=js& */ 462);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _uni_transition_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _uni_transition_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 31);

var renderjs




/* normalize component */

var component = Object(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _uni_transition_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _uni_transition_vue_vue_type_template_id_6369f8c4___WEBPACK_IMPORTED_MODULE_0__["render"],
  _uni_transition_vue_vue_type_template_id_6369f8c4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _uni_transition_vue_vue_type_template_id_6369f8c4___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "uni_modules/uni-transition/components/uni-transition/uni-transition.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 460:
/*!**********************************************************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-transition/components/uni-transition/uni-transition.vue?vue&type=template&id=6369f8c4& ***!
  \**********************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_uni_transition_vue_vue_type_template_id_6369f8c4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=template&id=6369f8c4& */ 461);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_uni_transition_vue_vue_type_template_id_6369f8c4___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_uni_transition_vue_vue_type_template_id_6369f8c4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_uni_transition_vue_vue_type_template_id_6369f8c4___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_uni_transition_vue_vue_type_template_id_6369f8c4___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 461:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-transition/components/uni-transition/uni-transition.vue?vue&type=template&id=6369f8c4& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 462:
/*!****************************************************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-transition/components/uni-transition/uni-transition.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_uni_transition_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=script&lang=js& */ 463);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_uni_transition_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_uni_transition_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_uni_transition_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_uni_transition_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_uni_transition_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 463:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-transition/components/uni-transition/uni-transition.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 3);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 19));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 15));
var _createAnimation = __webpack_require__(/*! ./createAnimation */ 464);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
/**
 * Transition 过渡动画
 * @description 简单过渡动画组件
 * @tutorial https://ext.dcloud.net.cn/plugin?id=985
 * @property {Boolean} show = [false|true] 控制组件显示或隐藏
 * @property {Array|String} modeClass = [fade|slide-top|slide-right|slide-bottom|slide-left|zoom-in|zoom-out] 过渡动画类型
 *  @value fade 渐隐渐出过渡
 *  @value slide-top 由上至下过渡
 *  @value slide-right 由右至左过渡
 *  @value slide-bottom 由下至上过渡
 *  @value slide-left 由左至右过渡
 *  @value zoom-in 由小到大过渡
 *  @value zoom-out 由大到小过渡
 * @property {Number} duration 过渡动画持续时间
 * @property {Object} styles 组件样式，同 css 样式，注意带’-‘连接符的属性需要使用小驼峰写法如：`backgroundColor:red`
 */
var _default2 = {
  name: 'uniTransition',
  emits: ['click', 'change'],
  props: {
    show: {
      type: Boolean,
      default: false
    },
    modeClass: {
      type: [Array, String],
      default: function _default() {
        return 'fade';
      }
    },
    duration: {
      type: Number,
      default: 300
    },
    styles: {
      type: Object,
      default: function _default() {
        return {};
      }
    },
    customClass: {
      type: String,
      default: ''
    }
  },
  data: function data() {
    return {
      isShow: false,
      transform: '',
      opacity: 1,
      animationData: {},
      durationTime: 300,
      config: {}
    };
  },
  watch: {
    show: {
      handler: function handler(newVal) {
        if (newVal) {
          this.open();
        } else {
          // 避免上来就执行 close,导致动画错乱
          if (this.isShow) {
            this.close();
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    // 生成样式数据
    stylesObject: function stylesObject() {
      var styles = _objectSpread(_objectSpread({}, this.styles), {}, {
        'transition-duration': this.duration / 1000 + 's'
      });
      var transform = '';
      for (var i in styles) {
        var line = this.toLine(i);
        transform += line + ':' + styles[i] + ';';
      }
      return transform;
    },
    // 初始化动画条件
    transformStyles: function transformStyles() {
      return 'transform:' + this.transform + ';' + 'opacity:' + this.opacity + ';' + this.stylesObject;
    }
  },
  created: function created() {
    // 动画默认配置
    this.config = {
      duration: this.duration,
      timingFunction: 'ease',
      transformOrigin: '50% 50%',
      delay: 0
    };
    this.durationTime = this.duration;
  },
  methods: {
    /**
     *  ref 触发 初始化动画
     */
    init: function init() {
      var obj = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      if (obj.duration) {
        this.durationTime = obj.duration;
      }
      this.animation = (0, _createAnimation.createAnimation)(Object.assign(this.config, obj), this);
    },
    /**
     * 点击组件触发回调
     */
    onClick: function onClick() {
      this.$emit('click', {
        detail: this.isShow
      });
    },
    /**
     * ref 触发 动画分组
     * @param {Object} obj
     */
    step: function step(obj) {
      var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      if (!this.animation) return;
      for (var i in obj) {
        try {
          if ((0, _typeof2.default)(obj[i]) === 'object') {
            var _this$animation;
            (_this$animation = this.animation)[i].apply(_this$animation, (0, _toConsumableArray2.default)(obj[i]));
          } else {
            this.animation[i](obj[i]);
          }
        } catch (e) {
          console.error("\u65B9\u6CD5 ".concat(i, " \u4E0D\u5B58\u5728"));
        }
      }
      this.animation.step(config);
      return this;
    },
    /**
     *  ref 触发 执行动画
     */
    run: function run(fn) {
      if (!this.animation) return;
      this.animation.run(fn);
    },
    // 开始过度动画
    open: function open() {
      var _this = this;
      clearTimeout(this.timer);
      this.transform = '';
      this.isShow = true;
      var _this$styleInit = this.styleInit(false),
        opacity = _this$styleInit.opacity,
        transform = _this$styleInit.transform;
      if (typeof opacity !== 'undefined') {
        this.opacity = opacity;
      }
      this.transform = transform;
      // 确保动态样式已经生效后，执行动画，如果不加 nextTick ，会导致 wx 动画执行异常
      this.$nextTick(function () {
        // TODO 定时器保证动画完全执行，目前有些问题，后面会取消定时器
        _this.timer = setTimeout(function () {
          _this.animation = (0, _createAnimation.createAnimation)(_this.config, _this);
          _this.tranfromInit(false).step();
          _this.animation.run();
          _this.$emit('change', {
            detail: _this.isShow
          });
        }, 20);
      });
    },
    // 关闭过度动画
    close: function close(type) {
      var _this2 = this;
      if (!this.animation) return;
      this.tranfromInit(true).step().run(function () {
        _this2.isShow = false;
        _this2.animationData = null;
        _this2.animation = null;
        var _this2$styleInit = _this2.styleInit(false),
          opacity = _this2$styleInit.opacity,
          transform = _this2$styleInit.transform;
        _this2.opacity = opacity || 1;
        _this2.transform = transform;
        _this2.$emit('change', {
          detail: _this2.isShow
        });
      });
    },
    // 处理动画开始前的默认样式
    styleInit: function styleInit(type) {
      var _this3 = this;
      var styles = {
        transform: ''
      };
      var buildStyle = function buildStyle(type, mode) {
        if (mode === 'fade') {
          styles.opacity = _this3.animationType(type)[mode];
        } else {
          styles.transform += _this3.animationType(type)[mode] + ' ';
        }
      };
      if (typeof this.modeClass === 'string') {
        buildStyle(type, this.modeClass);
      } else {
        this.modeClass.forEach(function (mode) {
          buildStyle(type, mode);
        });
      }
      return styles;
    },
    // 处理内置组合动画
    tranfromInit: function tranfromInit(type) {
      var _this4 = this;
      var buildTranfrom = function buildTranfrom(type, mode) {
        var aniNum = null;
        if (mode === 'fade') {
          aniNum = type ? 0 : 1;
        } else {
          aniNum = type ? '-100%' : '0';
          if (mode === 'zoom-in') {
            aniNum = type ? 0.8 : 1;
          }
          if (mode === 'zoom-out') {
            aniNum = type ? 1.2 : 1;
          }
          if (mode === 'slide-right') {
            aniNum = type ? '100%' : '0';
          }
          if (mode === 'slide-bottom') {
            aniNum = type ? '100%' : '0';
          }
        }
        _this4.animation[_this4.animationMode()[mode]](aniNum);
      };
      if (typeof this.modeClass === 'string') {
        buildTranfrom(type, this.modeClass);
      } else {
        this.modeClass.forEach(function (mode) {
          buildTranfrom(type, mode);
        });
      }
      return this.animation;
    },
    animationType: function animationType(type) {
      return {
        fade: type ? 1 : 0,
        'slide-top': "translateY(".concat(type ? '0' : '-100%', ")"),
        'slide-right': "translateX(".concat(type ? '0' : '100%', ")"),
        'slide-bottom': "translateY(".concat(type ? '0' : '100%', ")"),
        'slide-left': "translateX(".concat(type ? '0' : '-100%', ")"),
        'zoom-in': "scaleX(".concat(type ? 1 : 0.8, ") scaleY(").concat(type ? 1 : 0.8, ")"),
        'zoom-out': "scaleX(".concat(type ? 1 : 1.2, ") scaleY(").concat(type ? 1 : 1.2, ")")
      };
    },
    // 内置动画类型与实际动画对应字典
    animationMode: function animationMode() {
      return {
        fade: 'opacity',
        'slide-top': 'translateY',
        'slide-right': 'translateX',
        'slide-bottom': 'translateY',
        'slide-left': 'translateX',
        'zoom-in': 'scale',
        'zoom-out': 'scale'
      };
    },
    // 驼峰转中横线
    toLine: function toLine(name) {
      return name.replace(/([A-Z])/g, '-$1').toLowerCase();
    }
  }
};
exports.default = _default2;

/***/ })

}]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uni-transition/components/uni-transition/uni-transition-create-component',
    {
        'uni_modules/uni-transition/components/uni-transition/uni-transition-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('1')['createComponent'](__webpack_require__(459))
        })
    },
    [['uni_modules/uni-transition/components/uni-transition/uni-transition-create-component']]
]);
