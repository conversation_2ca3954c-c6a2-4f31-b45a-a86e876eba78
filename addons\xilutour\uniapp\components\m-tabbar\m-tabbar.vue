<template>
	<view>
		<u-tabbar :value="footText" @change="footChange" inactiveColor="#898989" activeColor="#05B9AE" :fixed="true"
			:placeholder="false" :safeAreaInsetBottom="true" :border="false" :zIndex="100">
			<u-tabbar-item name="首页" text="首页"
				:icon="'/static/icon/icon_foot1' + (footIdx == 1 ? '_on' : '') + '.png'"></u-tabbar-item>
			<u-tabbar-item name="路线" text="路线"
				:icon="'/static/icon/icon_foot2' + (footIdx == 2 ? '_on' : '') + '.png'"></u-tabbar-item>
			<u-tabbar-item name="景点" text="景点"
				:icon="'/static/icon/icon_foot3' + (footIdx == 3 ? '_on' : '') + '.png'"></u-tabbar-item>
			<u-tabbar-item name="我的" text="我的"
				:icon="'/static/icon/icon_foot4' + (footIdx == 4 ? '_on' : '') + '.png'"></u-tabbar-item>
		</u-tabbar>
	</view>
</template>

<script>
	export default {
		name: "m-tabbar",
		data() {
			return {

			};
		},
		props: {
			footIdx: {
				type: Number,
				default: 1
			},
			footText: {
				type: String
			}
		},
		methods: {
			footChange(e) {
				console.log(e);
				switch (e) {
					case '首页':
						uni.switchTab({
							url: '/pages/index/index'
						})
						break;
					case '路线':
						uni.switchTab({
							url: '/pages/travel/travel'
						})
						break;
					case '景点':
						uni.switchTab({
							url: '/pages/landscape/landscape'
						})
						break;
					case '我的':
						uni.switchTab({
							url: '/pages/personal_center/personal_center'
						})
						break;
				}
			},
		}
	}
</script>

<style>

</style>