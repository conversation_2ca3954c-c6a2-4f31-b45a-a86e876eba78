{"version": 3, "sources": ["webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/liu-waterfall/components/liu-waterfall/liu-waterfall.vue?132f", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/liu-waterfall/components/liu-waterfall/liu-waterfall.vue?d21b", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/liu-waterfall/components/liu-waterfall/liu-waterfall.vue?d973", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/liu-waterfall/components/liu-waterfall/liu-waterfall.vue?2e20", "uni-app:///uni_modules/liu-waterfall/components/liu-waterfall/liu-waterfall.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/liu-waterfall/components/liu-waterfall/liu-waterfall.vue?03ae", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/liu-waterfall/components/liu-waterfall/liu-waterfall.vue?ba52"], "names": ["props", "dataList", "type", "required", "default", "column", "margin", "radius", "bgColor", "cardBgColor", "data", "columnData", "columnWidth", "loading", "loginShow", "h5LoginShow", "islogin", "posting", "watch", "immediate", "deep", "handler", "methods", "getElemHeight", "uni", "src", "success", "item", "boundingClientRect", "arr", "itemHeight", "distributeToNArrays", "arrays", "sums", "setColumn<PERSON><PERSON><PERSON>", "width", "setData", "acc", "Array", "click"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACqC;;;AAGjG;AACqN;AACrN,gBAAgB,gNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAo2B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsBx3B;EACAA;IACA;IACAC;MACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAH;MACAC;MACAC;IACA;IACA;IACAE;MACAJ;MACAE;IACA;IACA;IACAG;MACAL;MACAE;IACA;IACA;IACAI;MACAN;MACAE;IACA;IACA;IACAK;MACAP;MACAE;IACA;EACA;EACAM;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAjB;MACAkB;MACAC;MACAC;QAAA;QACA;UACA;UACA;QACA;MACA;IACA;IACAhB;MACAc;MACAC;MACAC;QAAA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACA;QACA;UACAC;YACAC;YACAC;cACAC;cACA,uDACAC;gBACAC,yCACA;kBACAC;gBACA,IACAH,MACA;gBACA;kBACA,oDACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAI;MACA;MACA,kBACA;QACA;QACA;QACAC;QACAC;QACA;MACA,GACA;QAAA;MAAA,GACA;IACA;IACAC;MAAA;MACA;MACA;MACA,gDACAN;QACAO;QACA;MACA;IAEA;IACAC;MAAA;MACA,uCACA;QACA;QACAC;QACA;MACA,GACAC;QAAA;MAAA,GACA;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7JA;AAAA;AAAA;AAAA;AAAmvC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACAvwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/liu-waterfall/components/liu-waterfall/liu-waterfall.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./liu-waterfall.vue?vue&type=template&id=64a760e9&scoped=true&\"\nvar renderjs\nimport script from \"./liu-waterfall.vue?vue&type=script&lang=js&\"\nexport * from \"./liu-waterfall.vue?vue&type=script&lang=js&\"\nimport style0 from \"./liu-waterfall.vue?vue&type=style&index=0&id=64a760e9&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"64a760e9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/liu-waterfall/components/liu-waterfall/liu-waterfall.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-waterfall.vue?vue&type=template&id=64a760e9&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-waterfall.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-waterfall.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"waterfall\" id=\"waterfall\" :style=\"'background-color:'+bgColor\">\n\t\t<!-- :style=\"{ width: columnWidth + 'px' }\" -->\n\t\t<view v-for=\"(item, index) in columnData\" :key=\"index\" class=\"column\">\n\t\t\t<view v-for=\"(childItem, childIndex) in item\" :key=\"childIndex\" style=\"width: 100%\" :id=\"'item' + childItem.id\"\n\t\t\t\t>\n\t\t\t\t<navigator class=\"item\"\n\t\t\t\t\t:style=\"'background-color:'+cardBgColor+';margin:'+margin+'rpx;border-radius:'+radius+'rpx;'\" hover-class=\"none\" :url=\"'/pages/article_detail/article_detail?id='+childItem.id\">\n\t\t\t\t\t<image :src=\"childItem.thumb_image_text\" mode=\"widthFix\" lazy-load\n\t\t\t\t\t\t:style=\"{height:childItem.height,width: '100%'}\">\n\t\t\t\t\t</image>\n\t\t\t\t\t<view class=\"title-info\">\n\t\t\t\t\t\t<view class=\"item-title\">{{ childItem.name }}</view>\n\t\t\t\t\t\t<!-- <view class=\"item-desc\">{{ childItem.desc }}</view> -->\n\t\t\t\t\t</view>\n\t\t\t\t</navigator>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tprops: {\n\t\t\t//数据源\n\t\t\tdataList: {\n\t\t\t\ttype: Array,\n\t\t\t\trequired: true,\n\t\t\t\tdefault: []\n\t\t\t},\n\t\t\t//显示列数\n\t\t\tcolumn: {\n\t\t\t\ttype: Number,\n\t\t\t\trequired: true,\n\t\t\t\tdefault: 2\n\t\t\t},\n\t\t\t//卡片margin(rpx)\n\t\t\tmargin: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 10\n\t\t\t},\n\t\t\t//卡片圆角(rpx)\n\t\t\tradius: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 8\n\t\t\t},\n\t\t\t//页面背景颜色\n\t\t\tbgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'unset'\n\t\t\t},\n\t\t\t//卡片背景颜色\n\t\t\tcardBgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#FFFFFF'\n\t\t\t},\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcolumnData: [],\n\t\t\t\tcolumnWidth: 0,\n\t\t\t\tloading: false,\n\t\t\t\tloginShow: false, //登录弹框\n\t\t\t\th5LoginShow: false,\n\t\t\t\tislogin: false,\n\t\t\t\tposting:false\n\t\t\t};\n\t\t},\n\t\twatch: {\n\t\t\tdataList: {\n\t\t\t\timmediate: true,\n\t\t\t\tdeep: true,\n\t\t\t\thandler(newValue, oldValue) {\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.setColumnWidth()\n\t\t\t\t\t\tthis.setData()\n\t\t\t\t\t})\n\t\t\t\t},\n\t\t\t},\n\t\t\tcolumn: {\n\t\t\t\timmediate: false,\n\t\t\t\tdeep: true,\n\t\t\t\thandler(newValue) {\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.setColumnWidth()\n\t\t\t\t\t\tthis.setData()\n\t\t\t\t\t})\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\tmethods: {\n\t\t\t//计算每列的高度\n\t\t\tgetElemHeight(index) {\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tvar arr = [];\n\t\t\t\t\tthis.dataList.map((item, index) => {\n\t\t\t\t\t\tuni.getImageInfo({\n\t\t\t\t\t\t\tsrc: item.picUrl,\n\t\t\t\t\t\t\tsuccess: (e) => {\n\t\t\t\t\t\t\t\titem.height = (e.height * (this.columnWidth / e.width)) + 'px'\n\t\t\t\t\t\t\t\tthis.createSelectorQuery().select('#item' + item.id)\n\t\t\t\t\t\t\t\t\t.boundingClientRect(res => {\n\t\t\t\t\t\t\t\t\t\tarr.push({\n\t\t\t\t\t\t\t\t\t\t\t...{\n\t\t\t\t\t\t\t\t\t\t\t\titemHeight: res.height\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t\t...item\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\tif (arr.length == this.dataList.length) {\n\t\t\t\t\t\t\t\t\t\t\tthis.columnData = this.distributeToNArrays(arr,\n\t\t\t\t\t\t\t\t\t\t\t\tthis.column);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}).exec();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t\tdistributeToNArrays(arr, n) {\n\t\t\t\tlet sums = new Array(n).fill(0);\n\t\t\t\treturn arr.reduce(\n\t\t\t\t\t(arrays, item) => {\n\t\t\t\t\t\tlet minSum = Math.min(...sums);\n\t\t\t\t\t\tlet minIndex = sums.indexOf(minSum);\n\t\t\t\t\t\tarrays[minIndex].push(item);\n\t\t\t\t\t\tsums[minIndex] += item.itemHeight;\n\t\t\t\t\t\treturn arrays;\n\t\t\t\t\t},\n\t\t\t\t\tnew Array(n).fill().map(() => []),\n\t\t\t\t)\n\t\t\t},\n\t\t\tsetColumnWidth() {\n\t\t\t\t// let width = uni.getSystemInfoSync().windowWidth\n\t\t\t\tlet width;\n\t\t\t\tthis.createSelectorQuery().select('#waterfall')\n\t\t\t\t\t.boundingClientRect(res => {\n\t\t\t\t\t\twidth = res.width\n\t\t\t\t\t\tthis.columnWidth = Math.floor(width / this.column)\n\t\t\t\t\t}).exec();\n\n\t\t\t},\n\t\t\tsetData() {\n\t\t\t\tconst resultArray = this.dataList.reduce(\n\t\t\t\t\t(acc, cur, index) => {\n\t\t\t\t\t\tconst targetIndex = index % this.column;\n\t\t\t\t\t\tacc[targetIndex].push(cur);\n\t\t\t\t\t\treturn acc;\n\t\t\t\t\t},\n\t\t\t\t\tArray.from(Array(this.column), () => []),\n\t\t\t\t);\n\t\t\t\tthis.columnData = resultArray;\n\t\t\t\tthis.getElemHeight()\n\t\t\t},\n\t\t\tclick(index) {\n\t\t\t\tthis.$emit('click', index)\n\t\t\t}\n\t\t},\n\t};\n</script>\n\n<style scoped>\n\t.waterfall {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.waterfall .column {\n\t\twidth: 335rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t}\n\t\n\t.waterfall .column + .column{\n\t\tmargin-left: 20rpx;\n\t}\n\n\t.waterfall .item {\n\t\tmargin-bottom: 20rpx!important;\n\t\toverflow: hidden;\n\t}\n\n\t.waterfall .title-info {\n\t\tpadding: 0rpx 20rpx 20rpx 20rpx;\n\t}\n\n\t.waterfall .item-title {\n\t\tfont-size: 32rpx;\n\t\tcolor: #333333;\n\t\tline-height: 46rpx;\n\t\ttext-align: justify;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tdisplay: -webkit-box;\n\t\t-webkit-box-orient: vertical;\n\t\t-webkit-line-clamp: 2;\n\t\tfont-weight: bold;\n\t}\n\n\t.waterfall .item-desc {\n\t\tmargin-top: 4rpx;\n\t\tfont-size: 26rpx;\n\t\tcolor: #666666;\n\t\tline-height: 34rpx;\n\t\ttext-align: justify;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\tdisplay: -webkit-box;\n\t\t-webkit-box-orient: vertical;\n\t\t-webkit-line-clamp: 2;\n\t}\n</style>", "import mod from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-waterfall.vue?vue&type=style&index=0&id=64a760e9&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-waterfall.vue?vue&type=style&index=0&id=64a760e9&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341778\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}