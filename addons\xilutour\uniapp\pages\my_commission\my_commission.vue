<template>
	<view class="xilu">
		<view class="container">
			<view class="xilu_data_box">
				<view class="flex-box mb45">
					<view class="flex-1">
						<view class="num">
							<text class="fs24 col-f">¥</text>
							<text>{{account.total_money}}</text>
						</view>
						<view class="fs28 col-f">总额</view>
					</view>
					<view class="btn" @click="withdraw()">申请提现</view>
				</view>
				<view class="flex-box">
					<view class="mr45 pr45">
						<view class="fs36 col-f mb10">{{account.money}}</view>
						<view class="fs24 col-f">可提现佣金</view>
					</view>
					<view class="mr45 pr45">
						<view class="fs36 col-f mb10">{{account.freeze_money}}</view>
						<view class="fs24 col-f">冻结中佣金</view>
					</view>
				</view>
			</view>

			<view class="fs36 col-10 mb30">佣金明细</view>

			<view class="xilu_commission" v-for="(item,index) in moneyList"  :key="index">
				<view v-if="item.type != 3">
					<view class="flex-box mb20">
						<view class="fs32 col-10 flex-1">订单号{{item.extra_text.order_no}}</view>
						<view class="col-5">
							<text class="fs24">¥</text>
							<text class="fs30">{{item.extra_text.pay_price}}</text>
						</view>
					</view>
					<view class="flex-box">
						<image :src="item.extra_text.avatar" mode="aspectFill"></image>
						<view class="fs28 col-5 mr20 ml15">{{item.extra_text.nickname}}</view>
						<view class="fs24 col-89 flex-1">{{item.createtime_text}}</view>
						<view class="col-price">
							<text class="fs30">+</text>
							<text class="fs24">¥</text>
							<text class="fs30">{{item.money}}</text>
						</view>
					</view>
				</view>
				<view v-else-if="item.type">
					<view class="flex-box mb20">
						<view class="fs32 col-10 flex-1">订单号{{item.extra_text.order_no}}</view>
						
					</view>
					<view class="flex-box">
						<view class="fs28 col-5 mr20 ml15">{{item.memo}}</view>
						<view class="fs24 col-89 flex-1">{{item.createtime_text}}</view>
						<view class="col-price">
							<text class="fs30">{{item.money>0?'+':'-'}}</text>
							<text class="fs24">¥</text>
							<text class="fs30">{{item.money_text}}</text>
						</view>
					</view>
				</view>
				
			</view>
			
			<view class="g-btn3-wrap">
				<view class="g-btn3" @click="fetch">{{moneyListMore.text}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				account:{
					id: 0,
					money: '0.00',
					total_money: '0.00',
					freeze_money: '0.00',
				},
				moneyList:[],
				moneyListMore: {page:1}
			};
		},
		onLoad() {
			this.getAccount();
			this.fetch();
		},
		onReachBottom() {
			this.fetch();
		},
		methods:{
			getAccount(){
				this.$core.post({url: 'xilutour.user/account',data: {},loading: false,success: ret => {
						this.account = ret.data;
					},fail: err => {
						console.log(err);
					}
				});
			},
			refresh(){
				this.moneyList = [];
				this.moneyListMore = {page:1};
				this.fetch();
			},
			fetch(){
				this.$util.fetch(this, 'xilutour.user/money_log', {pagesize:10}, 'moneyListMore', 'moneyList', 'data', data=>{
				  
				})
			},
			//提现
			withdraw(){
				uni.navigateTo({
					url: '/pages/withdrawal/withdrawal',
					events:{
						withdrawSuccess: data=>{
							this.getAccount();
							this.refresh();
						}
					}
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		.container {
			padding: 30rpx 40rpx;
			background: #F7F9FB;
		}

		&_data_box {
			padding: 40rpx 32rpx 40rpx 40rpx;
			margin: 0 0 35rpx;
			width: 670rpx;
			border-radius: 24rpx;
			background: var(--normal);

			.num {
				margin: 0 0 14rpx;
				font-size: 50rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 52rpx;
			}

			.btn {
				width: 170rpx;
				height: 80rpx;
				background: #FFFFFF;
				border-radius: 24rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #00B6AB;
				line-height: 80rpx;
				text-align: center;
			}
		}

		&_commission {
			margin: 0 0 20rpx;
			padding: 36rpx 30rpx 35rpx 30rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
			border-radius: 30rpx;
			image{
				display: block;
				width: 40rpx;
				height: 40rpx;
				border-radius: 50%;
			}
		}
	}
</style>