<template>
	<view class="xilu">
		<view class="container">
			<view class="xilu_data_box flex-box">
				<view class="flex-1">
					<view class="num">{{totalCount}}</view>
					<view class="fs28 col-f">我的好友</view>
				</view>
				<view class="btn" @click="sharePopOpen">邀请好友</view>
			</view>
			<view class="fs36 col-10 mb30">我的好友</view>
			<view class="xilu_friend flex-box" v-for="(item,index) in teamList" :key="index">
				<image class="img" :src="item.user.avatar" mode="aspectFill"></image>
				<view class="flex-1">
					<view class="name">{{item.user.nickname}}</view>
					<view class="time">{{item.bindtime_text}}</view>
				</view>
			</view>
			<view class="g-btn3-wrap">
				<view class="g-btn3" @click="fetch">{{teamListMore.text}}</view>
			</view>

			<uni-popup ref="sharePopup">
				<view class="share-pop" :class="{'scale': screenHeight < 750}" catchtouchmove="true">
					<image class="img_post" :src="posterPath" mode="widthFix"></image>
					<view class="flex-box flex-between tc" style="padding: 0 116rpx;">
						<button class="pop_btn fs24 col-f" open-type="share">
							<image class="icon" src="/static/icon/icon_wx1.png" mode="aspectFit"></image>
							<view>微信好友</view>
						</button>
						<button class="pop_btn fs24 col-f" @click="saveImage">
							<image class="icon" src="/static/icon/icon_down.png" mode="aspectFit"></image>
							<view>保存图片</view>
						</button>
					</view>
				</view>
			</uni-popup>
			
			<!--  -->
			<l-painter useCORS ref="painter" isCanvasToTempFilePath @success="posterPath = $event" custom-style="position: fixed; left: 200%;"
				css="width: 640rpx;height: 1068rpx;border-radius: 20rpx;background-color:#FFF;">
				<l-painter-image :src="poster.poster_img"
					css="object-fit: cover; object-position: 50% 50%; width: 100%; height: 868rpx;border-radius: 20rpx 20rpx 0 0;display:block;" />
				<l-painter-image :src="poster.img1"
					css="position: absolute;bottom: 50rpx;left: 30rpx; object-fit: cover; object-position: 50% 50%; width: 100rpx; height: 100rpx;border-radius: 50%;" />
				<l-painter-view css="position: absolute;top:918rpx;left:144rpx;right:210rpx;">
					<l-painter-text :text="poster.text1"
						css="font-size:34rpx;font-weight:bold;color:#333;line-height:48rpx;"></l-painter-text>
				</l-painter-view>
				<l-painter-view css="position: absolute;top:974rpx;left:144rpx;right:210rpx;">
					<l-painter-text :text="poster.text2"
						css="font-size:24rpx;font-weight:500;color:#999999;line-height:32rpx;"></l-painter-text>
				</l-painter-view>
				<l-painter-image :src="poster.img2"
					css="position: absolute;bottom: 12rpx;right: 30rpx; object-fit: cover; object-position: 50% 50%; width: 176rpx; height: 176rpx;" />
			</l-painter>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				poster:{
					text1:'',
					text2:'',
					poster_img:'',
					img1:'',
					img2:''
				},
				posterPath: '',
				screenHeight: 0,
				totalCount:0,
				teamList:[],
				teamListMore: {page:1}
			};
		},
		onLoad() {
			this.screenHeight = getApp().globalData.screenHeight;
			this.fetch()
		},
		onReachBottom() {
			this.fetch()
		},
		methods: {
			fetch(){
				this.$util.fetch(this, 'xilutour.user/myteam', {pagesize:10}, 'teamListMore', 'teamList', 'data', data=>{
				  this.totalCount = data.total;
				})
			},
			
			// 打开分享弹窗
			sharePopOpen() {
				this.$core.post({url: 'xilutour.user/poster',data: {},success: ret => {
						this.poster = ret.data;
						uni.showLoading({
							title:'海报绘制中',
							success: () => {
								setTimeout(()=>{
									uni.hideLoading();
									this.$refs.sharePopup.open();
								},1000)
							}
						})
					},fail: err => {
						console.log(err);
					}
				});
			},
			
			// 关闭分享弹窗
			sharePopClose() {
				this.$refs.sharePopup.close();
			},
			saveImage() {
				let that = this;
				uni.saveImageToPhotosAlbum({
					filePath: that.posterPath,
					success: function() {
						uni.showToast({
							icon: 'success',
							title: '保存成功'
						});
					}
				});
			},
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		.container {
			padding: 30rpx 40rpx;
			background: #F7F9FB;
		}

		&_data_box {
			padding: 0 32rpx 0 40rpx;
			margin: 0 0 40rpx;
			width: 670rpx;
			height: 168rpx;
			border-radius: 24rpx;
			background: var(--normal);

			.num {
				margin: 0 0 14rpx;
				font-size: 50rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 52rpx;
			}

			.btn {
				width: 170rpx;
				height: 80rpx;
				background: #FFFFFF;
				border-radius: 24rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #00B6AB;
				line-height: 80rpx;
				text-align: center;
			}
		}

		&_friend {
			margin: 0 0 30rpx;
			padding: 0 30rpx;
			width: 670rpx;
			height: 160rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
			border-radius: 24rpx;

			.img {
				margin-right: 30rpx;
				display: block;
				width: 100rpx;
				height: 100rpx;
				border-radius: 50%;
			}

			.name {
				margin: 0 0 30rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #101010;
				line-height: 32rpx;
			}

			.time {
				font-size: 28rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #898989;
				line-height: 30rpx;
			}
		}

		.share-pop {
			width: 642rpx;

			.img_post {
				margin: 0 0 30rpx;
				display: block;
				width: 642rpx;
				border-radius: 20rpx;
			}

			.pop_btn {
				margin: 0;
				padding: 0;
				background-color: unset;
			}

			.pop_btn::after {
				content: none;
			}

			.icon {
				margin: 0 auto 10rpx;
				display: block;
				width: 118rpx;
				height: 118rpx;
				border-radius: 50%;
			}
		}

		.share-pop.scale {
			transform: scale(0.9);
		}
	}
</style>