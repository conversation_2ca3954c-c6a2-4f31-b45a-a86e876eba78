<view class="xilu data-v-53ec0234"><view class="page-foot flex-box flex-between data-v-53ec0234"><view data-event-opts="{{[['tap',[['bindReset']]]]}}" class="btn1 data-v-53ec0234" bindtap="__e">清空</view><view data-event-opts="{{[['tap',[['bindSubmit']]]]}}" class="btn2 data-v-53ec0234" bindtap="__e">确定</view></view><view class="container data-v-53ec0234"><block xhs:if="{{type!=='tour'}}"><view class="data-v-53ec0234"><view class="fs30 col-10 mb30 data-v-53ec0234">景点级别</view><view class="xilu_filtrate flex-box flex-wrap data-v-53ec0234"><block xhs:for="{{levels}}" xhs:for-item="level" xhs:for-index="index" xhs:key="index"><view data-event-opts="{{[['tap',[['bindChanceLevel',['$0'],[[['levels','',index,'id']]]]]]]}}" class="{{['item','data-v-53ec0234',(level.id==query.level_id)?'active':'']}}" bindtap="__e">{{level.name}}</view></block></view></view></block><view class="data-v-53ec0234"><view class="fs30 col-10 mb30 data-v-53ec0234">标签</view><view class="xilu_filtrate flex-box flex-wrap data-v-53ec0234"><block xhs:for="{{tags}}" xhs:for-item="tag" xhs:for-index="index" xhs:key="index"><view data-event-opts="{{[['tap',[['bindChangeTags',[index]]]]]}}" class="{{['item','data-v-53ec0234',(tag.check)?'active':'']}}" bindtap="__e">{{tag.name}}</view></block></view></view></view></view>