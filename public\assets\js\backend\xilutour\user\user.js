define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'xilutour/user/user/index' + location.search,
                    add_url: 'xilutour/user/user/add',
                    edit_url: 'xilutour/user/user/edit',
                    del_url: 'xilutour/user/user/del',
                    multi_url: 'xilutour/user/user/multi',
                    moneylog_url: 'xilutour/finance/money_log/index',
                    table: 'xilutour_user_account',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'username', title: __('Username'), operate: 'LIKE'},
                        {field: 'nickname', title: __('Nickname'), operate: 'LIKE'},
                        {field: 'mobile', title: __('Mobile'), operate: 'LIKE'},
                        {field: 'avatar', title: __('Avatar'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'source_text', title: __('Source'), operate: 'LIKE', searchList: {'web': '网站注册', 'wxmini': '微信小程序', 'xhs': '小红书'}},
                        {field: 'account.money', title: __('余额'), operate: 'BETWEEN', sortable: true},
                        {field: 'account.total_money', title: __('累计佣金'), operate: 'BETWEEN', sortable: true},
                        {field: 'account.first_username', title: __('直属用户'), operate: 'LIKE'},
                        {field: 'account.second_username', title: __('间属用户'), operate: 'LIKE'},
                        {field: 'gender', title: __('Gender'), visible: false, searchList: {1: __('Male'), 0: __('Female')}},
                        {field: 'maxsuccessions', title: __('Maxsuccessions'), visible: false, operate: 'BETWEEN', sortable: true},
                        {field: 'logintime', title: __('Logintime'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
                        {field: 'jointime', title: __('Jointime'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
                        {field: 'status', title: __('Status'), formatter: Table.api.formatter.status, searchList: {normal: __('Normal'), hidden: __('Hidden')}},
                        {field: 'operate', title: __('Operate'),buttons: [
                                {
                                    name: 'moneylog',
                                    title: __('收入日志'),
                                    classname: 'btn btn-xs btn-warning btn-dialog',
                                    icon: 'fa fa-money',
                                    url: function (row) {
                                        return Fast.api.fixurl($.fn.bootstrapTable.defaults.extend.moneylog_url+'?user_id='+row.id)
                                    },
                                }
                            ] , table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
