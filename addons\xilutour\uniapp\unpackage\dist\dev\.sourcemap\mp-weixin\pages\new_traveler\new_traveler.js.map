{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/new_traveler/new_traveler.vue?cb25", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/new_traveler/new_traveler.vue?e77b", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/new_traveler/new_traveler.vue?7560", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/new_traveler/new_traveler.vue?4bf0", "uni-app:///pages/new_traveler/new_traveler.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/new_traveler/new_traveler.vue?bba7", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/new_traveler/new_traveler.vue?d69d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "genderList", "id", "name", "adultTypeList", "traveler", "username", "idcard", "mobile", "gender", "adult_type", "onLoad", "page", "methods", "bindGenderChange", "bindAdultTypeChange", "bindSave", "nameChn", "rules", "errorMsg", "require", "length", "uni", "title", "icon", "url", "success"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4Cz1B;AACA;AAAA,eACA;EACAC;IACA;MACAC;QAAAC;QAAAC;MAAA;QAAAD;QAAAC;MAAA;MACAC;QAAAF;QAAAC;MAAA;QAAAD;QAAAC;MAAA;MACAE;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IAEAC;MAAA;MACA;MACA,YACA;QAAAb;QAAAc;QAAAC;QAAAC;UAAAC;QAAA;MAAA,GACA;QAAAjB;QAAAc;QAAAC;QAAAC;UAAAC;UAAAC;QAAA;MAAA,GACA;QAAAlB;QAAAc;QAAAC;QAAAC;UAAAC;UAAAC;QAAA;MAAA,EAEA;MACA;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;QAAAC;QAAAzB;QAAA0B;UACA;UACAJ;UACAA;YACAC;YACAC;UACA;QACA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtGA;AAAA;AAAA;AAAA;AAAwhD,CAAgB,y5CAAG,EAAC,C;;;;;;;;;;;ACA5iD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/new_traveler/new_traveler.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/new_traveler/new_traveler.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./new_traveler.vue?vue&type=template&id=176b31c6&scoped=true&\"\nvar renderjs\nimport script from \"./new_traveler.vue?vue&type=script&lang=js&\"\nexport * from \"./new_traveler.vue?vue&type=script&lang=js&\"\nimport style0 from \"./new_traveler.vue?vue&type=style&index=0&id=176b31c6&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"176b31c6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/new_traveler/new_traveler.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./new_traveler.vue?vue&type=template&id=176b31c6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./new_traveler.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./new_traveler.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"page-foot\" @click=\"bindSave()\">\r\n\t\t\t<view class=\"g-btn1\">确定</view>\r\n\t\t</view>\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"g_input_box flex-box mb30\">\r\n\t\t\t\t<view class=\"fs30 col-5\">姓名</view>\r\n\t\t\t\t<input class=\"flex-1 tr fs30 col-10\" v-model=\"traveler.username\" type=\"text\" placeholder=\"请输入姓名\" placeholder-class=\"col-10\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"g_input_box flex-box mb30\">\r\n\t\t\t\t<view class=\"fs30 col-5 flex-1\">性别</view>\r\n\t\t\t\t<view class=\"flex-box flex-between flex-1 fs30 col-10\">\r\n\t\t\t\t\t\t<view class=\"flex-box\" v-for=\"(item,index) in genderList\" :key=\"index\" @click=\"bindGenderChange(item.id)\">\r\n\t\t\t\t\t\t\t<image v-if=\"item.id==traveler.gender\" class=\"icon_check\" src=\"/static/icon/icon_checkon.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t<image v-else class=\"icon_check\" src=\"/static/icon/icon_check.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"g_input_box flex-box mb30\">\r\n\t\t\t\t<view class=\"fs30 col-5 flex-1\">类型</view>\r\n\t\t\t\t<view class=\"flex-box flex-between flex-1 fs30 col-10\">\r\n\t\t\t\t\t\t<view class=\"flex-box\" v-for=\"(item,index) in adultTypeList\" :key=\"index\" @click=\"bindAdultTypeChange(item.id)\">\r\n\t\t\t\t\t\t\t<image v-if=\"item.id==traveler.adult_type\" class=\"icon_check\" src=\"/static/icon/icon_checkon.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t<image v-else class=\"icon_check\" src=\"/static/icon/icon_check.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"g_input_box flex-box mb30\">\r\n\t\t\t\t<view class=\"fs30 col-5\">身份证号</view>\r\n\t\t\t\t<input class=\"flex-1 tr fs30 col-10\" v-model=\"traveler.idcard\" maxlength=\"18\" type=\"idcard\" placeholder=\"请输入身份证号\" placeholder-class=\"col-10\"/>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"g_input_box flex-box mb30\">\r\n\t\t\t\t<view class=\"fs30 col-5\">手机号码</view>\r\n\t\t\t\t<input class=\"flex-1 tr fs30 col-10\" v-model=\"traveler.mobile\" maxlength=\"11\" type=\"number\" placeholder=\"请输入手机号码\" placeholder-class=\"col-10\"/>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\n// import { func } from \"prop-types\";\n\tvar validate = require(\"../../xilu/validate.js\");\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tgenderList:[{id:1,name:'男'},{id:2,name:'女'}],\n\t\t\t\tadultTypeList:[{id:1,name:'成人'},{id:2,name:'儿童'}],\n\t\t\t\ttraveler:{\n\t\t\t\t\tusername:'',\n\t\t\t\t\tidcard:'',\n\t\t\t\t\tmobile:'',\n\t\t\t\t\tgender: 1,\n\t\t\t\t\tadult_type:1,\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\n\t\tonLoad() {\n\t\t\tlet page = this;\n\t\t\tthis.getOpenerEventChannel().on(\"editTransfor\",function(data){\n\t\t\t\tpage.traveler = data\n\t\t\t})\n\t\t},\n\t\tmethods:{\n\t\t\t//性别\n\t\t\tbindGenderChange(id){\n\t\t\t\tthis.traveler.gender = id;\n\t\t\t},\n\t\t\t//类型\n\t\t\tbindAdultTypeChange(id){\n\t\t\t\tthis.traveler.adult_type = id;\n\t\t\t},\n\t\t\t\n\t\t\tbindSave(){\n\t\t\t\tlet formData = this.traveler;\n\t\t\t\tvar rule = [\n\t\t\t\t\t{name: 'username',nameChn: '姓名',rules: ['require'],errorMsg: {require: '请填写姓名'}},\n\t\t\t\t\t{name: 'idcard',nameChn: '身份证号',rules: ['require','length:18'],errorMsg: {require: '请填写身份证号',length:\"身份证长度错误\"}},\n\t\t\t\t\t{name: 'mobile',nameChn: '手机号',rules: ['require','length:11'],errorMsg: {require: '请填写手机号',length:\"手机号错误\"}},\n\t\t\t\t    \n\t\t\t\t];\n\t\t\t\t// 是否全部通过，返回Boolean\n\t\t\t\tif (!validate.check(formData, rule)) {\n\t\t\t\t    uni.showToast({\n\t\t\t\t        title: validate.getError()[0],\n\t\t\t\t        icon: 'none'\n\t\t\t\t    });\n\t\t\t\t    return;\n\t\t\t\t}\n\t\t\t\tthis.$core.post({url: 'xilutour.traveler/set_traveler',data: formData,success: ret => {\n\t\t\t\t    this.getOpenerEventChannel().emit(\"setSuccess\",{})\n\t\t\t\t    uni.navigateBack({});\n\t\t\t\t    uni.showToast({\n\t\t\t\t        title: '提交成功',\n\t\t\t\t        icon: 'none'\n\t\t\t\t    });\n\t\t\t\t}})\n\t\t\t}\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.xilu {\r\n\t\t.page-foot {\r\n\t\t\tpadding: 20rpx 65rpx;\r\n\t\t\tbackground-color: #FFF;\r\n\t\t}\r\n\r\n\t\t.container {\r\n\t\t\tpadding: 30rpx 40rpx 160rpx !important;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./new_traveler.vue?vue&type=style&index=0&id=176b31c6&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./new_traveler.vue?vue&type=style&index=0&id=176b31c6&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341234\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}