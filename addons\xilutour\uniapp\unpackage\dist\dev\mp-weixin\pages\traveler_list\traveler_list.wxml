<view class="xilu data-v-1a799274"><view data-event-opts="{{[['tap',[['addTraveler']]]]}}" class="page-foot flex-box data-v-1a799274" bindtap="__e"><view class="g-btn1 flex-1 data-v-1a799274">新建出行人</view></view><view class="container data-v-1a799274"><block wx:for="{{travelerList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="xilu_traveler data-v-1a799274"><view class="data-v-1a799274"><view class="flex-box mb30 data-v-1a799274"><view class="col-10 mr20 data-v-1a799274">{{item.username}}</view><block wx:if="{{item.gender==1}}"><image class="icon_gender data-v-1a799274" src="/static/icon/icon_gender1.png" mode="aspectFit"></image></block><block wx:else><image class="icon_gender data-v-1a799274" src="/static/icon/icon_gender2.png" mode="aspectFit"></image></block></view><view class="flex-box mb20 data-v-1a799274"><view class="col-9 mr15 data-v-1a799274">身份证号</view><view class="col-3 flex-1 data-v-1a799274">{{item.idcard}}</view></view><view class="flex-box data-v-1a799274"><view class="col-9 mr15 data-v-1a799274">手机号码</view><view class="col-3 flex-1 data-v-1a799274">{{item.mobile}}</view></view></view><view class="flex-box flex-end fs30 col-89 data-v-1a799274"><view data-event-opts="{{[['tap',[['bindEdit',[index]]]]]}}" class="flex-box mr30 data-v-1a799274" bindtap="__e"><image class="icon data-v-1a799274" src="/static/icon/icon_edit1.png" mode="aspectFit"></image><view class="data-v-1a799274">编辑</view></view><view data-event-opts="{{[['tap',[['bindDel',[index]]]]]}}" class="flex-box data-v-1a799274" bindtap="__e"><image class="icon data-v-1a799274" src="/static/icon/icon_del1.png" mode="aspectFit"></image><view class="data-v-1a799274">删除</view></view></view></view></block></view></view>