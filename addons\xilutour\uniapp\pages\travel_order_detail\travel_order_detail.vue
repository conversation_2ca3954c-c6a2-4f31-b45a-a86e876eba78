<template>
	<view class="xilu">
		<view class="page-foot ptb15">
			<view class="g_order_foot1 flex-box" v-if="order.state == 0 || order.state == 2">
				<view class="flex-1">
					<!-- <view class="col-price fs24">
						<view>请在24小时内支付</view>
						<view>过期自动取消</view>
					</view> -->
				</view>
				<view class="btn2" v-if="order.state == 0" @click="orderCancel()">取消订单</view>
				<view class="btn3" v-if="order.state == 0" @click="payment()">立即支付</view>
				<view class="btn3" v-if="order.state==2 && order.comment_status==0" @click="bindComment()">立即评价</view>
			</view>
		</view>

		<view class="container">
			<view class="xilu_status">
				<view class="title">{{order.state_text}}</view>
				<view v-if="order.state==0">请在{{order.expiretime_text}}前支付订单，过期自动取消</view>
				<!-- <view class="title" v-else-if="order.state==1">已支付</view> -->
				<view v-else-if="order.state==1">订单已支付完成～</view>
				<!-- 	<view class="title">已完成</view>-->
				<view v-else-if="order.state==2">本次服务已完成，感谢使用</view> 
			</view>

			<view class="xilu_goods flex-box mb30">
				<image class="img" :src="order.order_tour.thumb_image" mode="aspectFill"></image>
				<view class="flex-1">
					<view class="m-ellipsis fs36 col-10 mb20">{{order.order_tour.tour_name}}</view>
					<view class="flex-box col-3 mb20">
						<text class="fs24">¥</text>
						<text class="fs30 flex-1">{{order.order_tour.tour_date_salesprice}}</text>
						<view class="fs30 col-89 pr40">数量 {{order.total_count}}</view>
					</view>
					<view class="flex-box" style="height: 70rpx;">
						<view class="flex-1">
							<text class="fs30 col-89">实付款 </text>
							<text class="fs30 col-price">¥</text>
							<text class="fs40 col-price">{{order.pay_price}}</text>
						</view>
						<view class="btn_apply" v-if="(order.state==1 || order.state==5) && order.is_refund==1" @click="bindRefund()">申请退款</view>
					</view>
				</view>
			</view>

			<view class="xilu_travel_time flex-box">
				<view class="flex-1">
					<view class="fs24 col-89 mb15">出发日期</view>
					<view>
						<text class="fs36 col-10 mr15">{{order.order_tour.appoint_date_text}}</text>
						<text class="fs30 col-5">{{order.order_tour.appoint_date_week}}</text>
					</view>
				</view>
				<view class="line"></view>
				<view class="flex-1">
					<view class="fs24 col-89 mb15">结束日期</view>
					<view>
						<text class="fs36 col-10 mr15">{{order.order_tour.appoint_end_date_text}}</text>
						<text class="fs30 col-5">{{order.order_tour.appoint_end_date_week}}</text>
					</view>
				</view>
				<view class="fs30 col-normal">{{order.order_tour.series_days}}天</view>
			</view>

			<view class="xilu_info_box fs30">
				<view class="fs34 col-10 mb40">联系人</view>
				<view class="flex-box mb50">
					<view class="col-5 flex-1">姓名</view>
					<view class="col-10">{{order.contact_name}}</view>
				</view>
				<view class="flex-box">
					<view class="col-5 flex-1">手机号码</view>
					<view class="col-10">{{order.contact_mobile}}</view>
				</view>
			</view>

			<view class="xilu_info_box fs30">
				<view class="fs34 col-10">出行人信息</view>
				<view class="flex-box mt40" v-for="(traveler,index) in order.order_traveler" :key="index">
					<view class="fs30 col-10 mr20">{{traveler.username}}</view>
					<view class="flex-1">
						<image class="g-icon30" v-if="traveler.gender==1" src="../../static/icon/icon_gender1.png" mode="aspectFit"></image>
						<image class="g-icon30" v-else-if="traveler.gender==2" src="../../static/icon/icon_gender2.png" mode="aspectFit"></image>
					</view>
					<view class="identity1" v-if="traveler.adult_type==1">成人</view>
					<view class="identity2" v-else-if="traveler.adult_type==2">儿童</view>
				</view>
			</view>
			
			<view class="g_order_info">
				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">订单号</view>
					<view class="fs30 col-10">{{order.order_no}}</view>
				</view>
				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">订单时间</view>
					<view class="fs30 col-10">{{order.createtime_text}}</view>
				</view>
				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">支付方式</view>
					<image src="/static/icon/icon_wx.png" mode="aspectFit" class="g-icon30 mr15"></image>
					<view class="fs30 col-10">微信支付</view>
				</view>
			
				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">商品金额</view>
					<view class="col-10">
						<text class="fs30">¥</text>
						<text class="fs40">{{order.total_price}}</text>
					</view>
				</view>
			
				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">优惠劵</view>
					<view class="col-10">
						<text class="fs24">-¥</text>
						<text class="fs34">{{order.coupon_price}}</text>
					</view>
				</view>
				
				<view class="flex-box flex-end mb45">
					<view class="fs30 col-89 mr20">共计</view>
					<view class="col-price">
						<text class="fs30">¥</text>
						<text class="fs40">{{order.pay_price}}</text>
					</view>
				</view>
			</view>

			<view class="fs24 col-5 ptb30" v-if="order.state == 1">提示：{{order.tips}}</view>
			
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				orderId: 0,
				order:{
					order_no:'',
					total_count: 0,
					createtime_text: '',
					total_price: 0,
					pay_price: 0,
					coupon_price: 0,
					order_tour:{
						thumb_image: '',
						tour_name: '',
						appoint_end_date_text: '',
						appoint_end_date_week: '',
						appoint_date_text: '',
						appoint_date_week: '',
						series_days: 0,
						tour_date_salesprice: 0,
					},
					order_traveler:[]
				}
			};
		},
		onLoad(options) {
			this.orderId = options.id || 0;
			this.fetchDetail();
		},
		methods:{
			fetchDetail(){
				this.$core.get({url:'xilutour.tour_order/detail',data:{order_id: this.orderId},success:(ret)=>{
					this.order = ret.data;
					uni.setNavigationBarTitle({
						title: ret.data.state_text
					})
				}});
			},
			//退款
			bindRefund(){
				let order = this.order;
				uni.navigateTo({
					url: '/pages/apply_refund/apply_refund?id='+order.id,
					events:{
						aftersaleSuccess: data=>{
							this.fetchDetail();
						}
					}
				})
			},
			//取消
			orderCancel(){
				let page = this;
				let order = this.order;
				uni.showModal({
					title:'提示',
					content: '确认取消订单？',
					success(res) {
						if(res.confirm){
							page.$core.post({url:'xilutour.tour_order/cancel',data:{order_id: order.id},loading:true,success:(ret)=>{
								page.fetchDetail();
							 }});
						}
					}
				})
			},
			payment(){
				let order = this.order;
				//#ifdef MP-WEIXIN
				this.$core.post({url:'xilutour.pay/pay',data:{pay_type:1,order_id:order.id,platform:'wxmini'},success:(ret)=>{
					let wxconfig =  ret.data;
					this.$core.payment(wxconfig,function(){
						
					})
				}});
				//#endif
			},
			bindComment(){
				let order = this.order;
				uni.navigateTo({
					url: '/pages/tour_evaluation/tour_evaluation',
					events:{
						commentSuccess: data=>{
							order.comment_status=1;
							this.order = order;
						}
					},
					success(res) {
						res.eventChannel.emit("addComment",order)
					}
				})
			},
		}
	}
</script>

<style lang="less" scoped>
	.g_order_info{
		padding-bottom: 5rpx;
	}
	.g_order_foot1 {
		padding-left: 30rpx;
	}
	.xilu {
		&_status {
			padding: 30rpx;
			margin: 0 0 40rpx;
			background: rgba(5, 185, 174, 0.1);
			border-radius: 22rpx;
			font-size: 28rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #555555;
			line-height: 30rpx;

			.title {
				margin: 0 0 20rpx;
				font-size: 34rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: var(--normal);
				line-height: 36rpx;
			}
		}

		&_goods {
			.btn_apply {
				width: 158rpx;
				height: 70rpx;
				border-radius: 25rpx;
				border: 1rpx solid var(--normal);
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: var(--normal);
				line-height: 70rpx;
				text-align: center;
			}

			.img {
				margin-right: 30rpx;
				display: block;
				width: 180rpx;
				height: 180rpx;
				border-radius: 15rpx;
			}
		}

		&_travel_time {
			padding: 0 40rpx 0 30rpx;
			margin: 0 0 30rpx;
			height: 120rpx;
			background: #F7F9FB;
			border-radius: 20rpx;

			.line {
				margin: 0 60rpx;
				width: 25rpx;
				height: 2rpx;
				background-color: var(--normal);
			}
		}

		&_info_box {
			margin: 0 0 30rpx;
			padding: 30rpx 30rpx 40rpx;
			background: #F7F9FB;
			border-radius: 25rpx;

			.identity1 {
				width: 60rpx;
				height: 36rpx;
				background: #FFAB29;
				border-radius: 5rpx;
				font-size: 24rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 36rpx;
				text-align: center;
			}

			.identity2 {
				width: 60rpx;
				height: 36rpx;
				background: var(--normal);
				border-radius: 5rpx;
				font-size: 24rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 36rpx;
				text-align: center;
			}
		}


		.page-foot~.container {
			padding-bottom: 170rpx;
		}

		.container {
			padding-top: 30rpx;
			padding-left: 40rpx;
			padding-right: 40rpx;
		}
	}
</style>