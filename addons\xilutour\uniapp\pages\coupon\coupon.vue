<template>
	<view class="xilu">
		<view class="page-head bg-white">
			<view class="g_tab flex-box">
				<view class="item flex-1" :class="{'active': tabIdx == 0}" @click="tabClick(0)">全部</view>
				<view class="item flex-1" :class="{'active': tabIdx == 1}" @click="tabClick(1)">待使用</view>
				<view class="item flex-1" :class="{'active': tabIdx == 2}" @click="tabClick(2)">已使用</view>
				<view class="item flex-1" :class="{'active': tabIdx == 3}" @click="tabClick(3)">已过期</view>
			</view>
		</view>
		<view class="container">
			<view class="xilu_coupon" :class="coupon.state_text==1?'':'disabled'" v-for="(coupon,index) in couponList" :key="index">
				<image src="/static/icon/icon_coupon_bg1.png" mode="aspectFill" class="bg"></image>
				<view class="inner flex-box">
					<view class="left">
						<view class="fwb mb20">
							<text class="fs24">¥</text>
							<text class="fs50">{{coupon.coupon.money}}</text>
						</view>
						<view class="man">满{{coupon.coupon.at_least}}可用</view>
					</view>
					<view class="right flex-1 flex-box">
						<view class="flex-1">
							<view class="fs30 mb20">{{coupon.coupon.name}}</view>
							<view class="fs24">{{coupon.endtime_text}}到期</view>
						</view>
						<view class="use" v-if="coupon.state_text==1">使用</view>
						<image v-else-if="coupon.state_text==2" src="../../static/icon/icon_coupon1.png" mode="aspectFill" class="icon_disabled"></image>
						<image v-else src="../../static/icon/icon_coupon2.png" mode="aspectFill" class="icon_disabled"></image>
					</view>
				</view>
			</view>

			<view class="g-btn3-wrap">
				<view class="g-btn3" @click="fetch">{{couponListMore.text}}</view>
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabIdx: 0,
				couponList:[],
				couponListMore:{page:1}
			};
		},
		onLoad() {
			this.fetch();
		},
		onReachBottom() {
			this.fetch();
		},
		methods: {
			tabClick(i) {
				this.tabIdx = i;
				this.refresh()
			},
			refresh(){
				this.couponList = [];
				this.couponListMore = {page:1};
				this.fetch();
			},
			fetch(){
				this.$util.fetch(this, 'xilutour.coupon/mycoupons', {tab: this.tabIdx,pagesize:10}, 'couponListMore', 'couponList', 'data', data=>{
				  
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		&_coupon {
			position: relative;
			margin: 0 0 40rpx;
			width: 670rpx;
			height: 194rpx;
			color: #FFF;

			.bg {
				display: block;
				width: 670rpx;
				height: 194rpx;
			}

			.inner {
				position: absolute;
				left: 0;
				top: 0;
				right: 0;
				bottom: 0;

				.left {
					padding: 30rpx 30rpx 0;
					width: 218rpx;
					height: 194rpx;
					text-align: center;

					.man {
						width: 156rpx;
						height: 44rpx;
						background: rgba(255, 255, 255, 0.2);
						border-radius: 22rpx;
						font-size: 24rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #FFFFFF;
						line-height: 44rpx;
					}
				}

				.right {
					padding: 0 30rpx;
					height: 194rpx;
					color: #FDFEFE;

					.use {
						margin: 0 0 0 25rpx;
						width: 120rpx;
						height: 80rpx;
						background: rgba(255, 255, 255, 0.3);
						border-radius: 34rpx;
						font-size: 30rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #FFFFFF;
						line-height: 80rpx;
						text-align: center;
					}
					.icon_disabled{
						margin: 0 0 0 25rpx;
						display: block;
						width: 110rpx;
						height: 110rpx;
					}
				}
			}
		}

		&_coupon.disabled {
			color: #CCCCCC;

			.inner {
				.left {
					.man {
						color: #FFFFFF;
						background: #E5E5E5;
					}
				}

				.right {
					color: #E5E5E5;
					.fs30 {
						color: #CCCCCC;
					}
				}
			}
		}

		.container {
			padding: 136rpx 40rpx 40rpx;
			background: #F7F9FB;
		}
	}
</style>