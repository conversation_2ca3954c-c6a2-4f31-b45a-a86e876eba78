<template>
	<view class="xilu">
		<view class="container">
			<view class="fs36 col-10 mb30">佣金明细</view>
			<view class="xilu_record" v-for="(item,index) in withDrawList" :key="index">
				<view class="flex-box mb25">
					<view class="fs32 col-10 flex-1">申请提现</view>
					<view class="fs24 col-89">{{item.createtime_text}}</view>
				</view>
				<view class="flex-box">
					<view class="col-normal flex-1">
						<text class="fs24">¥</text>
						<text class="fs40">{{item.money}}</text>
					</view>
					<view class="fs30 col-price" v-if="item.state==1 || item.state==2">审核中</view>
					<view class="fs30 col-5" v-else-if="item.state==3">已通过</view>
					<view class="fs30 col-5" v-else-if="item.state==4">已拒绝</view>
				</view>
			</view>
			
			<view class="g-btn3-wrap">
				<view class="g-btn3" @click="fetch">{{withDrawListMore.text}}</view>
			</view>
			
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				withDrawList:[],
				withDrawListMore: {page:1}
			};
		},
		onLoad() {
			this.fetch();
		},
		onReachBottom() {
			this.fetch();
		},
		methods:{
			fetch(){
				this.$util.fetch(this, 'xilutour.user/withdraw_log', {pagesize:10}, 'withDrawListMore', 'withDrawList', 'data', data=>{
				
				})
			},
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		.container {
			padding: 30rpx 40rpx;
			background: #F7F9FB;
		}

		&_record {
			margin: 0 0 30rpx;
			padding: 34rpx 30rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
			border-radius: 30rpx;
		}
	}
</style>