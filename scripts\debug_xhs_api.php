<?php
/**
 * 小红书API调试脚本
 * 
 * 使用方法：
 * php scripts/debug_xhs_api.php
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 获取项目根目录
$rootPath = dirname(__DIR__);

// 引入框架
define('APP_PATH', $rootPath . '/application/');
define('ROOT_PATH', $rootPath . '/');
define('EXTEND_PATH', $rootPath . '/extend/');
define('VENDOR_PATH', $rootPath . '/vendor/');
define('RUNTIME_PATH', $rootPath . '/runtime/');
define('LOG_PATH', RUNTIME_PATH . 'log/');
define('CACHE_PATH', RUNTIME_PATH . 'cache/');
define('TEMP_PATH', RUNTIME_PATH . 'temp/');
define('CONF_PATH', $rootPath . '/config/');

require_once $rootPath . '/thinkphp/base.php';

echo "=== 小红书API调试脚本 ===\n";

try {
    // 初始化应用
    \think\App::initCommon();
    
    // 获取小红书配置
    $xhs_config = \app\common\model\xilutour\Config::getMyConfig('xhs');
    
    echo "1. 检查配置信息...\n";
    echo "   AppID: " . ($xhs_config['xhs_appid'] ?? '未配置') . "\n";
    echo "   AppSecret: " . (isset($xhs_config['xhs_appsecret']) && !empty($xhs_config['xhs_appsecret']) ? '已配置' : '未配置') . "\n";
    echo "   状态: " . ($xhs_config['xhs_status'] ?? '未启用') . "\n";
    
    if (empty($xhs_config['xhs_appid']) || empty($xhs_config['xhs_appsecret'])) {
        echo "❌ 小红书配置不完整，请先在管理后台配置\n";
        exit(1);
    }
    
    echo "\n2. 测试不同的API请求格式...\n";
    
    $appId = $xhs_config['xhs_appid'];
    $appSecret = $xhs_config['xhs_appsecret'];
    
    // 测试不同的URL和参数格式
    $testCases = [
        [
            'name' => '正式环境 + form-data',
            'url' => 'https://miniapp.xiaohongshu.com/api/rmp/token',
            'params' => ['appid' => $appId, 'secret' => $appSecret],
            'method' => 'form'
        ],
        [
            'name' => '正式环境 + JSON',
            'url' => 'https://miniapp.xiaohongshu.com/api/rmp/token',
            'params' => ['appid' => $appId, 'secret' => $appSecret],
            'method' => 'json'
        ],
        [
            'name' => '沙箱环境 + form-data',
            'url' => 'https://miniapp-sandbox.xiaohongshu.com/api/rmp/token',
            'params' => ['appid' => $appId, 'secret' => $appSecret],
            'method' => 'form'
        ],
        [
            'name' => '沙箱环境 + JSON',
            'url' => 'https://miniapp-sandbox.xiaohongshu.com/api/rmp/token',
            'params' => ['appid' => $appId, 'secret' => $appSecret],
            'method' => 'json'
        ],
        [
            'name' => '带grant_type参数',
            'url' => 'https://miniapp.xiaohongshu.com/api/rmp/token',
            'params' => ['appid' => $appId, 'secret' => $appSecret, 'grant_type' => 'client_credentials'],
            'method' => 'form'
        ]
    ];
    
    foreach ($testCases as $index => $testCase) {
        echo "\n   测试 " . ($index + 1) . ": " . $testCase['name'] . "\n";
        echo "   URL: " . $testCase['url'] . "\n";
        echo "   参数: " . json_encode($testCase['params']) . "\n";
        
        try {
            $startTime = microtime(true);
            
            if ($testCase['method'] === 'json') {
                $response = \fast\Http::post($testCase['url'], json_encode($testCase['params']), [
                    'Content-Type: application/json',
                    'Accept: application/json'
                ]);
            } else {
                $response = \fast\Http::post($testCase['url'], $testCase['params']);
            }
            
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            echo "   响应时间: {$duration}ms\n";
            echo "   响应内容: " . $response . "\n";
            
            $result = json_decode($response, true);
            if ($result) {
                if (isset($result['success']) && $result['success'] === true) {
                    echo "   ✅ 请求成功！\n";
                    if (isset($result['data']['access_token'])) {
                        echo "   🎉 获取到access_token: " . substr($result['data']['access_token'], 0, 10) . "...\n";
                        break; // 成功了就不用继续测试了
                    }
                } else {
                    echo "   ❌ 请求失败: " . ($result['msg'] ?? '未知错误') . "\n";
                    if (isset($result['code'])) {
                        echo "   错误码: " . $result['code'] . "\n";
                    }
                }
            } else {
                echo "   ❌ 响应格式错误，无法解析JSON\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ 请求异常: " . $e->getMessage() . "\n";
        }
        
        // 避免请求过于频繁
        sleep(1);
    }
    
    echo "\n3. 网络连接测试...\n";
    
    // 测试网络连接
    $testUrls = [
        'https://miniapp.xiaohongshu.com/',
        'https://miniapp-sandbox.xiaohongshu.com/',
        'https://www.xiaohongshu.com/'
    ];
    
    foreach ($testUrls as $testUrl) {
        echo "   测试连接: {$testUrl}\n";
        try {
            $startTime = microtime(true);
            $response = \fast\Http::get($testUrl);
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            echo "   ✅ 连接成功，响应时间: {$duration}ms\n";
        } catch (Exception $e) {
            echo "   ❌ 连接失败: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n4. 建议和解决方案...\n";
    echo "   - 如果所有请求都失败，请检查网络连接和防火墙设置\n";
    echo "   - 如果返回'参数不合法'，请确认AppID和AppSecret是否正确\n";
    echo "   - 如果使用沙箱环境，请确认账号是否有沙箱权限\n";
    echo "   - 建议先在小红书开发者工具中测试API调用\n";
    echo "   - 可以尝试使用curl命令行工具直接测试API\n";
    
    echo "\n5. curl测试命令示例:\n";
    echo "   curl -X POST 'https://miniapp.xiaohongshu.com/api/rmp/token' \\\n";
    echo "        -H 'Content-Type: application/x-www-form-urlencoded' \\\n";
    echo "        -d 'appid={$appId}&secret={$appSecret}'\n";
    
} catch (Exception $e) {
    echo "❌ 调试脚本执行失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== 调试完成 ===\n";
?>
