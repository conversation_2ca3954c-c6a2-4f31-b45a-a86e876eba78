<template>
	<view class="xilu">
		<view class="container">
			<image src="../../static/icon/icon_failed.png" mode="aspectFill" class="img"></image>
			<view class="fs36 col-10 tc">核销失败！</view>
			<view class="fs30 col-5 tc mt25">{{msg}}</view>
			<navigator class="g-btn1" url="/pages/personal_center/personal_center" open-type="switchTab" hover-class="none">去核销</navigator>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				msg: ''
			};
		},
		onLoad(options) {
			this.msg = options.msg || '';
		},
	}
</script>

<style lang="less" scoped>
.xilu {
	.container{
		padding: 60rpx 75rpx;
	}
	.img{
		margin: 0 auto 30rpx;
		display: block;
		width: 200rpx;
		height: 190rpx;
	}
	.g-btn1{
		margin-top: 220rpx;
	}
}
</style>
