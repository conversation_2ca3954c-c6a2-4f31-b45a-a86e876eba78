<?php
namespace app\api\controller\xilutour;


use addons\xilutour\controller\WeixinMini;
use addons\xilutour\controller\XiaohongshuMini;
use app\common\controller\XilutourApi;
use app\common\library\Sms;
use app\common\model\xilutour\Config;
use app\common\model\xilutour\coupon\UserCoupon;
use app\common\model\xilutour\FooterView;
use app\common\model\xilutour\OrderDivide;
use app\common\model\xilutour\scenery\SceneryVerifier;
use app\common\model\xilutour\Third;
use app\common\model\xilutour\UserAccount;
use app\common\model\xilutour\UserCollection;
use fast\Random;
use think\Db;
use think\Exception;
use think\exception\ThrowableError;
use think\Hook;
use think\Validate;
use function fast\array_get;

/**
 * 用户模块控制器
 */
class User extends XilutourApi
{
    protected $noNeedLogin = ['wxlogin','get_mobile','mobilelogin','xhslogin','xhs_get_mobile','xhs_cache_manage','xhs_login_with_profile'];

    public function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
    }

    /**
     * 微信登录
     * @ApiParams (name="code", type="string", required=true, description="微信登录code")
     * @ApiParams (name="platform", type="string", required=true, description="登录平台")
     */
    public function wxlogin()
    {
        $param = $this->request->only(['code','platform']);
        $platform = array_get($param,'platform','wxmini');
        $wx = new WeixinMini();
        try {
            switch ($platform){
                case 'wxmini':
                    $config = \app\common\model\xilutour\Config::getMyConfig('wxmini');
                    if(!$config || !$config['wxmini_appid'] || !$config['wxmini_appid']){
                        throw new Exception("请正确配置微信信息");
                    }
                    $data = $wx->wxlogin($param['code']);

                    $third = Third::where('platform',$platform)->where('openid',$data['openid'])->find();
                    if(!$third){
                        $third_data = [
                            'platform'      =>  $platform,
                            'user_id'       =>  0,
                            'openname'      =>'',
                            'openid'        =>  $data['openid'],
                            'unionid'      =>  $data['unionid'] ?? '',
                            'access_token'  =>  $data['session_key'],
                            'logintime'     =>  time()
                        ];
                        $third = Third::create($third_data);
                    }else{
                        $third_data = [
                            'access_token'  =>  $data['session_key'],
                            'unionid'      =>  $data['unionid'] ?? '',
                            'logintime'     =>  time()
                        ];
                        if($third->user_id>0){
                            $user = \app\common\model\User::get(['id'=>$third->user_id]);
                            if(!$user){
                                $third_data['user_id'] = 0;
                            }
                        }
                        $third->save($third_data);
                    }
                    if($third->user_id>0){
                        #模拟登陆
                        $this->auth->direct($user->id);
                        $data = ['userinfo' => $this->auth->getUserinfo()];
                        $data['third'] = ['third_id'=>$third->id,'openname'=>$third->openname,'binding'=>1];
                    }else{
                        $data = ['userinfo'=>[]];
                        $data['third'] = ['third_id'=>$third->id,'openname'=>$third->openname,'binding'=>0];
                    }
                    break;
                case 'wxpublic':

                    break;
            }
        }catch (ThrowableError|Exception $e){
            $this->error($e->getMessage());
        }
        $this->success('获取session_key成功',$data);

    }


    /**
     * @ApiTitle (获取手机号)
     * @ApiParams (name="third_id", type="int", required=true, description="第三方id")
     * @ApiParams (name="code", type="string", required=true, description="code")
     */
    public function get_mobile()
    {
        $param = $this->request->only(['third_id','code','puser_id']);
        $iv = $this->request->param('iv',null,null);
        $encryptedData = $this->request->param('encryptedData',null,null);
        $wx = new WeixinMini();
        $third = Third::where('id',array_get($param,'third_id'))->find();
        if(!$third) $this->error('未找到third数据',[],40001);
        if($third->user_id>0){
            $user = \app\common\model\User::get($third->user_id);
            if($user){
                $this->auth->direct($third->user_id);
                $third = ['third_id'=>$third->id,'binding'=>1];
                $this->success('手机号已经注册成功',['userinfo'=>$this->auth->getUserinfo(),'third'=>$third]);
            }
        }

        Db::startTrans();
        try {
            //新版本授权
            if($code = array_get($param,'code')){
                $data = $wx->getPhoneNumber($code);
            }else if($iv && $encryptedData){
                $data = $wx->wxEncrypted($third['access_token'],$iv,$encryptedData);
            }else{
                Db::rollback();
                throw new Exception("获取手机号参数错误");
            }
            if(!$data){
                Db::rollback();
                throw new Exception("获取手机号失败");
            }
            $user = \app\common\model\User::where('mobile',$data['phoneNumber'])->find();
            if ($user) {
                if ($user->status != 'normal') {
                    Db::rollback();
                    $this->error(__('Account is locked'));
                }
                //如果已经有账号则直接登录
                $ret = $this->auth->direct($user->id);
            } else {
                $user_config = \app\common\model\xilutour\Config::getMyConfig('user');
                $extend = [];
                if(isset($user_config['avatar'])){
                    $extend['avatar'] = cdnurl($user_config['avatar'],true);
                }
                $extend['nickname'] = preg_match("/^1[3-9]{1}\d{9}$/", $data['phoneNumber']) ? substr_replace($data['phoneNumber'], '****', 3, 4) : $data['phoneNumber'];
                $extend['source'] = 'wxmini'; // 设置用户来源为微信小程序
                $ret = $this->auth->register($data['phoneNumber'], Random::alnum(), '', $data['phoneNumber'], $extend);
            }
            $puser_id = array_get($param,'puser_id');
            if ($ret) {
                $userinfo = $this->auth->getUserinfo();
                #更新third表字段
                $third->user_id = $userinfo['id'];
                $third->save();
                #创建用户消息表
                UserAccount::addAccount($userinfo['id'],$puser_id);
                $third = ['third_id'=>$third->id,'binding'=>1];
                $data = ['userinfo' => $userinfo,'third'=>$third];
            } else {
                Db::rollback();
                $this->error($this->auth->getError());
            }
        }catch (ThrowableError $e){
            Db::rollback();
            $this->error($e->getMessage());
        }catch (Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        Db::commit();
        $this->success(__('登录成功'), $data);
    }

    /**
     * 手机验证码登录
     *
     * @ApiMethod (POST)
     * @param string $mobile  手机号
     * @param string $captcha 验证码
     */
    public function mobilelogin()
    {
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('code');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('手机号格式错误'));
        }
        if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
            $this->error(__('验证码错误'));
        }
        $user = \app\common\model\User::getByMobile($mobile);
        if ($user) {
            if ($user->status != 'normal') {
                $this->error(__('账号已被锁定'));
            }
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            $extend['nickname'] = preg_match("/^1[3-9]{1}\d{9}$/", $mobile) ? substr_replace($mobile, '****', 3, 4) : $mobile;
            $extend['source'] = 'wxmini'; // 设置用户来源为微信小程序
            $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, $extend);
        }
        $puser_id = $this->request->post('puser_id');
        if ($ret) {
            Sms::flush($mobile, 'mobilelogin');
            $userinfo = $this->auth->getUserinfo();
            #创建用户消息表
            UserAccount::addAccount($userinfo['id'],$puser_id);
            $data = ['userinfo' => $userinfo];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }


    /**
     * 用户信息
     */
    public function info(){
        $user = $this->auth->getUser()->hidden(['group_id','password','salt','level','bio','token','loginip','joinip','jointime','successions','maxsuccessions','prevtime']);
        //$user['mobile'] = substr_replace($user['mobile'],'****',3,4);
        #收藏
        $user->collection_count = UserCollection::getCollectionCount($user,[UserCollection::TYPE_TOUR,UserCollection::TYPE_SCENERY]);
        #优惠券
        $user->coupon_count = UserCoupon::getCount(['user_id'=>$user->id]);
        #景点订单
        $user->scenery_order_count = \app\common\model\xilutour\order\SceneryOrder::getCount($user->id);
        #旅游订单
        $user->tour_order_count = \app\common\model\xilutour\order\TourOrder::getCount($user->id);
        #核销权限
        $user->verifier_status = SceneryVerifier::isVerifier($user->mobile);
        #消息
        $account = UserAccount::where('user_id',$user->id)->field('(user_message+system_message) as total_message')->find();
        $user->user_message = $account['total_message'];
        $this->success('查询成功',$user);
    }

    /**
     * 门店信息-所有景点
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function verifier_shop(){
        $params = $this->request->param('');
        $user = $this->auth->getUser();
        $companyId = SceneryVerifier::where('mobile',$user['mobile'])->value('company_id');
        $params['company_id'] = $companyId;
        $shopList = \app\common\model\xilutour\scenery\Scenery::shopLists($params);
        $this->success('',$shopList);
    }

    /**
     * @ApiTitle (修改会员个人信息)
     * @ApiRoute (/api/xilumarket.user/profile)
     * @ApiMethod (POST)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     * @ApiParams (name="avatar", type="string", required=true, description="头像")
     * @ApiParams (name="nickname", type="string", required=true, description="昵称")
     */
    public function profile()
    {
        $user = $this->auth->getUser();
        $user->visible(['bio','avatar','nickname','mobile','gender']);
        if($this->request->isPost()) {
            $nickname = $this->request->post('nickname');
            $avatar = $this->request->post('avatar', '', 'trim,strip_tags,htmlspecialchars');
            $gender = $this->request->post('gender', '');
            $user->nickname = $nickname;
            $user->avatar = $avatar;
            $user->gender = $gender;
            $user->allowField(['avatar','nickname','gender'])->save();

            $this->success('', $user);
        }
        $this->success('',$user);
    }

    /**
     * 足迹
     */
    public function footer_view(){
        $params = $this->request->param('');
        $this->success('查询成功',FooterView::lists($params,$this->auth->id));
    }

    /**
     * 我的团队
     * @throws \think\exception\DbException
     */
    public function myteam(){
        $params = $this->request->param('');
        $pagesize = array_get($params,'pagesize');
        $lists = UserAccount::field("id,user_id,first_user_id,second_user_id,bindtime")->with(['user'=>function($q){
            $q->withField(['id','nickname','avatar']);
        }])->where('first_user_id|second_user_id',$this->auth->id)
            ->order('bindtime','desc')
            ->paginate($pagesize)
            ->each(function ($row){
                $row->append(['bindtime_text']);
            });
        $this->success('',$lists);
    }

    /**
     * 账号
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function account(){
        $userId = $this->auth->id;
        $account = UserAccount::field("id,user_id,money,total_money")->where('user_id',$userId)->find();
        $freezeMoney = OrderDivide::where('first_user_id',$userId)->where('status',1)->sum('first_money');
        $freezeMoney2 = OrderDivide::where('second_user_id',$userId)->where('status',1)->sum('second_money');
        $account->freeze_money  = bcadd($freezeMoney,$freezeMoney2,2);
        $this->success('',$account);
    }

    /**
     * 佣金日志
     */
    public function money_log(){
        $params = $this->request->param('');
        $pagesize = array_get($params,'pagesize');
        $lists = \app\common\model\xilutour\MoneyLog::where('user_id',$this->auth->id)
            ->order('id','desc')
            ->paginate($pagesize)
            ->each(function ($row){
                $row->money_text = bcadd(0,abs($row->money),2);
                $row->extra_text = json_decode($row->extra,true);
            });
        $this->success('查询成功',$lists);
    }

    /**
     * 提现申请
     */
    public function withdraw()
    {
        $params = $this->request->post('');
        try {
            $result = \app\common\model\xilutour\finance\Withdraw::withdraw($params, $this->auth->getUser());
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('提现成功', $result);
    }

    /**
     * 提现记录
     */
    public function withdraw_log(){
        $params = $this->request->param('');
        $pagesize = array_get($params,'pagesize');
        $lists = \app\common\model\xilutour\finance\Withdraw::where('user_id',$this->auth->id)
            ->order('id','desc')
            ->paginate($pagesize);
        $this->success('查询成功',$lists);
    }

    /**
     * 海报
     */
    public function poster(){
        $uid = $this->auth->id;
        $wx = new WeixinMini();
        $page = "pages/index/index";
        $res = $wx->getUnlimited("uid=".$uid,$page);
        if(!$res) $this->error('获取小程序二维码失败');
        $root = '.';
        if(!get_addon_config('alioss')){
            $path = "/uploads/".date('Ymd').'/';
        }else{
            $path = "/uploads/xilutour/".date('Ymd').'/';
        }
        if (!file_exists($root . $path)) {
            @mkdir($root . $path, 0777, true);
        }
        $filename = $uid.'.png';
        $qrcode = $path.$filename;
        $alioss = get_addon_info("alioss");
        if($alioss && $alioss['state']==1){
            Common::uploadAlioss($qrcode, $res);
        }else{
            file_put_contents($root.$qrcode,$res);
        }
        $shareConfig = Config::getMyConfig('share');
        $this->success('',[
            'text1' => $this->auth->nickname,
            'text2' => $shareConfig['poster_text'],
            'poster_img'=> cdnurl($shareConfig['poster_image'],true),
            'img1'=> cdnurl($this->auth->avatar,true),
            'img2'=> cdnurl($qrcode,true),
        ]);
    }

    /**
     * 小红书登录
     * @ApiParams (name="code", type="string", required=true, description="小红书登录code")
     * @ApiParams (name="platform", type="string", required=true, description="登录平台")
     */
    public function xhslogin()
    {
        $param = $this->request->only(['code','platform']);
        $platform = array_get($param,'platform','xhs');
        $xhs = new XiaohongshuMini();

        try {
            switch ($platform){
                case 'xhs':
                    $config = \app\common\model\xilutour\Config::getMyConfig('xhs');
                    if(!$config || !$config['xhs_appid'] || !$config['xhs_appsecret']){
                        throw new Exception("请正确配置小红书信息");
                    }
                    $data = $xhs->xhsLogin($param['code']);

                    $third = Third::where('platform',$platform)->where('openid',$data['openid'])->find();
                    if(!$third){
                        $third_data = [
                            'platform'      =>  $platform,
                            'user_id'       =>  0,
                            'openname'      =>'',
                            'openid'        =>  $data['openid'],
                            'unionid'       =>  $data['unionid'] ?? '',
                            'access_token'  =>  $data['access_token'] ?? '',
                            'refresh_token' =>  $data['refresh_token'] ?? '',
                            'expires_in'    =>  $data['expires_in'] ?? 7200,
                            'logintime'     =>  time()
                        ];
                        $third = Third::create($third_data);
                    }else{
                        $third_data = [
                            'access_token'  =>  $data['access_token'] ?? '',
                            'refresh_token' =>  $data['refresh_token'] ?? '',
                            'unionid'       =>  $data['unionid'] ?? '',
                            'expires_in'    =>  $data['expires_in'] ?? 7200,
                            'logintime'     =>  time()
                        ];
                        if($third->user_id>0){
                            $user = \app\common\model\User::get(['id'=>$third->user_id]);
                            if(!$user){
                                $third_data['user_id'] = 0;
                            }
                        }
                        $third->save($third_data);
                    }
                    break;
                default:
                    throw new Exception("不支持的登录平台");
            }

            if($third->user_id>0){
                $user = \app\common\model\User::get(['id'=>$third->user_id]);
                if($user && $user->status == 'normal'){
                    $this->auth->direct($user->id);
                    $userinfo = $this->auth->getUserinfo();
                    $third = ['third_id'=>$third->id,'binding'=>1];
                    $data = ['userinfo' => $userinfo,'third'=>$third];
                }else{
                    $third = ['third_id'=>$third->id,'binding'=>0];
                    $data = ['userinfo' => [],'third'=>$third];
                }
            }else{
                $third = ['third_id'=>$third->id,'binding'=>0];
                $data = ['userinfo' => [],'third'=>$third];
            }
        }catch (ThrowableError $e){
            $this->error($e->getMessage());
        }catch (Exception $e){
            $this->error($e->getMessage());
        }
        $this->success(__('登录成功'), $data);
    }

    /**
     * 小红书获取手机号
     * @ApiParams (name="third_id", type="int", required=true, description="第三方登录ID")
     * @ApiParams (name="iv", type="string", required=false, description="加密向量")
     * @ApiParams (name="encryptedData", type="string", required=false, description="加密数据")
     * @ApiParams (name="code", type="string", required=false, description="手机号授权code")
     * @ApiParams (name="puser_id", type="int", required=false, description="推荐人ID")
     */
    public function xhs_get_mobile()
    {
        $param = $this->request->only(['third_id','iv','encryptedData','code','puser_id']);
        $third_id = array_get($param,'third_id');
        $third = Third::get($third_id);
        if(!$third || $third->platform != 'xhs'){
            $this->error('第三方登录信息不存在');
        }

        Db::startTrans();
        try {
            $xhs = new XiaohongshuMini();

            // 获取手机号
            if(array_get($param,'code')){
                // 使用新版API获取手机号
                $data = $xhs->decryptPhoneNumber($third->access_token, '', array_get($param,'code'));
            }else{
                // 使用旧版解密方式
                $data = $xhs->decryptPhoneNumber($third->access_token, array_get($param,'iv'), array_get($param,'encryptedData'));
            }

            $user = \app\common\model\User::where('mobile',$data['phoneNumber'])->find();
            if ($user) {
                if ($user->status != 'normal') {
                    Db::rollback();
                    $this->error(__('Account is locked'));
                }
                //如果已经有账号则直接登录
                $ret = $this->auth->direct($user->id);
            } else {
                $user_config = \app\common\model\xilutour\Config::getMyConfig('user');
                $extend = [];
                if(isset($user_config['avatar'])){
                    $extend['avatar'] = cdnurl($user_config['avatar'],true);
                }
                $extend['nickname'] = preg_match("/^1[3-9]{1}\d{9}$/", $data['phoneNumber']) ? substr_replace($data['phoneNumber'], '****', 3, 4) : $data['phoneNumber'];
                $extend['source'] = 'xhs'; // 设置用户来源为小红书
                $ret = $this->auth->register($data['phoneNumber'], Random::alnum(), '', $data['phoneNumber'], $extend);
            }
            $puser_id = array_get($param,'puser_id');
            if ($ret) {
                $userinfo = $this->auth->getUserinfo();
                #更新third表字段
                $third->user_id = $userinfo['id'];
                $third->save();
                #创建用户消息表
                UserAccount::addAccount($userinfo['id'],$puser_id);
                $third = ['third_id'=>$third->id,'binding'=>1];
                $data = ['userinfo' => $userinfo,'third'=>$third];
            } else {
                Db::rollback();
                $this->error($this->auth->getError());
            }
        }catch (ThrowableError $e){
            Db::rollback();
            $this->error($e->getMessage());
        }catch (Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        Db::commit();
        $this->success(__('登录成功'), $data);
    }

    /**
     * 小红书登录并保存用户信息（不需要手机号）
     * @ApiParams (name="code", type="string", required=true, description="小红书登录code")
     * @ApiParams (name="userInfo", type="object", required=true, description="用户信息对象")
     * @ApiParams (name="platform", type="string", required=true, description="登录平台")
     * @ApiParams (name="puser_id", type="int", required=false, description="推荐人ID")
     */
    public function xhs_login_with_profile()
    {
        $param = $this->request->only(['code','userInfo','platform','puser_id']);
        $platform = array_get($param,'platform','xhs');
        $userInfo = array_get($param,'userInfo',[]);
        $puser_id = array_get($param,'puser_id',0);

        if (empty($userInfo) || empty($userInfo['nickName'])) {
            $this->error('用户信息不能为空');
        }

        $xhs = new XiaohongshuMini();

        Db::startTrans();
        try {
            switch ($platform){
                case 'xhs':
                    $config = \app\common\model\xilutour\Config::getMyConfig('xhs');
                    if(!$config || !$config['xhs_appid'] || !$config['xhs_appsecret']){
                        throw new Exception("请正确配置小红书信息");
                    }

                    // 获取小红书登录信息
                    $data = $xhs->xhsLogin($param['code']);
                    $openid = $data['openid'];

                    // 处理用户信息
                    $processedUserInfo = $xhs->processUserProfile($userInfo, $openid);

                    $third = Third::where('platform',$platform)->where('openid',$openid)->find();
                    if(!$third){
                        $third_data = [
                            'platform'      =>  $platform,
                            'user_id'       =>  0,
                            'openname'      =>  $processedUserInfo['nickname'],
                            'openid'        =>  $openid,
                            'unionid'       =>  $data['unionid'] ?? '',
                            'access_token'  =>  $data['access_token'] ?? '',
                            'refresh_token' =>  $data['refresh_token'] ?? '',
                            'expires_in'    =>  $data['expires_in'] ?? 7200,
                            'logintime'     =>  time()
                        ];
                        $third = Third::create($third_data);
                    }else{
                        $third_data = [
                            'openname'      =>  $processedUserInfo['nickname'],
                            'access_token'  =>  $data['access_token'] ?? '',
                            'refresh_token' =>  $data['refresh_token'] ?? '',
                            'unionid'       =>  $data['unionid'] ?? '',
                            'expires_in'    =>  $data['expires_in'] ?? 7200,
                            'logintime'     =>  time()
                        ];
                        if($third->user_id>0){
                            $user = \app\common\model\User::get(['id'=>$third->user_id]);
                            if(!$user){
                                $third_data['user_id'] = 0;
                            }
                        }
                        $third->save($third_data);
                    }

                    // 如果已经绑定用户，直接登录
                    if($third->user_id>0){
                        $user = \app\common\model\User::get(['id'=>$third->user_id]);
                        if($user && $user->status == 'normal'){
                            $this->auth->direct($user->id);
                            $userinfo = $this->auth->getUserinfo();
                            $third_info = ['third_id'=>$third->id,'binding'=>1];
                            $data = ['userinfo' => $userinfo,'third'=>$third_info];
                        }else{
                            $third_info = ['third_id'=>$third->id,'binding'=>0];
                            $data = ['userinfo' => [],'third'=>$third_info];
                        }
                    }else{
                        // 创建新用户
                        $user_config = \app\common\model\xilutour\Config::getMyConfig('user');
                        $extend = [];

                        // 设置用户头像
                        if(!empty($processedUserInfo['avatar'])){
                            $extend['avatar'] = $processedUserInfo['avatar'];
                        } elseif(isset($user_config['avatar'])){
                            $extend['avatar'] = cdnurl($user_config['avatar'],true);
                        }

                        // 设置用户昵称
                        $extend['nickname'] = $processedUserInfo['nickname'] ?: '小红书用户';

                        // 设置用户来源
                        $extend['source'] = 'xhs';

                        // 设置性别
                        if(isset($processedUserInfo['gender'])){
                            $extend['gender'] = $processedUserInfo['gender'];
                        }

                        // 使用openid作为用户名（因为没有手机号）
                        $username = 'xhs_' . substr($openid, -8);
                        $ret = $this->auth->register($username, Random::alnum(), '', '', $extend);

                        if ($ret) {
                            $userinfo = $this->auth->getUserinfo();
                            // 更新third表字段
                            $third->user_id = $userinfo['id'];
                            $third->save();
                            // 创建用户消息表
                            UserAccount::addAccount($userinfo['id'],$puser_id);
                            $third_info = ['third_id'=>$third->id,'binding'=>1];
                            $data = ['userinfo' => $userinfo,'third'=>$third_info];
                        } else {
                            Db::rollback();
                            $this->error($this->auth->getError());
                        }
                    }
                    break;
                default:
                    throw new Exception("不支持的登录平台");
            }

        }catch (ThrowableError $e){
            Db::rollback();
            $this->error($e->getMessage());
        }catch (Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }

        Db::commit();
        $this->success(__('登录成功'), $data);
    }

    /**
     * 小红书缓存管理接口（仅用于调试）
     * @ApiParams (name="action", type="string", required=true, description="操作类型：info|clear|refresh|test")
     */
    public function xhs_cache_manage()
    {
        $action = $this->request->post('action', 'info');
        $xhs = new XiaohongshuMini();

        try {
            switch ($action) {
                case 'info':
                    // 获取缓存信息
                    $cacheInfo = $xhs->getCacheInfo();
                    $this->success('缓存信息获取成功', $cacheInfo);
                    break;

                case 'clear':
                    // 清除缓存
                    $result = $xhs->clearAccessTokenCache();
                    $this->success('缓存清除' . ($result ? '成功' : '失败'), ['cleared' => $result]);
                    break;

                case 'refresh':
                    // 强制刷新缓存
                    $xhs->clearAccessTokenCache();
                    $newToken = $xhs->getAccessToken();
                    $this->success('缓存刷新成功', [
                        'new_token_preview' => substr($newToken, 0, 10) . '...',
                        'cache_info' => $xhs->getCacheInfo()
                    ]);
                    break;

                case 'test':
                    // 测试连接
                    $testResult = $xhs->testConnection();
                    if ($testResult['api_check']) {
                        $this->success('连接测试成功', $testResult);
                    } else {
                        $this->error('连接测试失败', $testResult);
                    }
                    break;

                default:
                    $this->error('不支持的操作类型');
            }
        } catch (Exception $e) {
            $this->error('操作失败：' . $e->getMessage());
        }
    }
}
