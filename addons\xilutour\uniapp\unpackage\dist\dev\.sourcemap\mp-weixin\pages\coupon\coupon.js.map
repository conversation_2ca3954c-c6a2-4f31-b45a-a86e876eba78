{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/coupon/coupon.vue?46b9", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/coupon/coupon.vue?9725", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/coupon/coupon.vue?4d23", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/coupon/coupon.vue?8622", "uni-app:///pages/coupon/coupon.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/coupon/coupon.vue?d1f5", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/coupon/coupon.vue?e70a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "tabIdx", "couponList", "couponListMore", "page", "onLoad", "onReachBottom", "methods", "tabClick", "refresh", "fetch", "tab", "pagesize"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+zB,CAAgB,+xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0Cn1B;EACAC;IACA;MACAC;MACAC;MACAC;QAAAC;MAAA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QAAAL;MAAA;MACA;IACA;IACAM;MACA;QAAAC;QAAAC;MAAA,4DAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAAkhD,CAAgB,m5CAAG,EAAC,C;;;;;;;;;;;ACAtiD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/coupon/coupon.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/coupon/coupon.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./coupon.vue?vue&type=template&id=032f11f4&scoped=true&\"\nvar renderjs\nimport script from \"./coupon.vue?vue&type=script&lang=js&\"\nexport * from \"./coupon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./coupon.vue?vue&type=style&index=0&id=032f11f4&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"032f11f4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/coupon/coupon.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./coupon.vue?vue&type=template&id=032f11f4&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./coupon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./coupon.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"page-head bg-white\">\r\n\t\t\t<view class=\"g_tab flex-box\">\r\n\t\t\t\t<view class=\"item flex-1\" :class=\"{'active': tabIdx == 0}\" @click=\"tabClick(0)\">全部</view>\r\n\t\t\t\t<view class=\"item flex-1\" :class=\"{'active': tabIdx == 1}\" @click=\"tabClick(1)\">待使用</view>\r\n\t\t\t\t<view class=\"item flex-1\" :class=\"{'active': tabIdx == 2}\" @click=\"tabClick(2)\">已使用</view>\r\n\t\t\t\t<view class=\"item flex-1\" :class=\"{'active': tabIdx == 3}\" @click=\"tabClick(3)\">已过期</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"xilu_coupon\" :class=\"coupon.state_text==1?'':'disabled'\" v-for=\"(coupon,index) in couponList\" :key=\"index\">\r\n\t\t\t\t<image src=\"/static/icon/icon_coupon_bg1.png\" mode=\"aspectFill\" class=\"bg\"></image>\r\n\t\t\t\t<view class=\"inner flex-box\">\r\n\t\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t\t<view class=\"fwb mb20\">\r\n\t\t\t\t\t\t\t<text class=\"fs24\">¥</text>\r\n\t\t\t\t\t\t\t<text class=\"fs50\">{{coupon.coupon.money}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"man\">满{{coupon.coupon.at_least}}可用</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"right flex-1 flex-box\">\r\n\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t<view class=\"fs30 mb20\">{{coupon.coupon.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"fs24\">{{coupon.endtime_text}}到期</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"use\" v-if=\"coupon.state_text==1\">使用</view>\n\t\t\t\t\t\t<image v-else-if=\"coupon.state_text==2\" src=\"../../static/icon/icon_coupon1.png\" mode=\"aspectFill\" class=\"icon_disabled\"></image>\n\t\t\t\t\t\t<image v-else src=\"../../static/icon/icon_coupon2.png\" mode=\"aspectFill\" class=\"icon_disabled\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"g-btn3-wrap\">\n\t\t\t\t<view class=\"g-btn3\" @click=\"fetch\">{{couponListMore.text}}</view>\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttabIdx: 0,\n\t\t\t\tcouponList:[],\n\t\t\t\tcouponListMore:{page:1}\r\n\t\t\t};\r\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.fetch();\n\t\t},\n\t\tonReachBottom() {\n\t\t\tthis.fetch();\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttabClick(i) {\r\n\t\t\t\tthis.tabIdx = i;\n\t\t\t\tthis.refresh()\r\n\t\t\t},\n\t\t\trefresh(){\n\t\t\t\tthis.couponList = [];\n\t\t\t\tthis.couponListMore = {page:1};\n\t\t\t\tthis.fetch();\n\t\t\t},\n\t\t\tfetch(){\n\t\t\t\tthis.$util.fetch(this, 'xilutour.coupon/mycoupons', {tab: this.tabIdx,pagesize:10}, 'couponListMore', 'couponList', 'data', data=>{\n\t\t\t\t  \n\t\t\t\t})\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.xilu {\r\n\t\t&_coupon {\r\n\t\t\tposition: relative;\r\n\t\t\tmargin: 0 0 40rpx;\r\n\t\t\twidth: 670rpx;\r\n\t\t\theight: 194rpx;\r\n\t\t\tcolor: #FFF;\r\n\r\n\t\t\t.bg {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 670rpx;\r\n\t\t\t\theight: 194rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.inner {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tright: 0;\r\n\t\t\t\tbottom: 0;\r\n\r\n\t\t\t\t.left {\r\n\t\t\t\t\tpadding: 30rpx 30rpx 0;\r\n\t\t\t\t\twidth: 218rpx;\r\n\t\t\t\t\theight: 194rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\r\n\t\t\t\t\t.man {\r\n\t\t\t\t\t\twidth: 156rpx;\r\n\t\t\t\t\t\theight: 44rpx;\r\n\t\t\t\t\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\t\t\t\t\tborder-radius: 22rpx;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\tline-height: 44rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.right {\r\n\t\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\t\theight: 194rpx;\r\n\t\t\t\t\tcolor: #FDFEFE;\r\n\r\n\t\t\t\t\t.use {\r\n\t\t\t\t\t\tmargin: 0 0 0 25rpx;\r\n\t\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\t\tbackground: rgba(255, 255, 255, 0.3);\r\n\t\t\t\t\t\tborder-radius: 34rpx;\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\tline-height: 80rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.icon_disabled{\r\n\t\t\t\t\t\tmargin: 0 0 0 25rpx;\r\n\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\twidth: 110rpx;\r\n\t\t\t\t\t\theight: 110rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_coupon.disabled {\r\n\t\t\tcolor: #CCCCCC;\r\n\r\n\t\t\t.inner {\r\n\t\t\t\t.left {\r\n\t\t\t\t\t.man {\r\n\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\tbackground: #E5E5E5;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.right {\r\n\t\t\t\t\tcolor: #E5E5E5;\r\n\t\t\t\t\t.fs30 {\r\n\t\t\t\t\t\tcolor: #CCCCCC;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.container {\r\n\t\t\tpadding: 136rpx 40rpx 40rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./coupon.vue?vue&type=style&index=0&id=032f11f4&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./coupon.vue?vue&type=style&index=0&id=032f11f4&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341202\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}