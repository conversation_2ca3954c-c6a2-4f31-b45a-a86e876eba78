<template>
	<view class="xilu">
		<view class="page-foot" @click="bindNext">
			<view class="g-btn1">下一步</view>
		</view>
		<view class="container">
			<view class="xilu_travel flex-box">
				<image :src="tour.thumb_image_text" mode="aspectFill" class="img mr30"></image>
				<view class="flex-1">
					<view class="fs36 col-10 mb40">{{tour.name}}</view>
					<view class="fs30 col-5 mb20" v-if="chooseDate">已选：{{chooseDate.appoint_date_text}}</view>
					<view class="fs30 col-5 mb20" v-else>亲选择行程</view>
					<view class="fs26 col-89">报名截止时间出发前{{tour.enroll_days}}天</view>
				</view>
			</view>
			<!-- <view class="xilu_title">行程套餐</view>
			<view class="xilu_specification">喀纳斯小环线</view>
			<view class="xilu_title">批次套餐</view>
			<view class="xilu_specification">6～20人旅行团</view> -->
			<view class="xilu_title">出行人数</view>
			<view class="xilu_number flex-box">
				<view class="mr40">成人</view>
				<view class="flex-1" v-if="chooseDate">¥{{chooseDate.salesprice}}</view>
				<image src="/static/icon/icon_jian.png" @click="bindAdultChangeCount('minus')" mode="aspectFit"></image>
				<view class="num">{{buyAdultCount}}</view>
				<image src="/static/icon/icon_jia.png" @click="bindAdultChangeCount('plus')" mode="aspectFit"></image>
			</view>
			<view class="xilu_number flex-box">
				<view class="mr40">儿童</view>
				<view class="flex-1" v-if="chooseDate">¥{{chooseDate.child_salesprice}}</view>
				<image src="/static/icon/icon_jian.png" @click="bindChildChangeCount('minus')" mode="aspectFit"></image>
				<view class="num">{{buyChildCount}}</view>
				<image src="/static/icon/icon_jia.png" @click="bindChildChangeCount('plus')" mode="aspectFit"></image>
			</view>

			<view class="xilu_title">选择出发日期</view>
			<view class="xilu_month">
				<view class="item" :class="{active:tourDatesIndex==index}" v-for="(item,index) in tourDates" :key="index" @click="bindMonthChange(index)">
					<view>
						<text class="fs36">{{item.month}}</text>
						<text class="fs24">月</text>
					</view>
					<view class="fs24">{{item.year}}</view>
				</view>
			</view>

			<view class="xilu_day flex-box flex-wrap">
				<view class="item" :class="item.sku_count<=0?'disabled':(chooseDate&&chooseDate.id==item.id?'active':'')" v-for="(item,index) in dateList" :key="index" @click="bindArrangementChange(item)">
					<view class="mb10">剩{{item.sku_count}}名</view>
					<text>{{item.appoint_date_text}}</text>
				</view>
				<!-- <view class="item active">
					<view class="mb10">剩5名</view>
					<text>2023.12.01</text>
				</view>
				<view class="item disabled">
					<view class="mb10">已满员</view>
					<text>2023.12.01</text>
				</view>
				<view class="item disabled">
					<view class="mb10">已满员</view>
					<text>2023.12.01</text>
				</view> -->
			</view>
		</view>
	</view>
</template>

<script>
	var validate = require("../../xilu/validate.js");
	export default {
		data() {
			return {
				tourId:0,
				tourDateId: 0,
				tour:{
					name:'',
					thumb_image_text: '',
					enroll_days: 0
				},
				buyAdultCount:1,
				buyChildCount:0,
				tourDates:[],
				tourDatesIndex:0,
				dateList:[],
				chooseDate:null
			};
		},
		onLoad(options) {
			this.tourId = options.tour_id;
			this.tourDateId = options.tour_date_id || 0;
			this.fetchDates();
		},
		methods:{
			//基本信息
			fetchDates(){
				this.$core.post({url: 'xilutour.tour/tour_date',data: {tour_id: this.tourId,tour_date_id: this.tourDateId},loading: false,success: ret => {
						this.tour = ret.data.tour;
						this.tourDates = ret.data.tour_dates;
						this.tourDatesIndex = ret.data.appoint_index;
						if(ret.data.tour_dates.length>0){
							this.fetchDateArrangement(ret.data.tour_dates[ret.data.appoint_index].date)
						}
					},fail: err => {
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel:false,
							success(res) {
								if(res.confirm){
									uni.navigateBack({});
								}
							}
						})
						return false;
					}
				});
			},
			//排期
			fetchDateArrangement(date){
				this.$core.post({url: 'xilutour.tour/date_arrange',data: {tour_id: this.tourId,date:date},success: ret => {
						this.dateList = ret.data;
						if(ret.data.length>0){
							for(let i=0;i<ret.data.length;i++){
								if(!this.chooseDate && this.tourDateId == ret.data[i].id){
									this.chooseDate = ret.data[i];
									break;
								}
							}
							if(!this.chooseDate){
								this.chooseDate = ret.data[0];
							}
						}
					},fail: err => {
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel:false,
							success(res) {
								if(res.confirm){
									uni.navigateBack({});
								}
							}
						})
						return false;
					}
				});
			},
			//切换年月
			bindMonthChange(index){
				this.tourDatesIndex = index;
				this.fetchDateArrangement(this.tourDates[index].date);
			},
			//切换排期选项
			bindArrangementChange(item){
				this.chooseDate = item;
			},
			
			//成人加减人数
			bindAdultChangeCount(type){
				if(type == 'plus'){
					this.buyAdultCount = ++this.buyAdultCount;
				}else{
					if(this.buyAdultCount<=0){
						return false;
					}
					this.buyAdultCount = --this.buyAdultCount;
				}
			},
			//儿童加减人数
			bindChildChangeCount(type){
				if(type == 'plus'){
					this.buyChildCount = ++this.buyChildCount;
				}else{
					if(this.buyChildCount<=0){
						return false;
					}
					this.buyChildCount = --this.buyChildCount;
				}
			},
			
			bindNext(){
				let tour = {
					tour_id: this.tourId,
					buy_adult_count: this.buyAdultCount,
					buy_child_count: this.buyChildCount,
					tour_date_id: this.chooseDate ? this.chooseDate.id: 0,
				}
				var rule = [
					{name: 'buy_adult_count',nameChn: '成人数量',rules: ['require','gt:0'],errorMsg: {require: '请添加成人数量',length: "成人数量不到为空"}},
					{name: 'tour_date_id',nameChn: '出发日期',rules: ['gt:0'],errorMsg: {gt: '请选择出发日期'},},
					];
				// 是否全部通过，返回Boolean
				if(!validate.check(tour,rule)){
					uni.showToast({
						title: validate.getError()[0],
						icon:'none'
					});
					return ;
				}
				
				uni.navigateTo({
					url: '/pages/travel_pay/travel_pay',
					success(res) {
						res.eventChannel.emit('tourTransfor', tour)
					}
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		&_travel {
			.img {
				display: block;
				width: 180rpx;
				height: 180rpx;
				border-radius: 15rpx;
			}
		}

		&_title {
			margin: 50rpx 0 30rpx;
			font-size: 36rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: bold;
			color: #101010;
			line-height: 40rpx;
		}

		&_specification {
			padding: 0 20rpx;
			display: inline-block;
			height: 90rpx;
			background: rgba(0, 193, 175, 0.1);
			border-radius: 15rpx;
			border: 2rpx solid var(--normal);
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			color: var(--normal);
			line-height: 88rpx;
		}

		&_number {
			font-size: 34rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #101010;
			line-height: 36rpx;

			image {
				display: block;
				width: 40rpx;
				height: 40rpx;
			}

			.num {
				width: 62rpx;
				font-size: 36rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #101010;
				line-height: 40rpx;
				text-align: center;
			}
		}

		&_number+&_number {
			margin-top: 44rpx;
		}

		&_month {
			padding: 0 0 30rpx;
			overflow-x: scroll;
			white-space: nowrap;
			overflow-y: hidden;

			.item {
				display: inline-block;
				padding: 10rpx 0 0;
				width: 95rpx;
				height: 95rpx;
				background: #F6F9FF;
				border-radius: 10rpx;
				text-align: center;
				font-size: 0;
				vertical-align: top;

				view {
					color: #555555;
				}

				text {
					color: #101010;
				}
			}

			.item+.item {
				margin-left: 20rpx;
			}

			.item.active {
				height: 92rpx;
				background: rgba(5, 185, 174, 0.1);
				border: 2rpx solid var(--normal);

				view {
					color: var(--normal);
				}

				text {
					color: var(--normal);
				}
			}
		}

		&_day {
			.item {
				padding: 15rpx 0 0 30rpx;
				margin: 0 20rpx 20rpx 0;
				width: 210rpx;
				height: 120rpx;
				background: #F6F9FF;
				border-radius: 20rpx;
				font-size: 30rpx;
				lighting-color: 34rpx;

				view {
					color: #101010;
				}

				text {
					color: #555555;
				}
			}
			.item:nth-of-type(3n){
				margin: 0 0 20rpx;
			}

			.item.active {
				height: 118rpx;
				background: rgba(5, 185, 174, 0.1);
				border: 2rpx solid var(--normal);

				view {
					color: var(--normal);
				}

				text {
					color: var(--normal);
				}
			}
			.item.disabled{
				view {
					color: #898989;
				}
				
				text {
					color: #898989;
				}
			}
		}

		.page-foot {
			padding: 20rpx 65rpx;
			background-color: #FFF;
		}

		.container {
			padding: 30rpx 40rpx 160rpx !important;
		}
	}
</style>