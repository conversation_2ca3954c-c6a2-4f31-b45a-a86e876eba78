<template>
	<view class="xilu">
		<view class="container">
			<view class="g_order1" v-for="(order,index) in sceneryList" :key="index">
				<view class="flex-box mb25">
					<view class="fs26 col-89">订单号</view>
					<view class="flex-1 fs26 col-5">{{order.order_no}}</view>
					<view class="fs30 col-3">已核销</view>
				</view>
				<view class="flex-box">
					<image class="img" :src="order.order_project.thumb_image" mode="aspectFill"></image>
					<view class="flex-1">
						<view class="m-ellipsis fs36 col-10 mb20">{{order.order_project.project_name}}</view>
						<view class="flex-box col-3 mb35">
							<text class="fs24">¥</text>
							<text class="fs30 flex-1">{{order.order_project.project_price}}</text>
							<view class="fs30 col-89">数量 {{order.total_count}}</view>
						</view>
						<view>
							<text class="fs30 col-89">核销金额 </text>
							<text class="fs30 col-price">¥</text>
							<text class="fs40 col-price">{{order.verifer_money}}</text>
						</view>
					</view>
				</view>
				<view class="flex-box pt30 fs26 col-89">
					<view class="flex-1">核销数: {{order.my_count}}</view>
					<view>核销时间: {{order.verifytime_text}}</view>
				</view>
			</view>
		
			<view class="g-btn3-wrap">
				<view class="g-btn3" @click="fetch">{{sceneryListMore.text}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				sceneryList:[],
				sceneryListMore:{page:1}
			};
		},
		onLoad() {
			this.fetch();
		},
		onReachBottom() {
			this.fetch();
		},
		methods:{
			fetch(){
				this.$util.fetch(this, 'xilutour.scenery_order/verifier_list', {pagesize:10}, 'sceneryListMore', 'sceneryList', 'data', data=>{
				  
				})
			},
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		.container {
			padding: 30rpx;
			background: #F7F9FB;
		}
	}
</style>