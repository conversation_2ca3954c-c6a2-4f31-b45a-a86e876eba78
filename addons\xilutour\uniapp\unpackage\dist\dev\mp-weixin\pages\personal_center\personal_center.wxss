.xilu_user.data-v-3e8d8070 {
  margin: 0 0 46rpx;
}
.xilu_user .num.data-v-3e8d8070 {
  position: relative;
  margin: 0 0 15rpx;
  font-size: 46rpx;
  font-family: <PERSON><PERSON><PERSON>, Poppins;
  font-weight: 800;
  color: #101010;
  line-height: 48rpx;
}
.xilu_user .num.dot.data-v-3e8d8070::after {
  position: absolute;
  right: 0rpx;
  top: 0rpx;
  content: '';
  width: 10rpx;
  height: 10rpx;
  background: #F35F4B;
  border-radius: 50%;
}
.xilu_user .head.data-v-3e8d8070 {
  position: relative;
  width: 208rpx;
  height: 208rpx;
  border: 6rpx solid #FFFFFF;
  border-radius: 50%;
}
.xilu_user .head .img_head.data-v-3e8d8070 {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.xilu_user .head .icon_edit.data-v-3e8d8070 {
  position: absolute;
  right: 20rpx;
  bottom: 0;
  display: block;
  width: 45rpx;
  height: 45rpx;
}
.xilu_menu.data-v-3e8d8070 {
  padding: 30rpx;
  width: 320rpx;
  border-radius: 30rpx;
}
.xilu_menu .title.data-v-3e8d8070 {
  margin: 0 0 24rpx;
  font-size: 34rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 36rpx;
}
.xilu_menu .view.data-v-3e8d8070 {
  width: 78rpx;
  height: 46rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 46rpx;
  text-align: center;
}
.xilu_nav_box.data-v-3e8d8070 {
  margin: 30rpx 0 0;
  padding: 30rpx 0 40rpx;
  background: #FFFFFF;
  box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
  border-radius: 30rpx;
}
.xilu_nav_box .title.data-v-3e8d8070 {
  padding: 0 26rpx;
  font-size: 36rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #101010;
  line-height: 38rpx;
}
.xilu_nav_box .item.data-v-3e8d8070 {
  padding: 0;
  margin: 40rpx 0 0;
  width: 25%;
  font-size: 28rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #555555;
  line-height: 30rpx;
  text-align: center;
  background-color: #fff;
}
.xilu_nav_box .item image.data-v-3e8d8070 {
  margin: 0 auto 20rpx;
  display: block;
  width: 60rpx;
  height: 60rpx;
}
.xilu_nav_box .item.data-v-3e8d8070::after {
  content: none;
}
.xilu .container.data-v-3e8d8070 {
  background: #F7F9FB;
}
.xilu .page-body.data-v-3e8d8070 {
  padding: 40rpx 40rpx 30rpx;
}

