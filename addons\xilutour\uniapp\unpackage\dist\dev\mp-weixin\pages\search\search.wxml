<view class="xilu data-v-4cedc0c6"><view class="container data-v-4cedc0c6"><view class="m-header bg-white data-v-4cedc0c6"><view class="g-custom-nav flex-box plr30 data-v-4cedc0c6" style="{{'padding-top:'+(statusBarHeight+'px')+';'+('height:'+('calc(90rpx + '+statusBarHeight+'px)')+';')}}"><image class="icon_back data-v-4cedc0c6" src="/static/icon/icon_back.png" mode="aspectFit" data-event-opts="{{[['tap',[['navBack',['$event']]]]]}}" bindtap="__e"></image><view class="search_box flex-box data-v-4cedc0c6"><view data-event-opts="{{[['tap',[['bindCityChange']]]]}}" class="addr m-ellipsis data-v-4cedc0c6" bindtap="__e">{{currentCity?currentCity.name:''}}</view><image class="icon_arrow data-v-4cedc0c6" src="/static/icon/icon_arrow.png"></image><view class="line data-v-4cedc0c6"></view><image class="icon_search data-v-4cedc0c6" src="/static/icon/icon_search.png"></image><input class="input flex-1 col-normal data-v-4cedc0c6" confirm-type="search" type="text" placeholder="出发城市/目的地" placeholder-class="cola" data-event-opts="{{[['confirm',[['searchConfirm']]],['focus',[['searchFocus']]],['input',[['__set_model',['$0','q','$event',[]],['query']]]]]}}" value="{{query.q}}" bindconfirm="__e" bindfocus="__e" bindinput="__e"/></view></view></view><view class="pr data-v-4cedc0c6" style="{{'padding-top:'+('calc(90rpx + '+statusBarHeight+'px)')+';'}}"><view class="p40 data-v-4cedc0c6"><block wx:if="{{showSearch}}"><block class="data-v-4cedc0c6"><view class="xilu_title data-v-4cedc0c6">历史搜索</view><view class="flex-box flex-wrap flex-align-start data-v-4cedc0c6"><block wx:for="{{searchHistoryList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['searchClick',['$0'],[[['searchHistoryList','',index]]]]]]]}}" class="xilu_label2 data-v-4cedc0c6" bindtap="__e">{{item}}</view></block></view></block></block><block wx:else><block class="data-v-4cedc0c6"><view class="xilu_top_box data-v-4cedc0c6"><view class="fs30 col-5 data-v-4cedc0c6">共<text class="col-normal data-v-4cedc0c6">{{total}}</text>个内容</view><view class="g_tab flex-box data-v-4cedc0c6"><view data-event-opts="{{[['tap',[['tabClick',[1]]]]]}}" class="{{['item','data-v-4cedc0c6',(tabIdx==1)?'active':'']}}" bindtap="__e">路线</view><view data-event-opts="{{[['tap',[['tabClick',[2]]]]]}}" class="{{['item','data-v-4cedc0c6',(tabIdx==2)?'active':'']}}" bindtap="__e">景点</view></view></view><block wx:if="{{tabIdx==1}}"><view class="g_travel_list data-v-4cedc0c6"><tour-list vue-id="50cad900-1" tourList="{{tourList}}" class="data-v-4cedc0c6" bind:__l="__l"></tour-list><view class="g-btn3-wrap data-v-4cedc0c6"><view data-event-opts="{{[['tap',[['fetchTourList',['$event']]]]]}}" class="g-btn3 data-v-4cedc0c6" bindtap="__e">{{tourListMore.text}}</view></view></view></block><block wx:if="{{tabIdx==2}}"><view class="g_travel_list data-v-4cedc0c6"><scenery-list vue-id="50cad900-2" sceneryList="{{sceneryList}}" class="data-v-4cedc0c6" bind:__l="__l"></scenery-list><view class="g-btn3-wrap data-v-4cedc0c6"><view data-event-opts="{{[['tap',[['fetchSceneryList',['$event']]]]]}}" class="g-btn3 data-v-4cedc0c6" bindtap="__e">{{sceneryListMore.text}}</view></view></view></block></block></block></view></view></view></view>