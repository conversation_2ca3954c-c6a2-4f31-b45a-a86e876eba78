<view class="xilu data-v-72bcdb74"><view class="page-foot data-v-72bcdb74"><view class="g_order_foot1 flex-box data-v-72bcdb74"><navigator class="nav data-v-72bcdb74" url="/pages/index/index" open-type="switchTab" hover-class="none"><image class="icon data-v-72bcdb74" src="/static/icon/icon_btn1.png" mode="aspectFit"></image><view class="data-v-72bcdb74">首页</view></navigator><button class="nav data-v-72bcdb74" open-type="contact" hover-class="none"><image class="icon data-v-72bcdb74" src="/static/icon/icon_btn2.png" mode="aspectFit"></image><view class="data-v-72bcdb74">客服</view></button><view data-event-opts="{{[['tap',[['toggleCollection']]]]}}" class="nav data-v-72bcdb74" bindtap="__e"><image class="icon data-v-72bcdb74" src="{{'/static/icon/icon_btn3'+(tour.is_collection_count==1?'on':'')+'.png'}}" mode="aspectFit"></image><view class="data-v-72bcdb74">{{tour.is_collection_count==1?'已收藏':'收藏'}}</view></view><view data-event-opts="{{[['tap',[['bindEnroll']]]]}}" class="btn1 data-v-72bcdb74" bindtap="__e">我要报名</view></view></view><view class="container data-v-72bcdb74"><view class="m-header data-v-72bcdb74" style="{{'background:'+(setCol?'var(--normal)':'unset')+';'}}"><view class="g-custom-nav flex-box plr30 data-v-72bcdb74" style="{{'padding-top:'+(statusBarHeight+'px')+';'+('height:'+('calc(90rpx + '+statusBarHeight+'px)')+';')}}"><image class="icon_back data-v-72bcdb74" src="/static/icon/icon_back1.png" mode="aspectFit" data-event-opts="{{[['tap',[['navBack',['$event']]]]]}}" bindtap="__e"></image><view class="flex-1 pr35 mr30 fs26 col-f tc data-v-72bcdb74">{{barTitle}}</view></view></view><view class="xilu_swiper data-v-72bcdb74"><swiper class="swiper data-v-72bcdb74" current="{{swiperCurrent}}" circular="{{true}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{tour.images_text}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="data-v-72bcdb74"><view class="{{['nav','data-v-72bcdb74',(swiperCurrent!==index)?'scale':'']}}"><image class="img data-v-72bcdb74" src="{{item}}" mode="aspectFill"></image></view></swiper-item></block></swiper><view class="swiper_dots flex-box flex-center data-v-72bcdb74"><block wx:for="{{tour.images_text}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['dots','data-v-72bcdb74',(swiperCurrent==index)?'active':'']}}"></view></block></view></view><view class="xilu_info_wrap data-v-72bcdb74"><view class="g_travel_list data-v-72bcdb74"><view class="flex-box mb30 data-v-72bcdb74"><block wx:if="{{tour.tour_date}}"><view class="flex-1 col-price data-v-72bcdb74"><text class="fs30 data-v-72bcdb74">¥</text><text class="fs40 data-v-72bcdb74">{{tour.tour_date.salesprice}}</text><text class="fs30 data-v-72bcdb74">起</text></view></block><view class="fs28 col-a data-v-72bcdb74">{{tour.view_count+"人浏览"}}</view></view><block wx:if="{{$root.g0>0}}"><view class="flex-box flex-wrap pb10 data-v-72bcdb74"><block wx:for="{{tour.tags}}" wx:for-item="tag" wx:for-index="index"><view class="label data-v-72bcdb74">{{tag.name}}</view></block></view></block><view class="mb20 data-v-72bcdb74"><text class="g_feng data-v-72bcdb74">{{tour.points}}</text><text class="fs36 col-10 data-v-72bcdb74">{{tour.name}}</text></view><view class="desc data-v-72bcdb74">{{tour.sub_name}}</view></view></view><view class="bg-white data-v-72bcdb74"><view class="xilu_coupon_box data-v-72bcdb74"><view data-event-opts="{{[['tap',[['couponPopOpen',['$event']]]]]}}" class="flex-box ptb25 m-hairline--bottom data-v-72bcdb74" bindtap="__e"><view class="col-price fs30 mr20 data-v-72bcdb74">优惠券</view><view class="coupon_list data-v-72bcdb74"><block wx:for="{{tour.coupons}}" wx:for-item="item" wx:for-index="index"><view class="coupon data-v-72bcdb74"><image src="/static/icon/icon_coupon_bg3.png" mode="aspectFill" class="data-v-72bcdb74"></image><view class="inner data-v-72bcdb74">{{item.name}}</view></view></block></view><image class="g-icon30 data-v-72bcdb74" src="/static/icon/icon_arrow2.png" mode="aspectFit"></image></view><view class="flex-box ptb35 flex-between tc data-v-72bcdb74"><view class="data-v-72bcdb74"><view class="fs30 col-3 mb20 data-v-72bcdb74">{{tour.team_count}}</view><view class="fs24 col-89 data-v-72bcdb74">限制人数</view></view><view class="data-v-72bcdb74"><view class="fs30 col-3 mb20 data-v-72bcdb74">{{tour.age_introduce}}</view><view class="fs24 col-89 data-v-72bcdb74">年龄限制</view></view><view class="data-v-72bcdb74"><view class="fs30 col-3 mb20 data-v-72bcdb74">{{tour.gather_city.name}}</view><view class="fs24 col-89 data-v-72bcdb74">集合地点</view></view><view class="data-v-72bcdb74"><view class="fs30 col-3 mb20 data-v-72bcdb74">{{tour.gather_city.name}}</view><view class="fs24 col-89 data-v-72bcdb74">解散地点</view></view></view></view><view class="fs36 m30 col-10 data-v-72bcdb74">近期出发</view><view class="xilu_regis data-v-72bcdb74"><block wx:for="{{tour.tour_date_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item data-v-72bcdb74"><view class="flex-box flex-between plr25 data-v-72bcdb74"><view class="data-v-72bcdb74"><view class="fs24 col-89 mb20 data-v-72bcdb74">出发</view><view class="fs34 col-10 mb10 data-v-72bcdb74">{{item.appoint_date_text}}</view><view class="fs24 col-5 data-v-72bcdb74">{{item.appoint_date_week}}</view></view><view class="line data-v-72bcdb74"></view><view class="data-v-72bcdb74"><view class="fs24 col-89 mb20 data-v-72bcdb74">结束</view><view class="fs34 col-10 mb10 data-v-72bcdb74">{{item.appoint_end_date_text}}</view><view class="fs24 col-5 data-v-72bcdb74">{{item.appoint_end_date_week}}</view></view></view><view class="col-price plr30 mt20 data-v-72bcdb74"><text class="fs30 data-v-72bcdb74">¥</text><text class="fs40 data-v-72bcdb74">{{item.salesprice}}</text><text class="fs30 data-v-72bcdb74">起</text></view><view data-event-opts="{{[['tap',[['bindEnrollDate',[index]]]]]}}" class="btn data-v-72bcdb74" bindtap="__e">去报名</view></view></block></view><view class="g_tab mtb15 data-v-72bcdb74"><view data-event-opts="{{[['tap',[['tabClick',[1]]]]]}}" class="{{['item','data-v-72bcdb74',(tabIdx==1)?'active':'']}}" bindtap="__e">路线亮点</view><view data-event-opts="{{[['tap',[['tabClick',[2]]]]]}}" class="{{['item','data-v-72bcdb74',(tabIdx==2)?'active':'']}}" bindtap="__e">行程简介</view><view data-event-opts="{{[['tap',[['tabClick',[3]]]]]}}" class="{{['item','data-v-72bcdb74',(tabIdx==3)?'active':'']}}" bindtap="__e">费用说明</view><view data-event-opts="{{[['tap',[['tabClick',[4]]]]]}}" class="{{['item','data-v-72bcdb74',(tabIdx==4)?'active':'']}}" bindtap="__e">行程必看</view><view data-event-opts="{{[['tap',[['tabClick',[5]]]]]}}" class="{{['item','data-v-72bcdb74',(tabIdx==5)?'active':'']}}" bindtap="__e">{{"用户评价("+total+")"}}</view></view><view class="xilu_detail_box data-v-72bcdb74"><block wx:if="{{tabIdx==1}}"><view class="data-v-72bcdb74"><rich-text nodes="{{tour.highlights_content}}" class="data-v-72bcdb74"></rich-text></view></block><block wx:if="{{tabIdx==2}}"><view class="data-v-72bcdb74"><view class="step_wrap data-v-72bcdb74"><rich-text nodes="{{tour.introduction_content}}" class="data-v-72bcdb74"></rich-text></view></view></block><block wx:if="{{tabIdx==3}}"><view class="data-v-72bcdb74"><view class="detail fs30 col-3 data-v-72bcdb74"><rich-text nodes="{{tour.fee_explain}}" class="data-v-72bcdb74"></rich-text></view></view></block><block wx:if="{{tabIdx==4}}"><view class="data-v-72bcdb74"><view class="detail fs30 col-3 data-v-72bcdb74"><rich-text nodes="{{tour.see_content}}" class="data-v-72bcdb74"></rich-text></view></view></block><block wx:if="{{tabIdx==5}}"><view class="data-v-72bcdb74"><block wx:for="{{$root.l0}}" wx:for-item="comment" wx:for-index="index" wx:key="index"><view class="g_comment data-v-72bcdb74"><view class="flex-box data-v-72bcdb74"><image class="head data-v-72bcdb74" src="{{comment.$orig.user.avatar}}" mode="aspectFill"></image><view class="flex-1 data-v-72bcdb74"><view class="fs30 col-5 mb20 data-v-72bcdb74">{{comment.$orig.user.nickname}}</view><view class="fs24 col-89 data-v-72bcdb74">{{comment.$orig.createtime_text}}</view></view><u-rate vue-id="{{'3641d7dc-1-'+index}}" disabled="{{true}}" readonly="{{true}}" value="{{4}}" count="{{4}}" size="{{18}}" gutter="{{0}}" active-icon="/static/icon/icon_star.png" class="data-v-72bcdb74" bind:__l="__l"></u-rate></view><view class="text1 pt25 data-v-72bcdb74">{{comment.$orig.content}}</view><block wx:if="{{comment.g1>0}}"><view class="flex-box flex-wrap data-v-72bcdb74"><block wx:for="{{comment.$orig.images_text}}" wx:for-item="img" wx:for-index="index2"><image class="img1 data-v-72bcdb74" src="{{img}}" mode="aspectFill" data-event-opts="{{[['tap',[['bindPrev',[index,index2]]]]]}}" bindtap="__e"></image></block></view></block></view></block><view class="g-btn3-wrap data-v-72bcdb74"><view data-event-opts="{{[['tap',[['fetch',['$event']]]]]}}" class="g-btn3 data-v-72bcdb74" bindtap="__e">{{commentListMore.text}}</view></view></view></block></view></view><uni-popup vue-id="3641d7dc-2" type="bottom" data-ref="couponPopup" class="data-v-72bcdb74 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="g_coupon_pop data-v-72bcdb74"><view class="fs30 col-10 tc mb30 data-v-72bcdb74">优惠券</view><image class="icon_close data-v-72bcdb74" src="/static/icon/icon_close.png" mode="aspectFit" data-event-opts="{{[['tap',[['couponPopClose',['$event']]]]]}}" bindtap="__e"></image><view class="pop_coupon_wrap data-v-72bcdb74"><block wx:for="{{tour.coupons}}" wx:for-item="coupon" wx:for-index="index" wx:key="index"><view class="pop_coupon data-v-72bcdb74"><image class="bg data-v-72bcdb74" src="/static/icon/icon_coupon_bg1.png" mode="aspectFill"></image><view class="inner flex-box data-v-72bcdb74"><view class="left data-v-72bcdb74"><view class="fwb mb20 data-v-72bcdb74"><text class="fs24 data-v-72bcdb74">¥</text><text class="fs50 data-v-72bcdb74">{{coupon.money}}</text></view><view class="man data-v-72bcdb74">{{"满"+coupon.at_least+"可用"}}</view></view><view class="right flex-1 flex-box data-v-72bcdb74"><view class="flex-1 data-v-72bcdb74"><view class="fs30 mb20 data-v-72bcdb74">{{coupon.name}}</view><view class="fs24 data-v-72bcdb74">{{coupon.use_end_time_text+"到期"}}</view></view><block wx:if="{{coupon.is_receive_count==1}}"><view class="use data-v-72bcdb74">已领取</view></block><block wx:else><view data-event-opts="{{[['tap',[['bindReceive',[index]]]]]}}" class="use data-v-72bcdb74" catchtap="__e">领取</view></block></view></view></view></block></view></view></uni-popup></view></view>