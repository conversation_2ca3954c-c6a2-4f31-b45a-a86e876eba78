{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue?d61a", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue?f982", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue?b63e", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue?93b4", "uni-app:///pages/travel_pay/travel_pay.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue?9b59", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue?d874"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "tour", "coupon_id", "tour_id", "buy_adult_count", "buy_child_count", "tour_date_id", "contact_name", "contact_mobile", "order", "coupon_price", "coupon_list", "pay_price", "total_price", "name", "sub_name", "thumb_image_text", "tour_date", "appoint_date_text", "appoint_date_week", "appoint_end_date_text", "appoint_end_date_week", "id", "originprice", "salesprice", "set_count", "status", "loading", "travelerList", "onLoad", "page", "methods", "preOrder", "url", "success", "fail", "uni", "title", "content", "showCancel", "createOrder", "icon", "nameChn", "rules", "errorMsg", "require", "length", "gt", "payment", "pay_type", "order_id", "platform", "bindTraveler", "events", "chooseSuccess", "res", "bindDel", "couponPopOpen", "couponPopClose", "bindChooseCoupon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2Jv1B;AAAA,eACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAZ;UACAa;UACAC;UACAC;QACA;QACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAvB;QACA;MACA;MACAwB;MACAC;IACA;EAEA;EACAC;IACA;IACA;MACA5B;MACAA;MACAA;MACA6B;MACAA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAAC;QAAAjC;QAAAkC;UACA;UACA;QACA;QAAAC;UACAC;YACAC;YACAC;YACAC;YACAL;cACA;gBACAE;cACA;YACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAI;MAAA;MACA;MACA;MACA;QACAJ;UACAC;UACAI;QACA;QACA;MACA;MACA;MACAb;QAAA;MAAA;MACAnB;MACA,YACA;QAAAK;QAAA4B;QAAAC;QAAAC;UAAAC;QAAA;MAAA,GACA;QAAA/B;QAAA4B;QAAAC;QAAAC;UAAAC;UAAAC;QAAA;MAAA,GACA;QAAAhC;QAAA4B;QAAAC;QAAAC;UAAAG;QAAA;MAAA,GACA;QAAAjC;QAAA4B;QAAAC;QAAAC;UAAAG;QAAA;MAAA,EACA;MACA;MACA;QACAX;UACAC;UACAI;QACA;QACA;MACA;MACA;MACA;QAAAR;QAAAjC;QAAA2B;QAAAO;UACA;UACA;UACA;QACA;QACAC;UACA;UACAC;YACAC;YACAC;YACAC;YACAL;cACA;gBACA;kBACAE;oBACAH;kBACA;gBACA;cACA;YACA;UACA;UACA;QACA;MACA;IACA;IACAe;MAAA;MAEA;QAAAf;QAAAjC;UAAAiD;UAAAC;UAAAC;QAAA;QAAAjB;UACA;UACA;YACAE;cACAH;YACA;UACA;QACA;MAAA;IAEA;IACA;IACAmB;MAAA;MACA;MACAhB;QACAH;QACAoB;UACAC;YACA;YACA;UACA;QACA;QACApB;UACAqB;QACA;MACA;IACA;IACA;IACAC;MACA;MACA5B;MACA;IACA;IACA;IACA6B;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzUA;AAAA;AAAA;AAAA;AAAshD,CAAgB,u5CAAG,EAAC,C;;;;;;;;;;;ACA1iD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/travel_pay/travel_pay.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/travel_pay/travel_pay.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./travel_pay.vue?vue&type=template&id=59cce134&scoped=true&\"\nvar renderjs\nimport script from \"./travel_pay.vue?vue&type=script&lang=js&\"\nexport * from \"./travel_pay.vue?vue&type=script&lang=js&\"\nimport style0 from \"./travel_pay.vue?vue&type=style&index=0&id=59cce134&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"59cce134\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/travel_pay/travel_pay.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_pay.vue?vue&type=template&id=59cce134&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.order.coupon_list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_pay.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_pay.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"page-foot ptb15\">\r\n\t\t\t<view class=\"g_order_foot1 flex-box\">\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"col-price\">\r\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs40\">{{order.pay_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"fs30 col-89 pl25\">共计</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"btn1\" :loading=\"loading\" :disabled=\"loading\" @click=\"createOrder()\">立即支付</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"xilu_travel flex-box mb30\">\r\n\t\t\t\t<image :src=\"order.tour.thumb_image_text\" mode=\"aspectFill\" class=\"img mr30\"></image>\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"fs36 col-10 mb20\">{{order.tour.name}}</view>\r\n\t\t\t\t\t<view class=\"fs30 col-5 mb20\">{{order.tour.sub_name}}｜{{order.tour.team_count}}人旅行团</view>\r\n\t\t\t\t\t<view class=\"col-price\">\r\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs40\">{{order.tour_date.salesprice}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_travel_time flex-box\">\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"fs24 col-89 mb15\">出发日期</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text class=\"fs36 col-10 mr15\">{{order.tour_date.appoint_date_text}}</text>\r\n\t\t\t\t\t\t<text class=\"fs30 col-5\">{{order.tour_date.appoint_date_week}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"fs24 col-89 mb15\">结束日期</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text class=\"fs36 col-10 mr15\">{{order.tour_date.appoint_end_date_text}}</text>\r\n\t\t\t\t\t\t<text class=\"fs30 col-5\">{{order.tour_date.appoint_end_date_week}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fs30 col-normal\">{{order.tour.series_days}}天</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_title mt50 mb30\">联系人信息</view>\r\n\r\n\t\t\t<view class=\"g_input_box flex-box mb30\">\r\n\t\t\t\t<view class=\"fs30 col-5\">姓名</view>\r\n\t\t\t\t<input class=\"flex-1 tr fs30 col-10\" v-model=\"tour.contact_name\" type=\"text\" placeholder=\"请输入姓名\" placeholder-class=\"col-10\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"g_input_box flex-box\">\r\n\t\t\t\t<view class=\"fs30 col-5\">手机号码</view>\r\n\t\t\t\t<input class=\"flex-1 tr fs30 col-10\" v-model=\"tour.contact_mobile\" maxlength=\"11\" type=\"number\" placeholder=\"请输入手机号码\" placeholder-class=\"col-10\" />\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"flex-box mt50\">\r\n\t\t\t\t<view class=\"flex-1 xilu_title\">出行人信息</view>\r\n\t\t\t\t<view class=\"fs24 col-5 m-arrow-right\" @click=\"bindTraveler()\">添加{{tour.buy_adult_count}}个成人{{tour.buy_child_count>0?'和'+tour.buy_child_count+'个儿童信息':''}}</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view>\r\n\t\t\t\t<view class=\"xilu_traveler flex-box\" v-for=\"(item,index) in travelerList\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"flex-box flex-1\">\r\n\t\t\t\t\t\t<view :class=\"item.adult_type == 1?'identity1':'identity2'\">{{item.adult_type_text}}</view>\r\n\t\t\t\t\t\t<view class=\"fs30 col-10 mlr20\">{{item.username}}</view>\r\n\t\t\t\t\t\t<image v-if=\"item.gender==1\" src=\"../../static/icon/icon_gender1.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t<image v-else src=\"../../static/icon/icon_gender2.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"btn_del\" @click=\"bindDel(index)\">\r\n\t\t\t\t\t\t<image src=\"../../static/icon/icon_del.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_title mt50 mb30\">订单信息</view>\r\n\r\n\t\t\t<view class=\"g_order_info\">\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">支付方式</view>\r\n\t\t\t\t\t<image src=\"/static/icon/icon_wx.png\" mode=\"aspectFit\" class=\"g-icon30 mr15\"></image>\r\n\t\t\t\t\t<view class=\"fs30 col-10\">微信支付</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">商品金额</view>\r\n\t\t\t\t\t<view class=\"col-10\">\r\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs40\">{{order.total_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"flex-box mb50\" v-if=\"order.coupon_list.length>0\" @click=\"couponPopOpen\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">优惠劵</view>\r\n\t\t\t\t\t<view class=\"col-10 m-arrow-right\">\r\n\t\t\t\t\t\t<text class=\"fs24\">-¥</text>\r\n\t\t\t\t\t\t<text class=\"fs34\">{{order.coupon_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"flex-box flex-end\">\r\n\t\t\t\t\t<view class=\"fs30 col-89 mr20\">共计</view>\r\n\t\t\t\t\t<view class=\"col-price\">\r\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs40\">{{order.pay_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t<uni-popup ref=\"couponPopup\" type=\"bottom\">\r\n\t\t\t\t<view class=\"g_coupon_pop\">\r\n\t\t\t\t\t<view class=\"fs30 col-10 tc mb30\">优惠券</view>\r\n\t\t\t\t\t<image src=\"/static/icon/icon_close.png\" mode=\"aspectFit\" class=\"icon_close\" @click=\"couponPopClose\"></image>\r\n\t\t\t\r\n\t\t\t\t\t<view class=\"pop_coupon_wrap\">\r\n\t\t\t\t\t\t<view class=\"pop_coupon\" v-for=\"(coupon,index) in order.coupon_list\" :key=\"index\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_coupon_bg1.png\" mode=\"aspectFill\" class=\"bg\"></image>\r\n\t\t\t\t\t\t\t<view class=\"inner flex-box\">\r\n\t\t\t\t\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"fwb mb20\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"fs24\">¥</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"fs50\">{{coupon.money}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"man\">满{{coupon.at_least}}可用</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"right flex-1 flex-box\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"fs30 mb20\">{{coupon.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"fs24\">{{coupon.use_end_time_text}}到期</view>\r\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"use active\" v-if=\"coupon.checked\">\n\t\t\t\t\t\t\t\t\t\t<image src=\"/static/icon/icon_coupon_check.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"use\" @click=\"bindChooseCoupon(index)\" v-else>选择</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\t\t<view class=\"g-btn1\" style=\"width: 670rpx;margin: 30rpx auto 0;\">确定</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\n\tvar validate = require(\"../../xilu/validate.js\");\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttour:{\n\t\t\t\t\tcoupon_id:0,\n\t\t\t\t\ttour_id:0,\n\t\t\t\t\tbuy_adult_count:0,\n\t\t\t\t\tbuy_child_count:0,\n\t\t\t\t\ttour_date_id:0,\n\t\t\t\t\tcontact_name:'',\n\t\t\t\t\tcontact_mobile:''\n\t\t\t\t},\n\t\t\t\torder:{\n\t\t\t\t\tcoupon_price:0,\n\t\t\t\t\tcoupon_list:[],\n\t\t\t\t\tpay_price:0,\n\t\t\t\t\ttotal_price:0,\n\t\t\t\t\ttour:{\n\t\t\t\t\t\tname:'',\n\t\t\t\t\t\tsub_name:'',\n\t\t\t\t\t\tthumb_image_text:''\n\t\t\t\t\t},\n\t\t\t\t\ttour_date:{\n\t\t\t\t\t\tappoint_date_text: \"\",\n\t\t\t\t\t\tappoint_date_week: \"\",\n\t\t\t\t\t\tappoint_end_date_text: \"\",\n\t\t\t\t\t\tappoint_end_date_week: \"\",\n\t\t\t\t\t\tid: 0,\n\t\t\t\t\t\toriginprice: 0,\n\t\t\t\t\t\tsalesprice: 0,\n\t\t\t\t\t\tset_count: 0,\n\t\t\t\t\t\tstatus: \"normal\",\n\t\t\t\t\t\ttour_id: 0\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tloading: false,\n\t\t\t\ttravelerList:[],\r\n\t\t\t};\r\n\r\n\t\t},\n\t\tonLoad() {\n\t\t\tlet page = this;\n\t\t\tthis.getOpenerEventChannel().on(\"tourTransfor\",function(tour){\n\t\t\t\ttour.coupon_id = 0;\n\t\t\t\ttour.contact_name = '';\n\t\t\t\ttour.contact_mobile = '';\n\t\t\t\tpage.tour = tour;\n\t\t\t\tpage.preOrder();\n\t\t\t})\n\t\t},\n\t\tmethods:{\n\t\t\tpreOrder() {\n\t\t\t\tthis.$core.post({url: 'xilutour.tour_order/pre_order',data: this.tour,success: ret => {\n\t\t\t\t\t\tthis.order = ret.data;\n\t\t\t\t\t\tif(ret.data.coupon) this.tour.coupon_id = ret.data.coupon.id;\n\t\t\t\t\t},fail: err => {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\tcontent: err.msg,\n\t\t\t\t\t\t\tshowCancel:false,\n\t\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t\t\tif(res.confirm){\n\t\t\t\t\t\t\t\t\tuni.navigateBack({});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t//下单\n\t\t\tcreateOrder(){\n\t\t\t\tlet order = this.tour;\n\t\t\t\tlet travelerList = this.travelerList;\n\t\t\t\tif(travelerList.length<=0 || travelerList.length != (order.buy_adult_count+order.buy_child_count)){\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t    title: \"请选择出行人信息\",\n\t\t\t\t\t    icon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tlet travelerIds = [];\n\t\t\t\ttravelerList.forEach(item=>travelerIds.push(item.id));\n\t\t\t\torder.traveler_ids = travelerIds.join(',');\n\t\t\t\tvar rule = [\n\t\t\t\t\t{name: 'contact_name',nameChn: '姓名',rules: ['require'],errorMsg: {require: '请填写姓名'}},\n\t\t\t\t\t{name: 'contact_mobile',nameChn: '手机号',rules: ['require','length:11'],errorMsg: {require: '请填写手机号',length:\"手机号错误\"}},\n\t\t\t\t\t{name: 'tour_id',nameChn: '线路',rules: ['gt:0'],errorMsg: {gt: '线路错误'}},\n\t\t\t\t\t{name: 'tour_date_id',nameChn: '线路日期',rules: ['gt:0'],errorMsg: {gt: '线路日期错误'}},\n\t\t\t\t];\n\t\t\t\t// 是否全部通过，返回Boolean\n\t\t\t\tif (!validate.check(order, rule)) {\n\t\t\t\t    uni.showToast({\n\t\t\t\t        title: validate.getError()[0],\n\t\t\t\t        icon: 'none'\n\t\t\t\t    });\n\t\t\t\t    return;\n\t\t\t\t}\n\t\t\t\tthis.loading = true;\n\t\t\t\tthis.$core.post({url: 'xilutour.tour_order/create_order',data: order,loading: true,success: ret => {\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t//下单成功，前往收银台\n\t\t\t\t\tthis.payment(ret.data);\n\t\t\t\t\t},\n\t\t\t\t\tfail: err => {\n\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle:'提示',\n\t\t\t\t\t\t\tcontent: err.msg,\n\t\t\t\t\t\t\tshowCancel: err.code==3?true:false,\n\t\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t\t\tif(res.confirm){\n\t\t\t\t\t\t\t\t\tif(err.code==3){\n\t\t\t\t\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/travel_order/travel_order'\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tpayment(order){\n\t\t\t\t//#ifdef MP-WEIXIN\n\t\t\t\tthis.$core.post({url:'xilutour.pay/pay',data:{pay_type:1,order_id:order.id,platform:'wxmini'},success:(ret)=>{\n\t\t\t\t\tlet wxconfig =  ret.data;\n\t\t\t\t\tthis.$core.payment(wxconfig,function(){\n\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\turl: '/pages/travel_order/travel_order'\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t}});\n\t\t\t\t//#endif\n\t\t\t},\n\t\t\t//添加出行人\n\t\t\tbindTraveler(){\n\t\t\t\tlet tour = this.tour;\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/choose_traveler/choose_traveler',\n\t\t\t\t\tevents:{\n\t\t\t\t\t\tchooseSuccess: data=>{\n\t\t\t\t\t\t\tthis.travelerList = data;\n\t\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\tres.eventChannel.emit(\"travelTransfor\",tour)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t//删除\n\t\t\tbindDel(index){\n\t\t\t\tlet travelerList = this.travelerList;\n\t\t\t\ttravelerList.splice(index,1);\n\t\t\t\tthis.travelerList = travelerList;\n\t\t\t},\n\t\t\t// 打开优惠券弹窗\n\t\t\tcouponPopOpen() {\n\t\t\t\tthis.$refs.couponPopup.open();\n\t\t\t},\n\t\t\t// 关闭优惠券弹窗\n\t\t\tcouponPopClose() {\n\t\t\t\tthis.$refs.couponPopup.close();\n\t\t\t},\n\t\t\t//选择优惠券\n\t\t\tbindChooseCoupon(index){\n\t\t\t\tthis.tour.coupon_id = this.order.coupon_list[index].id;\n\t\t\t\tthis.preOrder();\n\t\t\t\tthis.couponPopClose();\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.xilu {\r\n\t\t&_travel {\r\n\t\t\t.img {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 180rpx;\r\n\t\t\t\theight: 180rpx;\r\n\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_travel_time {\r\n\t\t\tpadding: 0 40rpx 0 30rpx;\r\n\t\t\theight: 120rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t.line {\r\n\t\t\t\tmargin: 0 60rpx;\r\n\t\t\t\twidth: 25rpx;\r\n\t\t\t\theight: 2rpx;\r\n\t\t\t\tbackground-color: var(--normal);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_title {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #101010;\r\n\t\t\tline-height: 40rpx;\r\n\t\t}\r\n\r\n\t\t&_traveler {\r\n\t\t\tmargin: 30rpx 0 0;\r\n\t\t\tpadding: 0 0 0 30rpx;\r\n\t\t\twidth: 670rpx;\r\n\t\t\theight: 110rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t\tborder-radius: 30rpx;\r\n\r\n\t\t\t.identity1 {\r\n\t\t\t\twidth: 60rpx;\r\n\t\t\t\theight: 36rpx;\r\n\t\t\t\tbackground: #FFAB29;\r\n\t\t\t\tborder-radius: 5rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tline-height: 36rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\r\n\t\t\t.identity2 {\r\n\t\t\t\twidth: 60rpx;\r\n\t\t\t\theight: 36rpx;\r\n\t\t\t\tbackground: var(--normal);\r\n\t\t\t\tborder-radius: 5rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tline-height: 36rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\r\n\t\t\t.btn_del {\r\n\t\t\t\tpadding: 40rpx 0 0 20rpx;\r\n\t\t\t\twidth: 70rpx;\r\n\t\t\t\theight: 110rpx;\r\n\t\t\t\tbackground: var(--normal);\r\n\t\t\t\tborder-radius: 0 30rpx 30rpx 0;\r\n\t\t\t}\r\n\r\n\t\t\timage {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 30rpx;\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.container {\r\n\t\t\tpadding: 30rpx 40rpx 200rpx !important;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_pay.vue?vue&type=style&index=0&id=59cce134&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_pay.vue?vue&type=style&index=0&id=59cce134&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341215\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}