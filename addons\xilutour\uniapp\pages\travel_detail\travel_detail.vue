<template>
	<view class="xilu">
		<view class="page-foot">
			<view class="g_order_foot1 flex-box">
				<navigator class="nav" url="/pages/index/index" open-type="switchTab" hover-class="none">
					<image src="/static/icon/icon_btn1.png" mode="aspectFit" class="icon"></image>
					<view>首页</view>
				</navigator>
				<button class="nav" open-type="contact" hover-class="none">
					<image src="/static/icon/icon_btn2.png" mode="aspectFit" class="icon"></image>
					<view>客服</view>
				</button>
				<view class="nav" @click="toggleCollection()">
					<image  :src="'/static/icon/icon_btn3'+(tour.is_collection_count == 1 ? 'on' : '')+'.png'" mode="aspectFit" class="icon"></image>
					<!-- <image v-else src="/static/icon/icon_btn3.png" mode="aspectFit" class="icon"></image> -->
					
					<view>{{tour.is_collection_count == 1?'已收藏':'收藏'}}</view>
				</view>
				<view class="btn1" @click="bindEnroll()">我要报名</view>
			</view>
		</view>

		<view class="container">
			<view class="m-header" :style="{ background: setCol ? 'var(--normal)' : 'unset'}">
				<view class="g-custom-nav flex-box plr30"
					:style="{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }">
					<image @click="navBack" src="/static/icon/icon_back1.png" mode="aspectFit" class="icon_back"></image>
					<view class="flex-1 pr35 mr30 fs26 col-f tc">{{barTitle}}</view>
				</view>
			</view>

			<view class="xilu_swiper">
				<swiper class="swiper" :current="swiperCurrent" circular @change="swiperChange">
					<swiper-item v-for="(item,index) in tour.images_text" :key="index">
						<view class="nav" :class="{'scale': swiperCurrent !==index}">
							<image :src="item" mode="aspectFill" class="img"></image>
						</view>
					</swiper-item>
				</swiper>
				<view class="swiper_dots flex-box flex-center">
					<view class="dots" v-for="(item,index) in tour.images_text" :key="index"
						:class="{'active': swiperCurrent == index}">
					</view>
				</view>
			</view>

			<view class="xilu_info_wrap">
				<view class="g_travel_list">
					<view class="flex-box mb30">
						<view class="flex-1 col-price" v-if="tour.tour_date">
							<text class="fs30">¥</text>
							<text class="fs40">{{tour.tour_date.salesprice}}</text>
							<text class="fs30">起</text>
						</view>
						<view class="fs28 col-a">{{tour.view_count}}人浏览</view>
					</view>
					<view class="flex-box flex-wrap pb10" v-if="tour.tags.length>0">
						<view class="label" v-for="(tag,index) in tour.tags">{{tag.name}}</view>
					</view>
					<view class="mb20">
						<text class="g_feng">{{tour.points}}</text>
						<text class="fs36 col-10">{{tour.name}}</text>
					</view>
					<view class="desc">{{tour.sub_name}}</view>
				</view>
			</view>
			
			<view class="bg-white">
			
			<view class="xilu_coupon_box">
				<!-- v-if="tour.coupons.length>0"  @click="moreCoupon()"-->
				<view class="flex-box ptb25 m-hairline--bottom" @click="couponPopOpen">
					<view class="col-price fs30 mr20">优惠券</view>
					<view class="coupon_list">
						<view class="coupon" v-for="(item,index) in tour.coupons">
							<image src="/static/icon/icon_coupon_bg3.png" mode="aspectFill"></image>
							<view class="inner">{{item.name}}</view>
						</view>
					</view>
					<image src="/static/icon/icon_arrow2.png" mode="aspectFit" class="g-icon30"></image>
				</view>
				<view class="flex-box ptb35 flex-between tc">
					<view>
						<view class="fs30 col-3 mb20">{{tour.team_count}}</view>
						<view class="fs24 col-89">限制人数</view>
					</view>
					<view>
						<view class="fs30 col-3 mb20">{{tour.age_introduce}}</view>
						<view class="fs24 col-89">年龄限制</view>
					</view>
					<view>
						<view class="fs30 col-3 mb20">{{tour.gather_city.name}}</view>
						<view class="fs24 col-89">集合地点</view>
					</view>
					<view>
						<view class="fs30 col-3 mb20">{{tour.gather_city.name}}</view>
						<view class="fs24 col-89">解散地点</view>
					</view>
				</view>
			</view>

			<view class="fs36 m30 col-10">近期出发</view>

			<view class="xilu_regis">
				<view class="item" v-for="(item,index) in tour.tour_date_list" :key="index">
					<view class="flex-box flex-between plr25">
						<view>
							<view class="fs24 col-89 mb20">出发</view>
							<view class="fs34 col-10 mb10">{{item.appoint_date_text}}</view>
							<view class="fs24 col-5">{{item.appoint_date_week}}</view>
						</view>
						<view class="line"></view>
						<view>
							<view class="fs24 col-89 mb20">结束</view>
							<view class="fs34 col-10 mb10">{{item.appoint_end_date_text}}</view>
							<view class="fs24 col-5">{{item.appoint_end_date_week}}</view>
						</view>
					</view>
					<view class="col-price plr30 mt20">
						<text class="fs30">¥</text>
						<text class="fs40">{{item.salesprice}}</text>
						<text class="fs30">起</text>
					</view>
					<view class="btn" @click="bindEnrollDate(index)">去报名</view>
				</view>
			</view>

			<view class="g_tab mtb15">
				<view class="item" :class="{'active': tabIdx == 1}" @click="tabClick(1)">路线亮点</view>
				<view class="item" :class="{'active': tabIdx == 2}" @click="tabClick(2)">行程简介</view>
				<view class="item" :class="{'active': tabIdx == 3}" @click="tabClick(3)">费用说明</view>
				<view class="item" :class="{'active': tabIdx == 4}" @click="tabClick(4)">行程必看</view>
				<view class="item" :class="{'active': tabIdx == 5}" @click="tabClick(5)">用户评价({{total}})</view>
			</view>

			<view class="xilu_detail_box">
				<view v-if="tabIdx == 1">
					<!-- <image class="img_detail" src="../../static/logo.png" mode="widthFix"></image> -->
					<rich-text :nodes="tour.highlights_content"></rich-text>
				</view>

				<view v-if="tabIdx == 2">
					<view class="step_wrap">
						<rich-text :nodes="tour.introduction_content"></rich-text>
					</view>
				</view>

				<view v-if="tabIdx == 3">
					<view class="detail fs30 col-3">
						<rich-text :nodes="tour.fee_explain"></rich-text>
					</view>
				</view>

				<view v-if="tabIdx == 4">
					<view class="detail fs30 col-3">
						<rich-text :nodes="tour.see_content"></rich-text>
					</view>
				</view>

				<view v-if="tabIdx == 5">
					<view class="g_comment" v-for="(comment,index) in commentList" :key="index">
						<view class="flex-box">
							<image :src="comment.user.avatar" mode="aspectFill" class="head"></image>
							<view class="flex-1">
								<view class="fs30 col-5 mb20">{{comment.user.nickname}}</view>
								<view class="fs24 col-89">{{comment.createtime_text}}</view>
							</view>
							<u-rate :disabled="true" :readonly="true" :value="4" :count="4" :size="18" :gutter="0" active-icon="/static/icon/icon_star.png"></u-rate>
						</view>
						<view class="text1 pt25">{{comment.content}}</view>
						<view class="flex-box flex-wrap"  v-if="comment.images_text.length>0">
							<image @click="bindPrev(index,index2)" v-for="(img,index2) in comment.images_text" :src="img" mode="aspectFill" class="img1"></image>
						</view>
					</view>
					<view class="g-btn3-wrap">
						<view class="g-btn3" @click="fetch">{{commentListMore.text}}</view>
					</view>

				</view>

			</view>
			
			</view>
			<uni-popup ref="couponPopup" type="bottom">
				<view class="g_coupon_pop">
					<view class="fs30 col-10 tc mb30">优惠券</view>
					<image src="/static/icon/icon_close.png" mode="aspectFit" class="icon_close" @click="couponPopClose"></image>
			
					<view class="pop_coupon_wrap">
						<view class="pop_coupon" v-for="(coupon,index) in tour.coupons" :key="index">
							<image src="/static/icon/icon_coupon_bg1.png" mode="aspectFill" class="bg"></image>
							<view class="inner flex-box">
								<view class="left">
									<view class="fwb mb20">
										<text class="fs24">¥</text>
										<text class="fs50">{{coupon.money}}</text>
									</view>
									<view class="man">满{{coupon.at_least}}可用</view>
								</view>
								<view class="right flex-1 flex-box">
									<view class="flex-1">
										<view class="fs30 mb20">{{coupon.name}}</view>
										<view class="fs24">{{coupon.use_end_time_text}}到期</view>
									</view>
									<view class="use" v-if="coupon.is_receive_count == 1">已领取</view>
									<view class="use" @click.stop="bindReceive(index)" v-else>领取</view>
								</view>
							</view>
						</view>
			
					</view>
			
				</view>
			</uni-popup>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				barTitle: '线路详情',
				statusBarHeight: 20,
				swiperCurrent: 0,
				tabIdx: 1,
				tourId:0,
				tour:{
					work_time: '',
					address: '',
					name: '',
					worktime: '',
					view_count: 0,
					images_text: [],
					tags: [],
					gather_city: {
						name: ''
					},
					coupons: []
				},
				
				total:0,
				commentList:[],
				commentListMore: {page:1},
				setCol:false
			};
		},
		onLoad(options) {
			this.statusBarHeight = getApp().globalData.statusBarHeight;
			this.tourId = options.id || 0;
			this.fetchDetail();
			this.fetch();
		},
		onReachBottom() {
			if(this.tabIdx == 5){
				this.fetch();
			}
		},
		onShareAppMessage(e) {
			let userinfo = this.$core.getUserinfo();
			let path = '/pages/travel_detail/travel_detail?id='+this.tourId;
			if(userinfo){
				path += '&pid='+userinfo.pid
			}
			return {
				title: this.tour.name,
				path: path,
				//imageUrl: this.tour.thumb_image_text
			}
		},
		onShareTimeline() {
			let userinfo = this.$core.getUserinfo();
			let query = "id=" + this.tourId
			if(userinfo){
				query += '&pid='+userinfo.pid
			}
			return {
				title: this.tour.name,
				query: query
			}
		},
		onPageScroll(e) {
			if(e.scrollTop > 350){
				this.setCol = true
			}else{
				this.setCol = false
			}
		},
		methods: {
			swiperChange(e) {
				this.swiperCurrent = e.detail.current
			},
			tabClick(i) {
				this.tabIdx = i;
			},
			navBack() {
				uni.navigateBack()
			},
			fetchDetail(){
				this.$core.post({url: 'xilutour.tour/detail',data: {tour_id: this.tourId},loading: false,success: ret => {
						ret.data.highlights_content = this.$core.richTextnew(ret.data.highlights_content);
						ret.data.introduction_content = this.$core.richTextnew(ret.data.introduction_content);
						ret.data.fee_explain = this.$core.richTextnew(ret.data.fee_explain);
						ret.data.see_content = this.$core.richTextnew(ret.data.see_content);
						this.tour = ret.data;
						this.barTitle = ret.data.name;
					},fail: err => {
						console.log(err);
						uni.showModal({
							title:'提示',
							content: err.msg,
							showCancel:false,
							complete() {
								uni.navigateBack()
							}
						})
						return false;
					}
				});
			},
			fetch(){
				let query = {tour_id: this.tourId};
				query.pagesize = 10;
				this.$util.fetch(this, 'xilutour.tour_comment/lists', query, 'commentListMore', 'commentList', 'data', data=>{
				  this.total = data.total;
				})
			},
			//更多优惠券
			moreCoupon(){
				uni.navigateTo({
					url: '/pages/coupon/coupon'
				})
			},
			//收藏
			toggleCollection() {
				if(!this.$core.getUserinfo(true)){
					return;
				}
			    this.$core.post({
			        url: 'xilutour.tour/toggle_collection',
			        data: {
			            tour_id: this.tour.id
			        },
			        success: ret => {
			            this.tour.is_collection_count = ret.data.is_collection_count;
			        },
			        fail: err => {}
			    });
			},
			//领取优惠券
			bindReceive(index){
				let coupons = this.tour.coupons;
				let coupon = coupons[index];
				if(!this.$core.getUserinfo(true)){
					return;
				}
				this.$core.post({
				    url: 'xilutour.coupon/receive',
				    data: {
				        coupon_id: coupon.id
				    },
				    success: ret => {
				        uni.showToast({
				        	title:'领取成功',
				        })
						coupons[index].is_receive_count = 1;
						this.tour.coupons = coupons;
				    },
				    fail: err => {
						uni.showModal({
							title: '提示',
							content: err.msg,
						})
						return false;
					}
				});
			},
			
			//报名
			bindEnroll(){
				uni.navigateTo({
					url: '/pages/select_specification/select_specification?tour_id='+this.tour.id
				})
			},
			//
			bindEnrollDate(index){
				let dateList = this.tour.tour_date_list;
				let tourDateId = dateList[index].id;
				uni.navigateTo({
					url: '/pages/select_specification/select_specification?tour_id='+this.tour.id+'&tour_date_id='+ tourDateId
				})
			},
			// 打开优惠券弹窗
			couponPopOpen() {
				this.$refs.couponPopup.open();
			},
			// 关闭优惠券弹窗
			couponPopClose() {
				this.$refs.couponPopup.close();
			},
			bindPrev(index,index2){
				uni.previewImage({
					urls: this.commentList[index].images_text,
					current: index2
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	
	.container{
		background: #F7F9FB;
	}
	.g_order_foot1 {
		margin: 0 auto 40rpx;
	}

	.xilu {
		&_detail_box {
			padding: 30rpx;
			background: #F7F9FB;

			.img_detail {
				margin: 0 0 20rpx;
				display: block;
				width: 100%;
			}

			.step_wrap {
				.step {
					position: relative;
					margin: 0 0 30rpx 50rpx;
					padding: 30rpx 30rpx 40rpx;
					width: 640rpx;
					background: #FFFFFF;
					box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
					border-radius: 30rpx;

					.img_step {
						display: block;
						max-width: 100%;
						margin: 30rpx 0 0;
					}
				}

				.step::before {
					position: absolute;
					top: 0;
					left: -40rpx;
					content: '';
					width: 2rpx;
					height: calc(100% + 30rpx);
					background-color: rgba(255, 171, 41, 0.2);
				}

				.step::after {
					position: absolute;
					top: 38rpx;
					left: -50rpx;
					content: '';
					width: 22rpx;
					height: 22rpx;
					background: #FFAB29;
					border-radius: 50%;
				}
			}

			.detail {
				padding: 30rpx;
				background: #FFFFFF;
				box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
				border-radius: 30rpx;
				line-height: 44rpx;
			}

		}

		&_regis {
			padding: 0 40rpx;
			overflow-x: scroll;
			overflow-y: hidden;
			white-space: nowrap;

			.item {
				position: relative;
				margin: 0 30rpx 0 0;
				padding: 20rpx 0 0;
				display: inline-block;
				width: 270rpx;
				height: 312rpx;
				background: #FFFFFF;
				border-radius: 25rpx;
				border: 1px solid #EEEEEE;

				.line {
					width: 12rpx;
					height: 2rpx;
					background-color: var(--normal);
				}

				.btn {
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					width: 270rpx;
					height: 60rpx;
					background-color: var(--normal);
					border-radius: 2rpx 2rpx 25rpx 25rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 60rpx;
					text-align: center;
				}
			}
		}

		&_coupon_box {
			margin: 0 30rpx 50rpx;
			padding: 0 30rpx;
			width: 690rpx;
			background: #F7F9FB;
			border-radius: 20rpx;

			.coupon_list {
				flex: 1;
				overflow-x: scroll;
				overflow-y: hidden;
				white-space: nowrap;
				height: 56rpx;
				overflow-y: hidden;

				.coupon {
					position: relative;
					margin-right: 20rpx;
					display: inline-block;

					image {
						display: block;
						width: 180rpx;
						height: 56rpx;
					}

					.inner {
						position: absolute;
						top: 0;
						right: 0;
						left: 0;
						bottom: 0;
						font-size: 26rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #D91B00;
						line-height: 56rpx;
						text-align: center;
					}
				}

			}
		}


		&_info_wrap {
			position: relative;
			margin-top: -50rpx;
			padding: 40rpx;
			background: #FFFFFF;
			border-radius: 50rpx 50rpx 0 0;

			.desc {
				line-height: 42rpx;
			}

			.label {
				margin: 0 20rpx 20rpx 0;
				padding: 0 10rpx;
				height: 48rpx;
				background: rgba(255, 171, 41, 0.1);
				border-radius: 8rpx;
				font-size: 28rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #EB9003;
				line-height: 48rpx;
			}
		}

		&_swiper {
			position: relative;

			.swiper {
				width: 750rpx;
				height: 860rpx;

				.nav {
					position: relative;
					width: 750rpx;
					height: 860rpx;
				}

				.img {
					display: block;
					width: 750rpx;
					height: 860rpx;
				}
			}

			.swiper_dots {
				position: absolute;
				bottom: 80rpx;
				left: 0;
				right: 0;

				.dots {
					margin: 0 4rpx;
					width: 14rpx;
					height: 4rpx;
					background: #D8D8D8;
				}

				.dots.active {
					background: var(--normal);
				}
			}
		}

	}

	.page-foot~.container {
		padding-bottom: 180rpx;
	}

</style>