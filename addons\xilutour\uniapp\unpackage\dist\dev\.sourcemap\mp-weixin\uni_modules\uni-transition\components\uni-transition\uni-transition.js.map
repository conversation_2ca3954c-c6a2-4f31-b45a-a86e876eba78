{"version": 3, "sources": ["webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-transition/components/uni-transition/uni-transition.vue?8e9d", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-transition/components/uni-transition/uni-transition.vue?a847", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-transition/components/uni-transition/uni-transition.vue?db69", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-transition/components/uni-transition/uni-transition.vue?0fc3", "uni-app:///uni_modules/uni-transition/components/uni-transition/uni-transition.vue"], "names": ["name", "emits", "props", "show", "type", "default", "modeClass", "duration", "styles", "customClass", "data", "isShow", "transform", "opacity", "animationData", "durationTime", "config", "watch", "handler", "immediate", "computed", "stylesObject", "transformStyles", "created", "timingFunction", "transform<PERSON><PERSON>in", "delay", "methods", "init", "onClick", "detail", "step", "console", "run", "open", "clearTimeout", "close", "styleInit", "buildStyle", "tranfromInit", "aniNum", "buildTranfrom", "animationType", "fade", "animationMode", "toLine"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;;;AAG7D;AACqN;AACrN,gBAAgB,gNAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAq2B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACKz3B;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,gBAgBA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;QACA;MACA;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAd;MACAe;QACA;UACA;QACA;UACA;UACA;YACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA,6CACA;QACA;MAAA,EACA;MACA;MACA;QACA;QACAT;MACA;MACA;IACA;IACA;IACAU;MACA;IACA;EACA;EACAC;IACA;IACA;MACAhB;MACAiB;MACAC;MACAC;IACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MAAA;MACA;QACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACA;UACA;YAAA;YACA;UACA;YACA;UACA;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MACAC;MACA;MACA;MACA;QAAAtB;QAAAD;MACA;QACA;MACA;MACA;MACA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;YACAkB;UACA;QACA;MACA;IACA;IACA;IACAM;MAAA;MACA;MACA,wBACAL,OACAE;QACA;QACA;QACA;QACA;UAAApB;UAAAD;QACA;QACA;QACA;UACAkB;QACA;MACA;IACA;IACA;IACAO;MAAA;MACA;QACAzB;MACA;MACA;QACA;UACAJ;QACA;UACAA;QACA;MACA;MACA;QACA8B;MACA;QACA;UACAA;QACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACAC;QACA;UACAA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACA;YACAA;UACA;UACA;YACAA;UACA;QACA;QACA;MACA;MACA;QACAC;MACA;QACA;UACAA;QACA;MACA;MAEA;IACA;IACAC;MACA;QACAC;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACAD;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAE;MACA;IACA;EACA;AACA;AAAA,4B", "file": "uni_modules/uni-transition/components/uni-transition/uni-transition.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-transition.vue?vue&type=template&id=6369f8c4&\"\nvar renderjs\nimport script from \"./uni-transition.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-transition.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-transition/components/uni-transition/uni-transition.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=template&id=6369f8c4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"isShow\" ref=\"ani\" :animation=\"animationData\" :class=\"customClass\" :style=\"transformStyles\" @click=\"onClick\"><slot></slot></view>\r\n</template>\r\n\r\n<script>\r\nimport { createAnimation } from './createAnimation'\r\n\r\n/**\r\n * Transition 过渡动画\r\n * @description 简单过渡动画组件\r\n * @tutorial https://ext.dcloud.net.cn/plugin?id=985\r\n * @property {Boolean} show = [false|true] 控制组件显示或隐藏\r\n * @property {Array|String} modeClass = [fade|slide-top|slide-right|slide-bottom|slide-left|zoom-in|zoom-out] 过渡动画类型\r\n *  @value fade 渐隐渐出过渡\r\n *  @value slide-top 由上至下过渡\r\n *  @value slide-right 由右至左过渡\r\n *  @value slide-bottom 由下至上过渡\r\n *  @value slide-left 由左至右过渡\r\n *  @value zoom-in 由小到大过渡\r\n *  @value zoom-out 由大到小过渡\r\n * @property {Number} duration 过渡动画持续时间\r\n * @property {Object} styles 组件样式，同 css 样式，注意带’-‘连接符的属性需要使用小驼峰写法如：`backgroundColor:red`\r\n */\r\nexport default {\r\n\tname: 'uniTransition',\r\n\temits:['click','change'],\r\n\tprops: {\r\n\t\tshow: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tmodeClass: {\r\n\t\t\ttype: [Array, String],\r\n\t\t\tdefault() {\r\n\t\t\t\treturn 'fade'\r\n\t\t\t}\r\n\t\t},\r\n\t\tduration: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 300\r\n\t\t},\r\n\t\tstyles: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {}\r\n\t\t\t}\r\n\t\t},\n\t\tcustomClass:{\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisShow: false,\r\n\t\t\ttransform: '',\r\n\t\t\topacity: 1,\r\n\t\t\tanimationData: {},\r\n\t\t\tdurationTime: 300,\r\n\t\t\tconfig: {}\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\tshow: {\r\n\t\t\thandler(newVal) {\r\n\t\t\t\tif (newVal) {\r\n\t\t\t\t\tthis.open()\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 避免上来就执行 close,导致动画错乱\r\n\t\t\t\t\tif (this.isShow) {\r\n\t\t\t\t\t\tthis.close()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\t// 生成样式数据\r\n\t\tstylesObject() {\r\n\t\t\tlet styles = {\r\n\t\t\t\t...this.styles,\r\n\t\t\t\t'transition-duration': this.duration / 1000 + 's'\r\n\t\t\t}\r\n\t\t\tlet transform = ''\r\n\t\t\tfor (let i in styles) {\r\n\t\t\t\tlet line = this.toLine(i)\r\n\t\t\t\ttransform += line + ':' + styles[i] + ';'\r\n\t\t\t}\r\n\t\t\treturn transform\r\n\t\t},\r\n\t\t// 初始化动画条件\r\n\t\ttransformStyles() {\r\n\t\t\treturn 'transform:' + this.transform + ';' + 'opacity:' + this.opacity + ';' + this.stylesObject\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\t// 动画默认配置\r\n\t\tthis.config = {\r\n\t\t\tduration: this.duration,\r\n\t\t\ttimingFunction: 'ease',\r\n\t\t\ttransformOrigin: '50% 50%',\r\n\t\t\tdelay: 0\r\n\t\t}\r\n\t\tthis.durationTime = this.duration\r\n\t},\r\n\tmethods: {\r\n\t\t/**\r\n\t\t *  ref 触发 初始化动画\r\n\t\t */\r\n\t\tinit(obj = {}) {\r\n\t\t\tif (obj.duration) {\r\n\t\t\t\tthis.durationTime = obj.duration\r\n\t\t\t}\r\n\t\t\tthis.animation = createAnimation(Object.assign(this.config, obj),this)\r\n\t\t},\r\n\t\t/**\r\n\t\t * 点击组件触发回调\r\n\t\t */\r\n\t\tonClick() {\r\n\t\t\tthis.$emit('click', {\r\n\t\t\t\tdetail: this.isShow\r\n\t\t\t})\r\n\t\t},\r\n\t\t/**\r\n\t\t * ref 触发 动画分组\r\n\t\t * @param {Object} obj\r\n\t\t */\r\n\t\tstep(obj, config = {}) {\r\n\t\t\tif (!this.animation) return\n\t\t\tfor (let i in obj) {\r\n\t\t\t\ttry {\n\t\t\t\t\tif(typeof obj[i] === 'object'){\n\t\t\t\t\t\tthis.animation[i](...obj[i])\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.animation[i](obj[i])\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error(`方法 ${i} 不存在`)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.animation.step(config)\n\t\t\treturn this\r\n\t\t},\r\n\t\t/**\r\n\t\t *  ref 触发 执行动画\r\n\t\t */\r\n\t\trun(fn) {\r\n\t\t\tif (!this.animation) return\r\n\t\t\tthis.animation.run(fn)\r\n\t\t},\r\n\t\t// 开始过度动画\r\n\t\topen() {\r\n\t\t\tclearTimeout(this.timer)\r\n\t\t\tthis.transform = ''\r\n\t\t\tthis.isShow = true\r\n\t\t\tlet { opacity, transform } = this.styleInit(false)\r\n\t\t\tif (typeof opacity !== 'undefined') {\r\n\t\t\t\tthis.opacity = opacity\r\n\t\t\t}\r\n\t\t\tthis.transform = transform\r\n\t\t\t// 确保动态样式已经生效后，执行动画，如果不加 nextTick ，会导致 wx 动画执行异常\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t// TODO 定时器保证动画完全执行，目前有些问题，后面会取消定时器\r\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tthis.animation = createAnimation(this.config, this)\r\n\t\t\t\t\tthis.tranfromInit(false).step()\r\n\t\t\t\t\tthis.animation.run()\r\n\t\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\t\tdetail: this.isShow\r\n\t\t\t\t\t})\r\n\t\t\t\t}, 20)\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 关闭过度动画\r\n\t\tclose(type) {\r\n\t\t\tif (!this.animation) return\r\n\t\t\tthis.tranfromInit(true)\r\n\t\t\t\t.step()\r\n\t\t\t\t.run(() => {\r\n\t\t\t\t\tthis.isShow = false\r\n\t\t\t\t\tthis.animationData = null\r\n\t\t\t\t\tthis.animation = null\r\n\t\t\t\t\tlet { opacity, transform } = this.styleInit(false)\r\n\t\t\t\t\tthis.opacity = opacity || 1\r\n\t\t\t\t\tthis.transform = transform\r\n\t\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\t\tdetail: this.isShow\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t},\r\n\t\t// 处理动画开始前的默认样式\r\n\t\tstyleInit(type) {\r\n\t\t\tlet styles = {\r\n\t\t\t\ttransform: ''\r\n\t\t\t}\r\n\t\t\tlet buildStyle = (type, mode) => {\r\n\t\t\t\tif (mode === 'fade') {\r\n\t\t\t\t\tstyles.opacity = this.animationType(type)[mode]\r\n\t\t\t\t} else {\r\n\t\t\t\t\tstyles.transform += this.animationType(type)[mode] + ' '\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (typeof this.modeClass === 'string') {\r\n\t\t\t\tbuildStyle(type, this.modeClass)\r\n\t\t\t} else {\r\n\t\t\t\tthis.modeClass.forEach(mode => {\r\n\t\t\t\t\tbuildStyle(type, mode)\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\treturn styles\r\n\t\t},\r\n\t\t// 处理内置组合动画\r\n\t\ttranfromInit(type) {\r\n\t\t\tlet buildTranfrom = (type, mode) => {\r\n\t\t\t\tlet aniNum = null\r\n\t\t\t\tif (mode === 'fade') {\r\n\t\t\t\t\taniNum = type ? 0 : 1\r\n\t\t\t\t} else {\r\n\t\t\t\t\taniNum = type ? '-100%' : '0'\r\n\t\t\t\t\tif (mode === 'zoom-in') {\r\n\t\t\t\t\t\taniNum = type ? 0.8 : 1\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (mode === 'zoom-out') {\r\n\t\t\t\t\t\taniNum = type ? 1.2 : 1\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (mode === 'slide-right') {\r\n\t\t\t\t\t\taniNum = type ? '100%' : '0'\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (mode === 'slide-bottom') {\r\n\t\t\t\t\t\taniNum = type ? '100%' : '0'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.animation[this.animationMode()[mode]](aniNum)\r\n\t\t\t}\r\n\t\t\tif (typeof this.modeClass === 'string') {\r\n\t\t\t\tbuildTranfrom(type, this.modeClass)\r\n\t\t\t} else {\r\n\t\t\t\tthis.modeClass.forEach(mode => {\r\n\t\t\t\t\tbuildTranfrom(type, mode)\r\n\t\t\t\t})\r\n\t\t\t}\r\n\r\n\t\t\treturn this.animation\r\n\t\t},\r\n\t\tanimationType(type) {\r\n\t\t\treturn {\r\n\t\t\t\tfade: type ? 1 : 0,\r\n\t\t\t\t'slide-top': `translateY(${type ? '0' : '-100%'})`,\r\n\t\t\t\t'slide-right': `translateX(${type ? '0' : '100%'})`,\r\n\t\t\t\t'slide-bottom': `translateY(${type ? '0' : '100%'})`,\r\n\t\t\t\t'slide-left': `translateX(${type ? '0' : '-100%'})`,\r\n\t\t\t\t'zoom-in': `scaleX(${type ? 1 : 0.8}) scaleY(${type ? 1 : 0.8})`,\r\n\t\t\t\t'zoom-out': `scaleX(${type ? 1 : 1.2}) scaleY(${type ? 1 : 1.2})`\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 内置动画类型与实际动画对应字典\r\n\t\tanimationMode() {\r\n\t\t\treturn {\r\n\t\t\t\tfade: 'opacity',\r\n\t\t\t\t'slide-top': 'translateY',\r\n\t\t\t\t'slide-right': 'translateX',\r\n\t\t\t\t'slide-bottom': 'translateY',\r\n\t\t\t\t'slide-left': 'translateX',\r\n\t\t\t\t'zoom-in': 'scale',\r\n\t\t\t\t'zoom-out': 'scale'\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 驼峰转中横线\r\n\t\ttoLine(name) {\r\n\t\t\treturn name.replace(/([A-Z])/g, '-$1').toLowerCase()\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style></style>\n"], "sourceRoot": ""}