<template>
	<view class="xilu">
		<view class="container">
			<view class="m-header bg-white">
				<view class="g-custom-nav flex-box plr30"
					:style="{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }">
					<image @click="navBack" src="/static/icon/icon_back.png" mode="aspectFit" class="icon_back"></image>
					<view class="search_box flex-box">
						<view class="addr m-ellipsis" @click="bindCityChange()">{{currentCity?currentCity.name:''}}</view>
						<image class="icon_arrow" src="/static/icon/icon_arrow.png"></image>
						<view class="line"></view>
						<image class="icon_search" src="/static/icon/icon_search.png"></image>
						<input class="input flex-1 col-normal" confirm-type="search" type="text" @confirm="searchConfirm()" v-model="query.q" @focus="searchFocus()" placeholder="出发城市/目的地" placeholder-class="cola" />
					</view>
				</view>
			</view>

			<view class="pr" :style="{ paddingTop: 'calc(90rpx + ' + statusBarHeight + 'px)' }">
				<view class="p40">

					
					<block v-if="showSearch">
						<!-- <view class="xilu_title">热门搜索</view>
						<view class="flex-box flex-wrap flex-align-start">
							<view class="xilu_label1">自然风光</view>
							<view class="xilu_label1">山川河流</view>
							<view class="xilu_label1">家庭游玩</view>
							<view class="xilu_label1">新疆</view>
							<view class="xilu_label1">特色木屋</view>
							<view class="xilu_label1">自然风光</view>
							<view class="xilu_label1">山川河流</view>
						</view> -->
						
						<view class="xilu_title">历史搜索</view>
						<view class="flex-box flex-wrap flex-align-start">
							<view class="xilu_label2" @click="searchClick(item)" v-for="(item,index) in searchHistoryList" :key="index">{{item}}</view>
						</view>

					</block>

					<block v-else>
						<view class="xilu_top_box">
							<view class="fs30 col-5">共<text class="col-normal">{{total}}</text>个内容</view>
							<view class="g_tab flex-box">
								<view class="item" :class="{'active': tabIdx == 1}" @click="tabClick(1)">路线</view>
								<view class="item" :class="{'active': tabIdx == 2}" @click="tabClick(2)">景点</view>
							</view>
						</view>

						<view class="g_travel_list" v-if="tabIdx==1">
							<tour-list :tourList="tourList"></tour-list>
							<view class="g-btn3-wrap">
								<view class="g-btn3" @click="fetchTourList">{{tourListMore.text}}</view>
							</view>
						</view>

						<view class="g_travel_list" v-if="tabIdx==2">
							<scenery-list :sceneryList="sceneryList"></scenery-list>
							<view class="g-btn3-wrap">
								<view class="g-btn3" @click="fetchSceneryList">{{sceneryListMore.text}}</view>
							</view>
						</view>
						

					</block>


				</view>

			</view>

		</view>
	</view>
</template>

<script>
	const app = getApp();
	import tourList from '@/components/tour-list/tour-list.vue';
	import sceneryList from '@/components/scenery-list/scenery-list.vue'
	export default {
		components: {
			tourList,
			sceneryList
		},
		data() {
			return {
				statusBarHeight: 20,
				tabIdx: 1,
				showSearch:true,
				currentCity: null,
				query:{q:''},
				searchHistoryList: [],
				
				total:0,
				tourTotal: 0,
				sceneryTotal:0,
				tourList: [],
				tourListMore: {page: 1},
				sceneryList:[],
				sceneryListMore:{page:1},
			};
		},
		onLoad() {
			this.statusBarHeight = getApp().globalData.statusBarHeight;
			let page = this;
			this.currentCity = this.$core.getCurrentCity();
			uni.$on(app.globalData.Event.CurrentCityChange, function(currentCity) {
				page.currentCity = currentCity;
				page.refresh();
			});
			this.searchHistoryList = uni.getStorageSync('search_history') || [];
		},
		onReachBottom() {
			if(this.tabIdx == 1){
				this.fetchTourList();
			}else if(this.tabIdx == 2){
				this.fetchSceneryList();
			}
		},
		onUnload() {
			uni.$off(app.globalData.Event.CurrentCityChange,this);
		},
		methods: {
			navBack(){
				uni.navigateBack({})
			},
			//更换城市
			bindCityChange(){
				uni.navigateTo({
				    url: '/pages/change_city/change_city'
				})
			},
			//搜索
			searchConfirm() {
				let q = this.query.q.trim();
				if (q == '') {
					uni.showToast({title:"请输入搜索内容",icon:'none'});
					return;
				}
				//搜索列表存缓存
				let searchHistory = this.searchHistoryList;
				let index = searchHistory.indexOf(q);
				if (index !== -1) {
					searchHistory.splice(index, 1);
				}
				searchHistory.unshift(q.toString());
				uni.setStorageSync('search_history', searchHistory);
				this.searchHistoryList = searchHistory;
				this.showSearch = false;
				this.refresh();
			},
			searchFocus(){
				this.showSearch = true;
			},
			searchClick(e){
				this.query.q = e;
				this.searchConfirm();
			},
			
			tabClick(i) {
				this.tabIdx = i;
			},
			refresh(){
				this.tourList = [];
				this.tourListMore = {page:1};
				this.sceneryList = [];
				this.sceneryListMore = {page:1};
				this.fetchTourList();
				this.fetchSceneryList();
			},
			//线路
			fetchTourList() {
				let query = this.query;
				query.pagesize = 10;
				this.$util.fetch(this, 'xilutour.tour/lists', query, 'tourListMore', 'tourList', 'data', data => {
					this.tourTotal = data.total;
					this.total = this.tourTotal+ this.sceneryTotal;
				})
			},
			//景点
			fetchSceneryList() {
				let query = this.query;
				query.pagesize = 10;
				this.$util.fetch(this, 'xilutour.scenery/lists', query, 'sceneryListMore', 'sceneryList', 'data', data=>{
					this.sceneryTotal = data.total;
					this.total = this.tourTotal+ this.sceneryTotal;
				})
			},
		}
	}
</script>

<style lang="less" scoped>
	.g-custom-nav .input {
		color: var(--normal);
	}

	.g_tab {
		margin-left: -40rpx;
	}

	.xilu {
		.xilu_title {
			margin: 0 0 30rpx;
			font-size: 34rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 500;
			color: #101010;
			line-height: 36rpx;
		}

		.xilu_label1 {
			margin: 0 30rpx 30rpx 0;
			padding: 0 15rpx;
			height: 58rpx;
			background: rgba(5, 185, 174, 0.1);
			border-radius: 8rpx;
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: var(--normal);
			line-height: 58rpx;
			text-align: center;
		}

		.xilu_label2 {
			margin: 0 30rpx 30rpx 0;
			padding: 0 15rpx;
			height: 58rpx;
			background: #F7F9FB;
			border-radius: 8rpx;
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #555555;
			line-height: 58rpx;
			text-align: center;
		}

		.xilu_top_box {}
	}
</style>