<template>
	<view class="xilu">
		<view class="page-head">
			<view class="g_tab">
				<view class="item" :class="{'active': tabIdx == 1}" @click="tabClick(1)">系统消息</view>
				<view class="item" :class="{'active': tabIdx == 2}" @click="tabClick(2)">我的消息</view>
			</view>
		</view>
		<view class="container">
			<view v-if="tabIdx == 1">
				<view class="xilu_message" v-for="(item,index) in systemList" :key="index">
					<view class="flex-box">
						<view class="fs30 col-10 flex-1 fwb">{{item.title}}</view>
						<view class="fs28 col-5">{{item.createtime_text}}</view>
					</view>
					<view class="mt20"><rich-text :nodes="item.content">{{item.content}}</rich-text></view>
				</view>
				<view class="g-btn3-wrap">
					<view class="g-btn3" @click="fetchSystem()">{{systemListMore.text}}</view>
				</view>
			</view>
			<view v-if="tabIdx == 2">
				<view class="xilu_message" @click="bindRead(index)" :class="{active:item.read==0}" v-for="(item,index) in personalList" :key="index">
					<view class="flex-box">
						<view class="fs30 col-10 flex-1 fwb">{{item.title}}</view>
						<view class="fs28 col-5">{{item.createtime_text}}</view>
					</view>
					<view class="flex-box mt25">
						<image class="img" :src="item.extra.thumb_image" mode="aspectFill"></image>
						<view class="flex-1">{{item.content}}</view>
					</view>
				</view>
				<view class="g-btn3-wrap">
					<view class="g-btn3" @click="fetchPersonal()">{{personalListMore.text}}</view>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabIdx: 1,
				
				systemList: [],
				systemListMore:{page:1},
				
				personalList:[],
				personalListMore: {page:1}
			};
		},
		onLoad() {
			this.fetchSystem();
			this.fetchPersonal();
		},
		onReachBottom() {
			if(this.tabIdx == 2){
				this.fetchPersonal();
			}else{
				this.fetchSystem();
			}
		},
		methods: {
			tabClick(i) {
				this.tabIdx = i;
			},
			fetchSystem() {
				this.$util.fetch(this, 'xilutour.message/system_list', {pagesize:20}, 'systemListMore', 'systemList', 'data', data=>{
				  
				})
			},
			fetchPersonal() {
				this.$util.fetch(this, 'xilutour.message/personal_list', {pagesize:20}, 'personalListMore', 'personalList', 'data', data=>{
				  
				})
			},
			//提交
			bindRead(index){
				let messageList = this.personalList;
				let message = messageList[index];
				if(message.type==1 || message.type == 3 || message.type == 5){
					uni.navigateTo({
						url: '/pages/landscape_order_detail/landscape_order_detail?id='+message.extra.order_id
					})
				}else if(message.type == 2 || message.type == 4){
					uni.navigateTo({
						url: '/pages/travel_order_detail/travel_order_detail?id='+message.extra.order_id
					})
				}
				if(message.read == 1){
					return '';
				}
				this.$core.post({url: 'xilutour.message/set_read',data: {message_id: message.id},loading: false,success: ret => {
						this.personalList[index].read = 1;
						this.$forceUpdate();
					},fail: err => {
						console.log(err);
					}
				});
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		&_message {
			position: relative;
			padding: 30rpx;
			margin: 0 0 30rpx;
			background: #FFFFFF;
			border-radius: 30rpx;
			font-size: 28rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #333333;
			line-height: 40rpx;

			.img {
				margin-right: 20rpx;
				display: block;
				width: 120rpx;
				height: 120rpx;
				border-radius: 15rpx;
			}
		}

		&_message.active::after {
			content: '';
			position: absolute;
			right: 20rpx;
			top: 20rpx;
			width: 12rpx;
			height: 12rpx;
			background: #DB1428;
			border-radius: 50%;
		}

		.container {
			padding: 110rpx 30rpx 30rpx;
			background: #F7F9FB;
		}
		
			
		.page-head{
			background: #F7F9FB;
		}
	}
</style>