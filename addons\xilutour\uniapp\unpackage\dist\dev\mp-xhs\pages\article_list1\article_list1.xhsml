<view class="xilu data-v-d2f65048"><view class="page-head bg-ghostWhite data-v-d2f65048"><view class="g_tab data-v-d2f65048"><block xhs:for="{{categoryList}}" xhs:for-item="item" xhs:for-index="index" xhs:key="index"><view data-event-opts="{{[['tap',[['catagoryChange',[index]]]]]}}" class="{{['item','','data-v-d2f65048',(index==categroyIndex)?'active':'']}}" bindtap="__e">{{item.name}}</view></block></view></view><view class="container plr30 pb30 bg-ghostWhite data-v-d2f65048"><liu-waterfall vue-id="08c4d938-1" dataList="{{articleList}}" column="{{2}}" margin="{{0}}" radius="{{15}}" class="data-v-d2f65048" bind:__l="__l"></liu-waterfall><view class="g-btn3-wrap data-v-d2f65048"><view data-event-opts="{{[['tap',[['fetch',['$event']]]]]}}" class="g-btn3 data-v-d2f65048" bindtap="__e">{{articleListMore.text}}</view></view></view></view>