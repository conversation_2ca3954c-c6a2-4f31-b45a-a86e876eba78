<template>
	<view class="xilu">
		<view class="page-foot ptb15">
			<view class="g_order_foot1 flex-box">
				<view class="flex-1">
					<view class="col-price fs24"  v-if="order.state==0">
						<!-- <view>请在24小时内支付</view>
						<view>过期自动取消</view> -->
					</view>
				</view>
				<view v-if="order.state==0" class="btn2" @click="orderCancel()" >取消订单</view>
				<view v-if="order.state==0" class="btn3" @click="payment()">立即支付</view>
				<button v-if="order.state==1" class="btn2" open-type="contact">联系客服</button>
				<view class="btn3" v-if="order.state==2 && order.comment_status==0" @click="bindComment()">立即评价</view>
				<view class="btn_apply" v-if="order.state==1" @click="refund()">申请退款</view>
			</view>
		</view>

		<view class="container">
			<view class="xilu_status">
				<view class="title">{{order.state_text}}</view>
				<view v-if="order.state == 0">请在{{order.expiretime_text}}前支付订单，过期自动取消</view>

				<!-- 				<view class="title">待使用</view>-->
				<view v-if="order.state == 1">订单已支付完成～</view> 

				<!-- 	<view class="title">已完成</view>-->
				<view v-else-if="order.state == 2">本次服务已完成，感谢使用</view> 

				<!-- 			<view class="title">退款/取消</view>-->
				<view v-else-if="order.state>2">退款理由 不想去了</view> 
			</view>

			<view class="xilu_goods flex-box mb30">
				<image class="img" :src="order.order_project.thumb_image" mode="aspectFill"></image>
				<view class="flex-1">
					<view class="m-ellipsis fs36 col-10 mb10">{{order.order_project.scenery_name}}</view>
					<view class="fs30 col-3 mb10">项目名称项目名称</view>
					<view class="flex-box col-3 mb10">
						<text class="fs24">¥</text>
						<text class="fs30 flex-1">{{order.order_project.project_price}}</text>
						<view class="fs30 col-89 pr40">数量 {{order.total_count}}</view>
					</view>
					<view class="flex-box">
						<view class="flex-1">
							<text class="fs30 col-89">实付款 </text>
							<text class="fs30 col-price">¥</text>
							<text class="fs40 col-price">{{order.pay_price}}</text>
						</view>
					</view>
				</view>
			</view>

			<view class="opening_box mb30">
				<view class="flex-box mb30">
					<view class="col-normal fs30 mr30">开放时间</view>
					<view class="fs30 col-5 mr15">{{order.order_project.work_time}}</view>
					<image @click="callphone()" class="icon" src="/static/icon/icon_phone.png" mode="aspectFit"></image>
				</view>
				<view class="flex-box flex-align-start">
					<view class="flex-1 mr10 fs30 col-5">{{order.order_project.city?order.order_project.city.name:''}}{{order.order_project.district?order.order_project.district.name:''}}{{order.order_project.address}}</view>
					<image v-if="order.order_project.lat" @click="bindOpenLocation()" class="icon" src="/static/icon/icon_go.png" mode="aspectFit"></image>
				</view>
			</view>

			<view class="xilu_code_swiper" v-if="order.state==1&&order.order_qrcode.length>0">
				<swiper :circular="true" :interval="3000" :duration="1000" next-margin="35">
					<swiper-item v-for="(qrcode,index) in order.order_qrcode" :key="index">
						<view class="xilu_code_box " :class="{disabled:qrcode.verifier_status==1}">
							<view class="fs34 col-10 mb40">券码{{index+1}}</view>
							<view class="code">
								<image :src="qrcode.qrcode_text" mode="aspectFit"></image>
								<view class="mask"></view>
							</view>
							<view class="num_box flex-box">
								<view class="fs30 col-5 mr30">券码</view>
								<view class="flex-1 fs30 col-10 num">{{qrcode.code}}</view>
								<view class="copy" v-if="qrcode.verifier_status==0 && qrcode.code" @click="codeCopy(qrcode.code)">复制</view>
							</view>
							<view class="flex-box flex-center">
								<view class="index">{{index+1}}/{{order.order_qrcode.length}}</view>
							</view>
						</view>
					</swiper-item>
					
				</swiper>
			</view>

			<view class="g_order_info">
				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">订单号</view>
					<view class="fs30 col-10">{{order.order_no}}</view>
				</view>
				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">订单时间</view>
					<view class="fs30 col-10">{{order.createtime_text}}</view>
				</view>
				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">支付方式</view>
					<image src="/static/icon/icon_wx.png" mode="aspectFit" class="g-icon30 mr15"></image>
					<view class="fs30 col-10">微信支付</view>
				</view>

				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">商品金额</view>
					<view class="col-10">
						<text class="fs30">¥</text>
						<text class="fs40">{{order.total_price}}</text>
					</view>
				</view>

				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">优惠劵</view>
					<view class="col-10">
						<text class="fs24">-¥</text>
						<text class="fs34">{{order.coupon_price}}</text>
					</view>
				</view>

				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">支付金额</view>
					<view class="col-10">
						<text class="fs30">¥</text>
						<text class="fs40">{{order.pay_price}}</text>
					</view>
				</view>

				<view class="flex-box mb50" v-if="order.state>2">
					<view class="fs30 col-5 flex-1">退款金额</view>
					<view class="col-price">
						<text class="fs30">¥</text>
						<text class="fs40">{{order.refund_price}}</text>
					</view>
				</view>

				<view class="flex-box flex-end mb45">
					<view class="fs30 col-89 mr20">共计</view>
					<view class="col-price">
						<text class="fs30">¥</text>
						<text class="fs40">{{order.pay_price}}</text>
					</view>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				orderId: 0,
				order:{
					state_text:'',
					order_no:'',
					total_count: 0,
					createtime_text: '',
					total_price: 0,
					pay_price: 0,
					coupon_price: 0,
					order_project:{
						tel:'',
						address:'',
						scenery_name:'',
						thumb_image: '',
						project_name: '',
						
					},
				}
			};
		},
		onLoad(options) {
			this.orderId = options.id || 0;
			this.fetchDetail();
		},
		methods:{
			fetchDetail(){
				this.$core.get({url:'xilutour.scenery_order/detail',data:{order_id: this.orderId},success:(ret)=>{
					this.order = ret.data;
					uni.setNavigationBarTitle({
						title: ret.data.state_text
					})
				}});
			},
			codeCopy(code){
				uni.setClipboardData({
					data: code,
					success: function () {
						console.log('success');
					}
				});

			},
			//取消
			orderCancel(){
				let page = this;
				let order = this.order;
				uni.showModal({
					title:'提示',
					content: '确认取消订单？',
					success(res) {
						if(res.confirm){
							page.$core.post({url:'xilutour.scenery_order/cancel',data:{order_id: order.id},loading:true,success:(ret)=>{
								page.fetchDetail();
							 }});
						}
					}
				})
			},
			payment(){
				let order = this.order;
				//#ifdef MP-WEIXIN
				this.$core.post({url:'xilutour.pay/pay',data:{type:'scenery_order',pay_type:1,order_id:order.id,platform:'wxmini'},success:(ret)=>{
					let wxconfig =  ret.data;
					this.$core.payment(wxconfig,function(){
						
					})
				}});
				//#endif
			},
			
			//拨打电话
			callphone(){
				let tel = this.order.order_project.tel;
				uni.makePhoneCall({
					phoneNumber: tel
				})
			},
			//导航
			bindOpenLocation(){
				let scenery = this.order.order_project;
				let address = (scenery.city?scenery.city.name:'') + (scenery.district?scenery.district.name:'') + (scenery.address)
				uni.openLocation({
					latitude: Number(scenery.lat),
					longitude: Number(scenery.lng),
					name: scenery.scenery_name,
					address: address
				})
			},
			//退款
			refund(){
				let page = this;
				let order = this.order;
				uni.showModal({
					title: '提示',
					content: '确认退款',
					success(res) {
						if(res.confirm){
							page.$core.post({url:'xilutour.scenery_order/refund',data:{order_id:order.id},success:(ret)=>{
								uni.showToast({title: ret.msg,icon: 'none'});
								page.fetchDetail()
							},fail:(ret)=>{
								uni.showModal({
									title:'提示',
									content: ret.msg
								})
								return false;
							}
							});
						}
					}
				})
			},
			bindComment(){
				let order = this.order;
				uni.navigateTo({
					url: '/pages/immediate_evaluation/immediate_evaluation',
					events:{
						commentSuccess: data=>{
							order.comment_status=1;
							this.order = order;
						}
					},
					success(res) {
						res.eventChannel.emit("addComment",order)
					}
				})
			},
		}
	}
</script>

<style lang="less" scoped>
	.g_order_info {
		padding-bottom: 5rpx;
	}

	.g_order_foot1 {
		padding-left: 30rpx;
	}

	.xilu {
		&_status {
			padding: 30rpx;
			margin: 0 0 40rpx;
			background: rgba(5, 185, 174, 0.1);
			border-radius: 22rpx;
			font-size: 28rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #555555;
			line-height: 30rpx;

			.title {
				margin: 0 0 20rpx;
				font-size: 34rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: var(--normal);
				line-height: 36rpx;
			}
		}

		&_goods {


			.img {
				margin-right: 30rpx;
				display: block;
				width: 180rpx;
				height: 180rpx;
				border-radius: 15rpx;
			}
		}

		.opening_box {
			padding: 30rpx;
			background: #F7F9FB;
			border-radius: 20rpx;

			.icon {
				display: block;
				width: 34rpx;
				height: 34rpx;
			}
		}

		&_code_box {
			margin: 0 0 30rpx;
			padding: 30rpx 30rpx 50rpx;
			width: 670rpx;
			background: #F7F9FB;
			border-radius: 25rpx;

			.code {
				position: relative;
				margin: 0 auto 30rpx;
				width: 260rpx;
				height: 260rpx;

				image {
					display: block;
					width: 260rpx;
					height: 260rpx;
				}

				.mask {
					position: absolute;
					top: 0;
					right: 0;
					bottom: 0;
					left: 0;
					width: 260rpx;
					height: 260rpx;
					background: #D8D8D8;
					opacity: 0.9;
					display: none;
				}
			}

			.num_box {
				margin: 0 auto;
				padding: 0 30rpx 0 35rpx;
				width: 530rpx;
				height: 90rpx;
				background: #FFFFFF;
				border-radius: 34rpx;
				border: 1px solid #EEEEEE;

				.copy {
					width: 66rpx;
					height: 40rpx;
					background: var(--normal);
					border-radius: 14rpx;
					font-size: 22rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 40rpx;
					text-align: center;
				}
			}
		}

		&_code_box.disabled {
			.code {
				.mask {
					display: block;
				}
			}

			.num_box {
				.num {
					color: #898989;
					text-decoration: line-through;
				}

				.copy {
					display: none;
				}
			}
		}

		&_code_swiper {
			margin: 0 0 30rpx;

			swiper {
				height: 590rpx;

				.xilu_code_box {
					margin: 0;
					width: 568rpx;
					padding: 30rpx;

					.num_box {
						width: 508rpx;
					}

					.index {
						margin: 30rpx auto 0;
						padding: 0 6rpx;
						height: 34rpx;
						background: rgba(5, 185, 174, 0.1);
						border-radius: 11rpx;
						font-size: 24rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #05B9AE;
						line-height: 34rpx;
						text-align: center;
					}
				}
			}
		}

		.page-foot~.container {
			padding-bottom: 170rpx;
		}

		.container {
			padding-top: 30rpx;
			padding-left: 40rpx;
			padding-right: 40rpx;
		}

	}
</style>