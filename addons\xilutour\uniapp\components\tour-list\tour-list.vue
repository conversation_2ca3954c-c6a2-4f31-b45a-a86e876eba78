<template>
	<view>
		<navigator class="item" :url="'/pages/travel_detail/travel_detail?id='+item.id" hover-class="none" v-for="item in tourList">
				<view class="mb20">
					<text class="level" v-if="item.level">{{item.level.name}}</text>
					<text class="g_feng">{{item.points}}</text>
					<text class="fs36 col-10">{{item.name}}</text>
				</view>
				<view class="desc m-ellipsis mb15">{{item.sub_name}}</view>
				<view class="flex-box mb20">
					<view class="flex-1 col-price" v-if="item.tour_date">
						<text class="fs30">¥</text>
						<text class="fs40">{{item.tour_date.salesprice}}</text>
						<text class="fs30">起</text>
					</view>
					<view class="fs28 col-a">{{item.view_count}}人浏览</view>
				</view>
				<view class="img_wrap flex-box flex-align-start" v-if="item.images_text.length>0">
					<image :src="item.images_text[0]" mode="aspectFill" class="img1"></image>
					<view class="flex-1" v-if="item.images_text.length>1">
						<image :src="item.images_text[1]" mode="aspectFill" class="img2"></image>
						<image  v-if="item.images_text.length>2" :src="item.images_text[2]" mode="aspectFill" class="img2"></image>
					</view>
				</view>
			</navigator>
		
	</view>
</template>

<script>
	export default {
		name:"tour-list",
		props:{
			tourList:{
				type: Array,
				default:[]
			}
		},
		data() {
			return {
				
			};
		}
	}
</script>

<style>

</style>