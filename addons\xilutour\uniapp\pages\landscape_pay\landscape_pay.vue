<template>
	<view class="xilu">
		<view class="page-foot ptb15">
			<view class="g_order_foot1 flex-box">
				<view class="flex-1">
					<view class="col-price">
						<text class="fs30">¥</text>
						<text class="fs40">{{order.pay_price}}</text>
					</view>
					<view class="fs30 col-89 pl25">共计</view>
				</view>
				<view class="btn1" @click="createOrder()"  :loading="loading" :disabled="loading">立即支付</view>
			</view>
		</view>

		<view class="container">
			<view class="ptb30 plr40 bg-white">
				<view class="xilu_travel flex-box mb30">
					<image :src="order.scenery.thumb_image_text" mode="aspectFill" class="img mr30"></image>
					<view class="flex-1">
						<view class="fs36 col-10 mb15">{{order.scenery.name}}</view>
						<view class="fs30 col-3 mb15 m-ellipsis">{{order.project.name}}</view>
						<view class="fs26 col-89 mb15">{{order.project.attention}}</view>
						<view class="col-price">
							<text class="fs30">¥</text>
							<text class="fs40">{{order.project.salesprice}}</text>
						</view>
					</view>
				</view>

				<view class="opening_box">
					<view class="flex-box mb30">
						<view class="col-normal fs30 mr30">开放时间</view>
						<view class="fs30 col-5 mr15">{{order.scenery.work_time}}</view>
						<image @click="callphone()" class="icon" src="/static/icon/icon_phone.png" mode="aspectFit"></image>
					</view>
					<view class="flex-box flex-align-start">
						<view class="flex-1 mr10 fs30 col-5">{{order.scenery.city?order.scenery.city.name:''}}{{order.scenery.district?order.scenery.district.name:''}}{{order.scenery.address}}</view>
						<image v-if="order.scenery.lat" @click="bindOpenLocation()" class="icon" src="/static/icon/icon_go.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			<view class="ptb30 plr40">
				<view class="xilu_number flex-box mb30">
					<view class="flex-1">出行人数</view>
					<image src="/static/icon/icon_jian.png" @click="bindChangeCount('minus')" mode="aspectFit"></image>
					<view class="num">{{buyNum}}</view>
					<image src="/static/icon/icon_jia.png" @click="bindChangeCount('plus')" mode="aspectFit"></image>
				</view>

				<view class="g_order_info">
					<view class="flex-box mb50">
						<view class="fs30 col-5 flex-1">支付方式</view>
						<image src="/static/icon/icon_wx.png" mode="aspectFit" class="g-icon30 mr15"></image>
						<view class="fs30 col-10">微信支付</view>
					</view>

					<view class="flex-box mb50">
						<view class="fs30 col-5 flex-1">商品金额</view>
						<view class="col-10">
							<text class="fs30">¥</text>
							<text class="fs40">{{order.total_price}}</text>
						</view>
					</view>

					<view class="flex-box mb50" v-if="order.coupon_list.length>0" @click="couponPopOpen">
						<view class="fs30 col-5 flex-1">优惠劵</view>
						<view class="col-10 m-arrow-right">
							<text class="fs24">-¥</text>
							<text class="fs34">{{order.coupon_price}}</text>
						</view>
					</view>

					<view class="flex-box flex-end">
						<view class="fs30 col-89 mr20">共计</view>
						<view class="col-price">
							<text class="fs30">¥</text>
							<text class="fs40">{{order.total_price}}</text>
						</view>
					</view>

				</view>
			</view>
			
			
			<uni-popup ref="couponPopup" type="bottom">
				<view class="g_coupon_pop">
					<view class="fs30 col-10 tc mb30">优惠券</view>
					<image src="/static/icon/icon_close.png" mode="aspectFit" class="icon_close" @click="couponPopClose"></image>
			
					<view class="pop_coupon_wrap">
						<view class="pop_coupon" v-for="(coupon,index) in order.coupon_list" :key="index">
							<image src="/static/icon/icon_coupon_bg1.png" mode="aspectFill" class="bg"></image>
							<view class="inner flex-box">
								<view class="left">
									<view class="fwb mb20">
										<text class="fs24">¥</text>
										<text class="fs50">{{coupon.money}}</text>
									</view>
									<view class="man">满{{coupon.at_least}}可用</view>
								</view>
								<view class="right flex-1 flex-box">
									<view class="flex-1">
										<view class="fs30 mb20">{{coupon.name}}</view>
										<view class="fs24">{{coupon.use_end_time_text}}到期</view>
									</view>
									<view class="use active" v-if="order.coupon.id == coupon.id">
										<image src="/static/icon/icon_coupon_check.png" mode="aspectFit"></image>
									</view>
									<view class="use" @click="bindChooseCoupon(index)" v-else>选择</view>
									
								</view>
							</view>
						</view>
			
					</view>
			
			
					<view class="g-btn1" style="width: 670rpx;margin: 30rpx auto 0;">确定</view>
				</view>
			</uni-popup>


		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				sceneryId: 0,
				projectId: 0,
				buyNum:1,
				couponId:0,
				order:{
					pay_price:'0.00',
					coupon:null,
					coupon_list:[],
					coupon_price:'0.00',
					project:{
						salesprice:'0.00',
						attention:''
					},
					scenery:{
						name: '',
						
					},
					total_price:'0.00'
				},
				loading: false,
			};
		},
		onLoad(options) {
			this.sceneryId = options.scenery_id;
			this.projectId = options.project_id;
			this.preOrder();
		},
		methods:{
			preOrder(){
				this.$core.post({url:'xilutour.scenery_order/pre_order',data:{project_id: this.projectId,scenery_id:this.sceneryId,coupon_id:this.couponId,buy_num:this.buyNum},loading:true,success:(ret)=>{
					this.order = ret.data;
				 },
				 fail:(ret)=>{
					 uni.showModal({
					 	title: '提示',
						content: ret.msg,
						showCancel:false,
						success(res) {
							if(res.confirm){
								uni.navigateBack()
							}
						}
					 })
					 return false;
				 }
				 });
			},
			//增减用户
			bindChangeCount(type){
				if(type == 'plus'){
					this.buyNum = ++this.buyNum;
				}else{
					if(this.buyNum<=1){
						return false;
					}
					this.buyNum = --this.buyNum;
				}
				this.preOrder()
			},
			createOrder(){
				this.loading = true;
				this.$core.post({url: 'xilutour.scenery_order/create_order',data: {project_id: this.projectId,scenery_id:this.sceneryId,coupon_id:this.couponId,buy_num:this.buyNum},loading: true,success: ret => {
					//下单成功，前往收银台
					this.loading = false;
					this.payment(ret.data);
					},fail: err => {
						this.loading = false;
						console.log(err);
					}
				});
			},
			payment(order){
				//#ifdef MP-WEIXIN
				this.$core.post({url:'xilutour.pay/pay',data:{pay_type:1,order_id:order.id,platform:'wxmini',type:'scenery_order'},success:(ret)=>{
					let wxconfig =  ret.data;
					this.$core.payment(wxconfig,function(){
						uni.redirectTo({
							url: '/pages/landscape_order/landscape_order'
						})
					})
				}});
				//#endif
			},
			//拨打电话
			callphone(){
				let tel = this.order.scenery.tel;
				uni.makePhoneCall({
					phoneNumber: tel
				})
			},
			//导航
			bindOpenLocation(){
				let scenery = this.order.scenery;
				let address = (scenery.city?scenery.city.name:'') + (scenery.district?scenery.district.name:'') + (scenery.address)
				uni.openLocation({
					latitude: Number(scenery.lat),
					longitude: Number(scenery.lng),
					name: scenery.name,
					address: address
				})
			},
			// 打开优惠券弹窗
			couponPopOpen() {
				this.$refs.couponPopup.open();
			},
			// 关闭优惠券弹窗
			couponPopClose() {
				this.$refs.couponPopup.close();
			},
			//选择优惠券
			bindChooseCoupon(index){
				this.couponId = this.order.coupon_list[index].id;
				this.preOrder();
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		.g_order_info{
			background-color: #fff;
		}
		
		&_travel {
			.img {
				display: block;
				width: 180rpx;
				height: 180rpx;
				border-radius: 15rpx;
			}
		}

		&_number {
			padding: 0 35rpx 0 30rpx;
			width: 670rpx;
			height: 110rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
			border-radius: 25rpx;
			font-size: 30rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #555555;
			line-height: 34rpx;

			image {
				display: block;
				width: 40rpx;
				height: 40rpx;
			}

			.num {
				width: 62rpx;
				font-size: 36rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #101010;
				line-height: 40rpx;
				text-align: center;
			}
		}


		.container {
			padding: 0 0 200rpx !important;
			background: #F7F9FB;
		}

		.opening_box {
			padding: 30rpx;
			background: #F7F9FB;
			border-radius: 20rpx;

			.icon {
				display: block;
				width: 34rpx;
				height: 34rpx;
			}
		}

		.page-foot {
			background: #F7F9FB;
		}

		.page-foot::after {
			background: #F7F9FB;
		}
	}
</style>