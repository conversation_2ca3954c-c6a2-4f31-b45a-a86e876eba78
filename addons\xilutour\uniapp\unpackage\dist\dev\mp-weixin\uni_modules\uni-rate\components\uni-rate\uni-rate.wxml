<view><view data-ref="uni-rate" class="uni-rate vue-ref"><block wx:for="{{stars}}" wx:for-item="star" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['touchstart',[['touchstart',['$event']]]],['touchmove',[['touchmove',['$event']]]],['mousedown',[['mousedown',['$event']]]],['mousemove',[['mousemove',['$event']]]],['mouseleave',[['mouseleave',['$event']]]]]}}" class="{{['uni-rate__icon',(disabled)?'uni-cursor-not-allowed':'']}}" style="{{'margin-right:'+(marginNumber+'px')+';'}}" catchtouchstart="__e" catchtouchmove="__e" catchmousedown="__e" catchmousemove="__e" bindmouseleave="__e"><uni-icons vue-id="{{'59bffe5a-1-'+index}}" color="{{color}}" size="{{size}}" type="{{isFill?'star-filled':'star'}}" bind:__l="__l"></uni-icons><view class="uni-rate__icon-on" style="{{'width:'+(star.activeWitch)+';'}}"><uni-icons vue-id="{{'59bffe5a-2-'+index}}" color="{{disabled?disabledColor:activeColor}}" size="{{size}}" type="star-filled" bind:__l="__l"></uni-icons></view></view></block></view></view>