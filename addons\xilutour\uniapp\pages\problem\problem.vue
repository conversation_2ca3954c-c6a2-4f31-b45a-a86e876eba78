<template>
	<view class="xilu">
		<view class="container">
			<navigator class="xilu_problem" :url="'/pages/problem_detail/problem_detail?id='+item.id" hover-class="none" v-for="(item,index) in qaList" :key="index">
				<view class="title">
					{{item.name}}
				</view>
				<view class="text m-ellipsis-l3"><rich-text :nodes="item.content"></rich-text></view>
			</navigator>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				qaList:[],
				qaListMore:{page:1}
			};
		},
		onLoad() {
			this.fetch();
		},
		onReachBottom() {
			this.fetch();
		},
		methods:{
			
			fetch() {
				this.$util.fetch(this, 'xilutour.article/question', {pagesize:10}, 'qaListMore', 'qaList', 'data', data=>{
				  
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		&_problem {
			margin: 0 0 30rpx;
			padding: 30rpx 30rpx 26rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
			border-radius: 24rpx;

			.title {
				margin: 0 0 30rpx;
				font-size: 34rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #101010;
				line-height: 36rpx;
			}

			.text {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #555555;
				line-height: 44rpx;
			}
		}

		.container {
			padding: 30rpx;
			background: #F7F9FB;
		}
	}
</style>