{"version": 3, "sources": ["webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue?a687", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue?6f21", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue?e39c", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue?09ab", "uni-app:///uni_modules/lime-painter/components/l-painter/l-painter.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue?16da", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/lime-painter/components/l-painter/l-painter.vue?c35b"], "names": ["name", "mixins", "data", "use2dCanvas", "canvasHeight", "canvasWidth", "parentWidth", "isPC", "inited", "progress", "first", "done", "computed", "styles", "canvasId", "size", "dpr", "boardWidth", "width", "boardHeight", "height", "hasBoard", "elements", "watch", "created", "SDKVersion", "version", "platform", "mounted", "setTimeout", "deep", "immediate", "methods", "watchRender", "clearTimeout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filePath", "param", "pathType", "getSize", "args", "canvasToTempFilePathSync", "getParentWeith", "uni", "in", "select", "boundingClientRect", "exec", "resolve", "render", "ctx", "canvas", "after<PERSON>elay", "Promise", "top", "left", "Object", "context", "pixelRatio", "fixed", "listen", "onProgress", "onEffectFail", "then", "catch", "draw", "node", "canvasDraw", "getContext", "console", "type", "_getContext", "canvasToTempFilePath", "tempFile<PERSON>ath", "reject", "destWidth", "destHeight", "success", "copyArgs", "x", "y", "fileType", "quality", "fail"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACqN;AACrN,gBAAgB,gNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAg2B,CAAgB,kyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsBp3B;AACA;AACA;AAGA;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eAKA;EACAA;EACAC;EACAC;IACA;MAEAC;MAKAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAA;QAAAC;MACA;IACA;IACAC;MACA;QAAA;QAAAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IAEAR;MAEA;QACA;MACA;IAKA;EAEA;EACAS;IACA;MAAAC;MAAAC;MAAAC;IAEA;IACA;EAQA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;cACA;gBACAC;kBACA;oBACAC;oBACAC;kBACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACAC;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,QACAC;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAF;gBAAA;gBAAA;cAAA;gBAAA,MACAE;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAF;cAAA;gBAEA;kBACA;gBACA;gBAAA,kCACAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,QACAC;gBAAA,QACAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA,MACAtB;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACAuB;MAAA;MACA;QACA;UACA;UACA;QACA;MACA;QACAV;MACA;IAEA;IAEAW;MAAA;MACA;QACAC,0BACAC,WACAC,wBACAC,qBACAC;UACA;UACA;UACA;UACAC;QACA;MACA;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAT;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAU;gBAEA/C,cAKA,OALAA,aACAc,aAIA,OAJAA,YACAE,cAGA,OAHAA,aACAgC,SAEA,OAFAA,QACAC,aACA,OADAA;gBAAA,MAEAjD;kBAAA;kBAAA;gBAAA;gBAAA,kCACAkD;cAAA;gBAEA;kBACAC;kBACAC;kBACArC;kBACAE;gBACA;gBACA;kBAAA,QACAoB;kBAAA,QACAA;kBACA;oBACAgB;sBAAAtC;oBAAA;kBACA;kBACAmB;oBACAoB;oBACAN;oBACAjC;oBACAE;oBACAsC;oBACAC;oBACAC;sBACAC;wBACA;wBACA;sBACA;sBACAC;wBACA;sBACA;oBACA;kBACA;kBACA;oBACAzB;kBACA;kBACA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAAnB;gBAAAE;gBACA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;kBAAA;gBAAA;cAAA;gBAAA,IACAjB;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,MAEAiD;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;gBACA;kBACA,8BACAW;oBACA;kBACA,GACAC;oBACA;kBACA;gBACA;gBAAA,kCACAX;kBACAH;kBACAe;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;MACA;QAAA;UAAA;YAAA;UAAA,UACAf;QAAA;MAAA;IACA;IACAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBACAC;gBAAA,kCACAhB;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAA;cAAA;gBAEAiB;gBACAC;kBACA;oBACA5B,0BACAC,WACAC,oCACAC,qBACAC;sBACA;wBACA;wBACA;0BACA;0BACA;0BACA;wBACA;wBACA;0BACAG;wBACA;wBAIA;wBACAF;sBACA;oBACA;kBACA;gBACA;gBAAA,IACA7C;kBAAA;kBAAA;gBAAA;gBAAA,kCACAoE;cAAA;gBAAA,kCAEA;kBACA5B,0BACAC,WACAC,oCACAqB,OACAnB;oBACA;oBACA;sBACA;sBACAC;oBACA;oBACA;oBACA;sBACA;sBACA;sBACA;oBACA;oBACA;oBACAA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAwB;MAAA;MAAA;MACA;QAAA;UAAA;UAAA;YAAA;cAAA;gBAAA;kBACArE;kBAAA,KACAA;oBAAA;oBAAA;kBAAA;kBAAA;kBAEA;oBAAAqC;kBAAA;kBAAA;kBAAA,OACA;gBAAA;kBAAAiC;kBACAjC;oBAAAiC;kBAAA;kBACAzB;oBAAAyB;kBAAA;kBAAA;kBAAA;gBAAA;kBAAA;kBAAA;kBAEAjC;kBACAkC;gBAAA;kBAAA;kBAAA;gBAAA;kBAAA,QAGA;kBACAC;kBACAC;kBAKAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;8BAAA;8BAAA,OAEA;4BAAA;8BAAAJ;8BACAzB;gCAAAyB;8BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA,gBAPAI;sBAAA;oBAAA;kBAAA;kBAQAC;oBACAC;oBACAC;oBACA9D;oBACAE;oBACAuD;oBACAC;oBACA9D;oBACAmE;oBACAC;oBACAL;oBACAM;kBACA;kBAKAxC;gBAAA;gBAAA;kBAAA;cAAA;YAAA;UAAA;QAAA,CAGA;QAAA;UAAA;QAAA;MAAA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC7XA;AAAA;AAAA;AAAA;AAAutC,CAAgB,6lCAAG,EAAC,C;;;;;;;;;;;ACA3uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/lime-painter/components/l-painter/l-painter.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./l-painter.vue?vue&type=template&id=cae877da&\"\nvar renderjs\nimport script from \"./l-painter.vue?vue&type=script&lang=js&\"\nexport * from \"./l-painter.vue?vue&type=script&lang=js&\"\nimport style0 from \"./l-painter.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/lime-painter/components/l-painter/l-painter.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=template&id=cae877da&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"lime-painter\" ref=\"limepainter\">\r\n\t\t<view v-if=\"canvasId && size\" :style=\"styles\">\r\n\t\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t\t<canvas class=\"lime-painter__canvas\" v-if=\"use2dCanvas\" :id=\"canvasId\" type=\"2d\" :style=\"size\"></canvas>\r\n\t\t\t<canvas class=\"lime-painter__canvas\" v-else :canvas-id=\"canvasId\" :style=\"size\" :id=\"canvasId\"\r\n\t\t\t\t:width=\"boardWidth * dpr\" :height=\"boardHeight * dpr\"></canvas>\r\n\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t\t<web-view :style=\"size\" ref=\"webview\"\r\n\t\t\t\tsrc=\"/uni_modules/lime-painter/static/index.html\"\r\n\t\t\t\tclass=\"lime-painter__canvas\" @pagefinish=\"onPageFinish\" @error=\"onError\" @onPostMessage=\"onMessage\">\r\n\t\t\t</web-view>\r\n\t\t\t<!-- #endif -->\r\n\t\t</view>\r\n\t\t<slot />\r\n\t</view>\r\n</template>\r\n\r\n\r\n<script>\r\n\timport { parent } from '../common/relation'\r\n\timport props from './props'\r\n\timport {toPx, base64ToPath, pathToBase64, isBase64, sleep, getImageInfo}from './utils';\r\n\t//  #ifndef APP-NVUE\r\n\timport { compareVersion } from './utils';\r\n\timport Painter from './painter'\r\n\tconst nvue = {}\r\n\t//  #endif\r\n\t//  #ifdef APP-NVUE\r\n\timport nvue from './nvue'\r\n\t//  #endif\r\n\texport default {\r\n\t\tname: 'lime-painter',\r\n\t\tmixins: [props, parent('painter'), nvue],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO || MP-ALIPAY\r\n\t\t\t\tuse2dCanvas: true,\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef MP-WEIXIN || MP-TOUTIAO || MP-ALIPAY\r\n\t\t\t\tuse2dCanvas: false,\r\n\t\t\t\t// #endif\r\n\t\t\t\tcanvasHeight: 150,\r\n\t\t\t\tcanvasWidth: null,\r\n\t\t\t\tparentWidth: 0,\r\n\t\t\t\tisPC: false,\r\n\t\t\t\tinited: false,\r\n\t\t\t\tprogress: 0,\r\n\t\t\t\tfirst: 0,\r\n\t\t\t\tdone: false,\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tstyles() {\r\n\t\t\t\treturn `${this.size}${this.customStyle||''}`\r\n\t\t\t},\r\n\t\t\tcanvasId() {\r\n\t\t\t\treturn `l-painter${this._uid || this._.uid}`\r\n\t\t\t},\r\n\t\t\tsize() {\r\n\t\t\t\tif (this.boardWidth && this.boardHeight) {\r\n\t\t\t\t\treturn `width:${this.boardWidth}px; height: ${this.boardHeight}px;`;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdpr() {\r\n\t\t\t\treturn this.pixelRatio || uni.getSystemInfoSync().pixelRatio;\r\n\t\t\t},\r\n\t\t\tboardWidth() {\r\n\t\t\t\tconst {width = 0} = (this.elements && this.elements.css) || this.elements || this\r\n\t\t\t\treturn toPx(width) || Math.max(toPx(width), toPx(this.canvasWidth));\r\n\t\t\t},\r\n\t\t\tboardHeight() {\r\n\t\t\t\tconst {height = 0} = (this.elements && this.elements.css) || this.elements || this\r\n\t\t\t\treturn toPx(height) || Math.max(toPx(height), toPx(this.canvasHeight));\r\n\t\t\t},\r\n\t\t\thasBoard() {\r\n\t\t\t\treturn this.board && Object.keys(this.board).length\r\n\t\t\t},\r\n\t\t\telements() {\r\n\t\t\t\treturn JSON.parse(JSON.stringify(this.hasBoard ? this.board : this.el))\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// #ifdef MP-WEIXIN ||  MP-ALIPAY\r\n\t\t\tsize(v) {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tif (this.use2dCanvas) {\r\n\t\t\t\t\tthis.inited = false;\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\t\tthis.inited = false;\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tconst { SDKVersion, version, platform } = uni.getSystemInfoSync();\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.isPC = /windows/i.test(platform)\r\n\t\t\tthis.use2dCanvas = this.type === '2d' && compareVersion(SDKVersion, '2.9.2') >= 0  && !this.isPC;\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\tthis.use2dCanvas = this.type === '2d' && compareVersion(SDKVersion, '1.78.0') >= 0;\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\tthis.use2dCanvas = this.type === '2d' && compareVersion(my.SDKVersion, '2.7.15') >= 0;\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tasync mounted() {\r\n\t\t\tawait sleep(30)\r\n\t\t\tawait this.getParentWeith()\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.$watch('elements', this.watchRender, {\r\n\t\t\t\t\t\tdeep: true,\r\n\t\t\t\t\t\timmediate: true\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 30)\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync watchRender(val, old) {\r\n\t\t\t\tif (!val || !val.views || (!this.first ? !val.views.length : !this.first) || JSON.stringify(val) === '{}' || JSON.stringify(val) == JSON.stringify(old)) return;\r\n\t\t\t\tthis.first = 1\r\n\t\t\t\tclearTimeout(this.rendertimer)\r\n\t\t\t\tthis.rendertimer = setTimeout(() => {\r\n\t\t\t\t\tthis.render(val);\r\n\t\t\t\t}, this.beforeDelay)\r\n\t\t\t},\r\n\t\t\tasync setFilePath(path, param) {\r\n\t\t\t\tlet filePath = path\r\n\t\t\t\tconst {pathType = this.pathType} =  param || this\r\n\t\t\t\tif (pathType == 'base64' && !isBase64(path)) {\r\n\t\t\t\t\tfilePath = await pathToBase64(path)\r\n\t\t\t\t} else if (pathType == 'url' && isBase64(path)) {\r\n\t\t\t\t\tfilePath = await base64ToPath(path)\r\n\t\t\t\t}\r\n\t\t\t\tif (param && param.isEmit) {\r\n\t\t\t\t\tthis.$emit('success', filePath);\r\n\t\t\t\t}\r\n\t\t\t\treturn filePath\r\n\t\t\t},\r\n\t\t\tasync getSize(args) {\r\n\t\t\t\tconst {width} = args.css || args\r\n\t\t\t\tconst {height} = args.css || args\r\n\t\t\t\tif (!this.size) {\r\n\t\t\t\t\tif (width || height) {\r\n\t\t\t\t\t\tthis.canvasWidth = width || this.canvasWidth\r\n\t\t\t\t\t\tthis.canvasHeight = height || this.canvasHeight\r\n\t\t\t\t\t\tawait sleep(30);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tawait this.getParentWeith()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcanvasToTempFilePathSync(args) {\r\n\t\t\t\tthis.stopWatch = this.$watch('done', (v) => {\r\n\t\t\t\t\tif (v) {\r\n\t\t\t\t\t\tthis.canvasToTempFilePath(args)\r\n\t\t\t\t\t\tthis.stopWatch && this.stopWatch()\r\n\t\t\t\t\t}\r\n\t\t\t\t}, {\r\n\t\t\t\t\timmediate: true\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\tgetParentWeith() {\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t\t.in(this)\r\n\t\t\t\t\t\t.select(`.lime-painter`)\r\n\t\t\t\t\t\t.boundingClientRect()\r\n\t\t\t\t\t\t.exec(res => {\r\n\t\t\t\t\t\t\tthis.parentWidth = Math.ceil(res[0].width)\r\n\t\t\t\t\t\t\tthis.canvasWidth = this.parentWidth || 300\r\n\t\t\t\t\t\t\tthis.canvasHeight = res[0].height || this.canvasHeight||150\r\n\t\t\t\t\t\t\tresolve(res[0])\r\n\t\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync render(args = {}) {\r\n\t\t\t\tthis.progress = 0\r\n\t\t\t\tthis.done = false\r\n\t\t\t\tawait this.getSize(args)\r\n\t\t\t\tconst ctx = await this.getContext();\r\n\t\t\t\tlet {\r\n\t\t\t\t\tuse2dCanvas,\r\n\t\t\t\t\tboardWidth,\r\n\t\t\t\t\tboardHeight,\r\n\t\t\t\t\tcanvas,\r\n\t\t\t\t\tafterDelay\r\n\t\t\t\t} = this;\r\n\t\t\t\tif (use2dCanvas && !canvas) {\r\n\t\t\t\t\treturn Promise.reject(new Error('render: fail canvas has not been created'));\r\n\t\t\t\t}\r\n\t\t\t\tthis.boundary = {\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\twidth: boardWidth,\r\n\t\t\t\t\theight: boardHeight\r\n\t\t\t\t};\r\n\t\t\t\tif (!this.painter) {\r\n\t\t\t\t\tconst {width} = args.css || args\r\n\t\t\t\t\tconst {height} = args.css || args\r\n\t\t\t\t\tif(!width && this.parentWidth) {\r\n\t\t\t\t\t\tObject.assign(args, {width: this.parentWidth})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst param = {\r\n\t\t\t\t\t\tcontext: ctx,\r\n\t\t\t\t\t\tcanvas,\r\n\t\t\t\t\t\twidth: boardWidth,\r\n\t\t\t\t\t\theight: boardHeight,\r\n\t\t\t\t\t\tpixelRatio: this.dpr,\r\n\t\t\t\t\t\tfixed: `${this.width || width ? 'width':''}${this.height || height ? 'height':''}`,\r\n\t\t\t\t\t\tlisten: {\r\n\t\t\t\t\t\t\tonProgress: (v) => {\r\n\t\t\t\t\t\t\t\tthis.progress = v\r\n\t\t\t\t\t\t\t\tthis.$emit('progress', v)\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tonEffectFail: (err) => {\r\n\t\t\t\t\t\t\t\tthis.$emit('faill', err)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(!use2dCanvas) {\r\n\t\t\t\t\t\tparam.createImage = getImageInfo\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.painter = new Painter(param, this)\r\n\t\t\t\t}// vue3 赋值给data会引起图片无法绘制 \r\n\t\t\t\t\r\n\t\t\t\tconst { width, height } = await this.painter.source(this.elements.views.length ? args : JSON.parse(JSON.stringify(args)))\r\n\t\t\t\tthis.boundary.height = this.canvasHeight = height\r\n\t\t\t\tthis.boundary.width = this.canvasWidth = width\r\n\t\t\t\t// await sleep(this.sleep);\r\n\t\t\t\tawait this.painter.render()\r\n\t\t\t\tawait new Promise(resolve => this.$nextTick(resolve));\r\n\t\t\t\tif (!use2dCanvas) {\r\n\t\t\t\t\tawait this.canvasDraw();\r\n\t\t\t\t}\r\n\t\t\t\tif (afterDelay && use2dCanvas) {\r\n\t\t\t\t\tawait sleep(afterDelay);\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('done');\r\n\t\t\t\tthis.done = true\r\n\t\t\t\tif (this.isCanvasToTempFilePath) {\r\n\t\t\t\t\tthis.canvasToTempFilePath()\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tthis.$emit('success', res.tempFilePath)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\tthis.$emit('fail', new Error(JSON.stringify(err)));\r\n\t\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\treturn Promise.resolve({\r\n\t\t\t\t\tctx,\r\n\t\t\t\t\tdraw: this.painter,\r\n\t\t\t\t\tnode: this.node\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcanvasDraw(flag = false) {\r\n\t\t\t\treturn new Promise((resolve, reject) => this.ctx.draw(flag, () => setTimeout(() => resolve(), this\r\n\t\t\t\t\t.afterDelay)));\r\n\t\t\t},\r\n\t\t\tasync getContext() {\r\n\t\t\t\tif (!this.canvasWidth) {\r\n\t\t\t\t\tthis.$emit('fail', 'painter no size')\r\n\t\t\t\t\tconsole.error('painter no size: 请给画板或父级设置尺寸')\r\n\t\t\t\t\treturn Promise.reject();\r\n\t\t\t\t}\r\n\t\t\t\tif (this.ctx && this.inited) {\r\n\t\t\t\t\treturn Promise.resolve(this.ctx);\r\n\t\t\t\t}\r\n\t\t\t\tconst { type, use2dCanvas, dpr, boardWidth, boardHeight } = this;\r\n\t\t\t\tconst _getContext = () => {\r\n\t\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t\t\t.in(this)\r\n\t\t\t\t\t\t\t.select(`#${this.canvasId}`)\r\n\t\t\t\t\t\t\t.boundingClientRect()\r\n\t\t\t\t\t\t\t.exec(res => {\r\n\t\t\t\t\t\t\t\tif (res) {\r\n\t\t\t\t\t\t\t\t\tconst ctx = uni.createCanvasContext(this.canvasId, this);\r\n\t\t\t\t\t\t\t\t\tif (!this.inited) {\r\n\t\t\t\t\t\t\t\t\t\tthis.inited = true;\r\n\t\t\t\t\t\t\t\t\t\tthis.use2dCanvas = false;\r\n\t\t\t\t\t\t\t\t\t\tthis.canvas = res;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tif (this.isPC) {\r\n\t\t\t\t\t\t\t\t\t\tctx.scale(1 / dpr, 1 / dpr);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\t\t\t\t\t\t\tctx.scale(dpr, dpr);\r\n\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\tthis.ctx = ctx\r\n\t\t\t\t\t\t\t\t\tresolve(this.ctx);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t};\r\n\t\t\t\tif (!use2dCanvas) {\r\n\t\t\t\t\treturn _getContext();\r\n\t\t\t\t}\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t\t.in(this)\r\n\t\t\t\t\t\t.select(`#${this.canvasId}`)\r\n\t\t\t\t\t\t.node()\r\n\t\t\t\t\t\t.exec(res => {\r\n\t\t\t\t\t\t\tlet {node: canvas} = res[0];\r\n\t\t\t\t\t\t\tif (!canvas) {\r\n\t\t\t\t\t\t\t\tthis.use2dCanvas = false;\r\n\t\t\t\t\t\t\t\tresolve(this.getContext());\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tconst ctx = canvas.getContext(type);\r\n\t\t\t\t\t\t\tif (!this.inited) {\r\n\t\t\t\t\t\t\t\tthis.inited = true;\r\n\t\t\t\t\t\t\t\tthis.use2dCanvas = true;\r\n\t\t\t\t\t\t\t\tthis.canvas = canvas;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.ctx = ctx\r\n\t\t\t\t\t\t\tresolve(this.ctx);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcanvasToTempFilePath(args = {}) {\r\n\t\t\t\treturn new Promise(async (resolve, reject) => {\r\n\t\t\t\t\tconst { use2dCanvas, canvasId, dpr, fileType, quality } = this;\r\n\t\t\t\t\tif (use2dCanvas) {\r\n\t\t\t\t\t\ttry{\r\n\t\t\t\t\t\t\tif(!args.pathType && !this.pathType) {args.pathType = 'url'}\r\n\t\t\t\t\t\t\tconst tempFilePath = await this.setFilePath(this.canvas.toDataURL(`image/${args.fileType||fileType}`.replace(/pg/, 'peg'), args.quality||quality), args)\r\n\t\t\t\t\t\t\targs.success && args.success({tempFilePath})\r\n\t\t\t\t\t\t\tresolve({tempFilePath})\r\n\t\t\t\t\t\t}catch(e){\r\n\t\t\t\t\t\t\targs.fail && args.fail(e)\r\n\t\t\t\t\t\t\treject(e)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tlet { top: y = 0, left: x = 0, width, height } = this.boundary || this;\r\n\t\t\t\t\t\tlet destWidth = width * dpr;\r\n\t\t\t\t\t\tlet destHeight = height * dpr;\r\n\t\t\t\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\t\t\t\twidth = destWidth;\r\n\t\t\t\t\t\theight = destHeight;\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\tconst success = async (res) => {\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\tconst tempFilePath = await this.setFilePath(res.tempFilePath || res)\r\n\t\t\t\t\t\t\t\tresolve(Object.assign(res, {tempFilePath}))\r\n\t\t\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t\t\tthis.$emit('fail', e)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tconst copyArgs = Object.assign({\r\n\t\t\t\t\t\t\tx,\r\n\t\t\t\t\t\t\ty,\r\n\t\t\t\t\t\t\twidth,\r\n\t\t\t\t\t\t\theight,\r\n\t\t\t\t\t\t\tdestWidth,\r\n\t\t\t\t\t\t\tdestHeight,\r\n\t\t\t\t\t\t\tcanvasId,\r\n\t\t\t\t\t\t\tfileType,\r\n\t\t\t\t\t\t\tquality,\r\n\t\t\t\t\t\t\tsuccess,\r\n\t\t\t\t\t\t\tfail: reject\r\n\t\t\t\t\t\t}, args);\r\n\t\t\t\t\t\t// #ifdef MP-ALIPAY\r\n\t\t\t\t\t\tuni.canvasToTempFilePath(copyArgs);\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\t\t\t\tuni.canvasToTempFilePath(copyArgs, this);\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\t.lime-painter,\r\n\t.lime-painter__canvas {\r\n\t\t// #ifndef APP-NVUE\r\n\t\twidth: 100%;\r\n\t\t// #endif\r\n\t\t// #ifdef APP-NVUE\r\n\t\tflex: 1;\r\n\t\t// #endif\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./l-painter.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341784\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}