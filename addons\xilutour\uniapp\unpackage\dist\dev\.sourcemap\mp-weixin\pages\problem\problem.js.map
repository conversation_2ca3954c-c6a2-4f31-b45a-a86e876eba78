{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/problem/problem.vue?5c9a", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/problem/problem.vue?14a5", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/problem/problem.vue?54f7", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/problem/problem.vue?76be", "uni-app:///pages/problem/problem.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/problem/problem.vue?7328", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/problem/problem.vue?7ffb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "qaList", "qaListMore", "page", "onLoad", "onReachBottom", "methods", "fetch", "pagesize"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAg0B,CAAgB,gyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCcp1B;EACAC;IACA;MACAC;MACAC;QAAAC;MAAA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IAEAC;MACA;QAAAC;MAAA,oDAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACnCA;AAAA;AAAA;AAAA;AAAmhD,CAAgB,o5CAAG,EAAC,C;;;;;;;;;;;ACAviD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/problem/problem.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/problem/problem.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./problem.vue?vue&type=template&id=dccca774&scoped=true&\"\nvar renderjs\nimport script from \"./problem.vue?vue&type=script&lang=js&\"\nexport * from \"./problem.vue?vue&type=script&lang=js&\"\nimport style0 from \"./problem.vue?vue&type=style&index=0&id=dccca774&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"dccca774\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/problem/problem.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./problem.vue?vue&type=template&id=dccca774&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./problem.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./problem.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"container\">\r\n\t\t\t<navigator class=\"xilu_problem\" :url=\"'/pages/problem_detail/problem_detail?id='+item.id\" hover-class=\"none\" v-for=\"(item,index) in qaList\" :key=\"index\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t{{item.name}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"text m-ellipsis-l3\"><rich-text :nodes=\"item.content\"></rich-text></view>\r\n\t\t\t</navigator>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tqaList:[],\n\t\t\t\tqaListMore:{page:1}\r\n\t\t\t};\r\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.fetch();\n\t\t},\n\t\tonReachBottom() {\n\t\t\tthis.fetch();\n\t\t},\n\t\tmethods:{\n\t\t\t\n\t\t\tfetch() {\n\t\t\t\tthis.$util.fetch(this, 'xilutour.article/question', {pagesize:10}, 'qaListMore', 'qaList', 'data', data=>{\n\t\t\t\t  \n\t\t\t\t})\n\t\t\t}\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.xilu {\r\n\t\t&_problem {\r\n\t\t\tmargin: 0 0 30rpx;\r\n\t\t\tpadding: 30rpx 30rpx 26rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tbox-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);\r\n\t\t\tborder-radius: 24rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\tmargin: 0 0 30rpx;\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #101010;\r\n\t\t\t\tline-height: 36rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.text {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #555555;\r\n\t\t\t\tline-height: 44rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.container {\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./problem.vue?vue&type=style&index=0&id=dccca774&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./problem.vue?vue&type=style&index=0&id=dccca774&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341238\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}