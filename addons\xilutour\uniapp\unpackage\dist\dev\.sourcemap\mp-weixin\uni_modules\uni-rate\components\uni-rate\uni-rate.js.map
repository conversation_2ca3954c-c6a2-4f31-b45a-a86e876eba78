{"version": 3, "sources": ["webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-rate/components/uni-rate/uni-rate.vue?abb3", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-rate/components/uni-rate/uni-rate.vue?d489", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-rate/components/uni-rate/uni-rate.vue?4518", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-rate/components/uni-rate/uni-rate.vue?e7c0", "uni-app:///uni_modules/uni-rate/components/uni-rate/uni-rate.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-rate/components/uni-rate/uni-rate.vue?68fc", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/uni_modules/uni-rate/components/uni-rate/uni-rate.vue?104e"], "names": ["name", "props", "isFill", "type", "default", "color", "activeColor", "disabledColor", "size", "value", "modelValue", "max", "margin", "disabled", "readonly", "allowHalf", "touchable", "data", "valueSync", "userMouseFristMove", "userRated", "userLastRate", "watch", "computed", "stars", "starList", "activeWitch", "marginNumber", "created", "mounted", "setTimeout", "methods", "touchstart", "e", "clientX", "screenX", "touchmove", "mousedown", "mousemove", "mouseleave", "_getRateCount", "index", "_onChange", "_getSize", "uni", "in", "select", "boundingClientRect", "exec"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACqN;AACrN,gBAAgB,gNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA+1B,CAAgB,iyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4Bn3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,eAmBA;EACAA;EACAC;IACAC;MACA;MACAC;MACAC;IACA;IACAC;MACA;MACAF;MACAC;IACA;IACAE;MACA;MACAH;MACAC;IACA;IACAG;MACA;MACAJ;MACAC;IACA;IACAI;MACA;MACAL;MACAC;IACA;IACAK;MACA;MACAN;MACAC;IACA;IACAM;MACA;MACAP;MACAC;IACA;IACAO;MACA;MACAR;MACAC;IACA;IACAQ;MACA;MACAT;MACAC;IACA;IACAS;MACA;MACAV;MACAC;IACA;IACAU;MACA;MACAX;MACAC;IACA;IACAW;MACA;MACAZ;MACAC;IACA;IACAY;MACA;MACAb;MACAC;IACA;EACA;EACAa;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAb;MACA;IACA;IACAC;MACA;IACA;EACA;EACAa;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;YACAC;UACA;QACA;UACAD;YACAC;UACA;QACA;UACAD;YACAC;UACA;QACA;MACA;MACA;IACA;IAEAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IAAA;IACAC;MACA;IACA;EAIA;EACAC;IACAC;MAIA;MACA,yBAGAC;QAFAC;QACAC;MAEA;MACA;IACA;IACAC;MAIA;MACA,0BAGAH;QAFAC;QACAC;MAEA;IACA;IAEA;AACA;AACA;IAEAE,kCAWA;IACAC,kCAeA;IACAC,oCAUA;IAgBA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;MACA;MACAC;MACAA;MACA;MACA;MACA;MACA;MACA;QACA;UACAhC;QACA;UACAA;QACA;MACA;QACAA;MACA;MAEAA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAiC;MAEA;MACA;MACA;QACAjC;MACA;IACA;IACA;AACA;AACA;IACAkC;MAAA;MAEAC,0BACAC,SACAC,oBACAC,qBACAC;QACA;UACA;QACA;MACA;IAUA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpUA;AAAA;AAAA;AAAA;AAAklD,CAAgB,45CAAG,EAAC,C;;;;;;;;;;;ACAtmD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-rate/components/uni-rate/uni-rate.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-rate.vue?vue&type=template&id=978a5ada&\"\nvar renderjs\nimport script from \"./uni-rate.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-rate.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-rate.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-rate/components/uni-rate/uni-rate.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-rate.vue?vue&type=template&id=978a5ada&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-rate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-rate.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view ref=\"uni-rate\" class=\"uni-rate\">\n\t\t\t<view class=\"uni-rate__icon\" :class=\"{'uni-cursor-not-allowed': disabled}\"\n\t\t\t\t:style=\"{ 'margin-right': marginNumber + 'px' }\" v-for=\"(star, index) in stars\" :key=\"index\"\n\t\t\t\*****************=\"touchstart\" @touchmove.stop=\"touchmove\" @mousedown.stop=\"mousedown\"\n\t\t\t\****************=\"mousemove\" @mouseleave=\"mouseleave\">\n\t\t\t\t<uni-icons :color=\"color\" :size=\"size\" :type=\"isFill ? 'star-filled' : 'star'\" />\n\t\t\t\t<!-- #ifdef APP-NVUE -->\n\t\t\t\t<view :style=\"{ width: star.activeWitch.replace('%','')*size/100+'px'}\" class=\"uni-rate__icon-on\">\n\t\t\t\t\t<uni-icons style=\"text-align: left;\" :color=\"disabled?'#ccc':activeColor\" :size=\"size\"\n\t\t\t\t\t\ttype=\"star-filled\" />\n\t\t\t\t</view>\n\t\t\t\t<!-- #endif -->\n\t\t\t\t<!-- #ifndef APP-NVUE -->\n\t\t\t\t<view :style=\"{ width: star.activeWitch}\" class=\"uni-rate__icon-on\">\n\t\t\t\t\t<uni-icons :color=\"disabled?disabledColor:activeColor\" :size=\"size\" type=\"star-filled\" />\n\t\t\t\t</view>\n\t\t\t\t<!-- #endif -->\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\t// #ifdef APP-NVUE\n\tconst dom = uni.requireNativePlugin('dom');\n\t// #endif\n\t/**\n\t * Rate 评分\n\t * @description 评分组件\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=33\n\t * @property {Boolean} \tisFill = [true|false] \t\t星星的类型，是否为实心类型, 默认为实心\n\t * @property {String} \tcolor \t\t\t\t\t\t未选中状态的星星颜色，默认为 \"#ececec\"\n\t * @property {String} \tactiveColor \t\t\t\t选中状态的星星颜色，默认为 \"#ffca3e\"\n\t * @property {String} \tdisabledColor \t\t\t\t禁用状态的星星颜色，默认为 \"#c0c0c0\"\n\t * @property {Number} \tsize \t\t\t\t\t\t星星的大小\n\t * @property {Number} \tvalue/v-model \t\t\t\t当前评分\n\t * @property {Number} \tmax \t\t\t\t\t\t最大评分评分数量，目前一分一颗星\n\t * @property {Number} \tmargin \t\t\t\t\t\t星星的间距，单位 px\n\t * @property {Boolean} \tdisabled = [true|false] \t是否为禁用状态，默认为 false\n\t * @property {Boolean} \treadonly = [true|false] \t是否为只读状态，默认为 false\n\t * @property {Boolean} \tallowHalf = [true|false] \t是否实现半星，默认为 false\n\t * @property {Boolean} \ttouchable = [true|false] \t是否支持滑动手势，默认为 true\n\t * @event {Function} change \t\t\t\t\t\tuniRate 的 value 改变时触发事件，e={value:Number}\n\t */\n\n\texport default {\n\t\tname: \"UniRate\",\n\t\tprops: {\n\t\t\tisFill: {\n\t\t\t\t// 星星的类型，是否镂空\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\tcolor: {\n\t\t\t\t// 星星未选中的颜色\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#ececec\"\n\t\t\t},\n\t\t\tactiveColor: {\n\t\t\t\t// 星星选中状态颜色\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#ffca3e\"\n\t\t\t},\n\t\t\tdisabledColor: {\n\t\t\t\t// 星星禁用状态颜色\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#c0c0c0\"\n\t\t\t},\n\t\t\tsize: {\n\t\t\t\t// 星星的大小\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 24\n\t\t\t},\n\t\t\tvalue: {\n\t\t\t\t// 当前评分\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tmodelValue: {\n\t\t\t\t// 当前评分\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tmax: {\n\t\t\t\t// 最大评分\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 5\n\t\t\t},\n\t\t\tmargin: {\n\t\t\t\t// 星星的间距\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tdisabled: {\n\t\t\t\t// 是否可点击\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\treadonly: {\n\t\t\t\t// 是否只读\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\tallowHalf: {\n\t\t\t\t// 是否显示半星\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\ttouchable: {\n\t\t\t\t// 是否支持滑动手势\n\t\t\t\ttype: [Boolean, String],\n\t\t\t\tdefault: true\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tvalueSync: \"\",\n\t\t\t\tuserMouseFristMove: true,\n\t\t\t\tuserRated: false,\n\t\t\t\tuserLastRate: 1\n\t\t\t};\n\t\t},\n\t\twatch: {\n\t\t\tvalue(newVal) {\n\t\t\t\tthis.valueSync = Number(newVal);\n\t\t\t},\n\t\t\tmodelValue(newVal) {\n\t\t\t\tthis.valueSync = Number(newVal);\n\t\t\t},\n\t\t},\n\t\tcomputed: {\n\t\t\tstars() {\n\t\t\t\tconst value = this.valueSync ? this.valueSync : 0;\n\t\t\t\tconst starList = [];\n\t\t\t\tconst floorValue = Math.floor(value);\n\t\t\t\tconst ceilValue = Math.ceil(value);\n\t\t\t\tfor (let i = 0; i < this.max; i++) {\n\t\t\t\t\tif (floorValue > i) {\n\t\t\t\t\t\tstarList.push({\n\t\t\t\t\t\t\tactiveWitch: \"100%\"\n\t\t\t\t\t\t});\n\t\t\t\t\t} else if (ceilValue - 1 === i) {\n\t\t\t\t\t\tstarList.push({\n\t\t\t\t\t\t\tactiveWitch: (value - floorValue) * 100 + \"%\"\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstarList.push({\n\t\t\t\t\t\t\tactiveWitch: \"0\"\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn starList;\n\t\t\t},\n\n\t\t\tmarginNumber() {\n\t\t\t\treturn Number(this.margin)\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.valueSync = Number(this.value || this.modelValue);\n\t\t\tthis._rateBoxLeft = 0\n\t\t\tthis._oldValue = null\n\t\t},\n\t\tmounted() {\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis._getSize()\n\t\t\t}, 100)\n\t\t\t// #ifdef H5\n\t\t\tthis.PC = this.IsPC()\n\t\t\t// #endif\n\t\t},\n\t\tmethods: {\n\t\t\ttouchstart(e) {\n\t\t\t\t// #ifdef H5\n\t\t\t\tif (this.IsPC()) return\n\t\t\t\t// #endif\n\t\t\t\tif (this.readonly || this.disabled) return\n\t\t\t\tconst {\n\t\t\t\t\tclientX,\n\t\t\t\t\tscreenX\n\t\t\t\t} = e.changedTouches[0]\n\t\t\t\t// TODO 做一下兼容，只有 Nvue 下才有 screenX，其他平台式 clientX\n\t\t\t\tthis._getRateCount(clientX || screenX)\n\t\t\t},\n\t\t\ttouchmove(e) {\n\t\t\t\t// #ifdef H5\n\t\t\t\tif (this.IsPC()) return\n\t\t\t\t// #endif\n\t\t\t\tif (this.readonly || this.disabled || !this.touchable) return\n\t\t\t\tconst {\n\t\t\t\t\tclientX,\n\t\t\t\t\tscreenX\n\t\t\t\t} = e.changedTouches[0]\n\t\t\t\tthis._getRateCount(clientX || screenX)\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 兼容 PC @tian\n\t\t\t */\n\n\t\t\tmousedown(e) {\n\t\t\t\t// #ifdef H5\n\t\t\t\tif (!this.IsPC()) return\n\t\t\t\tif (this.readonly || this.disabled) return\n\t\t\t\tconst {\n\t\t\t\t\tclientX,\n\t\t\t\t} = e\n\t\t\t\tthis.userLastRate = this.valueSync\n\t\t\t\tthis._getRateCount(clientX)\n\t\t\t\tthis.userRated = true\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tmousemove(e) {\n\t\t\t\t// #ifdef H5\n\t\t\t\tif (!this.IsPC()) return\n\t\t\t\tif (this.userRated) return\n\t\t\t\tif (this.userMouseFristMove) {\n\t\t\t\t\tconsole.log('---mousemove----', this.valueSync);\n\t\t\t\t\tthis.userLastRate = this.valueSync\n\t\t\t\t\tthis.userMouseFristMove = false\n\t\t\t\t}\n\t\t\t\tif (this.readonly || this.disabled || !this.touchable) return\n\t\t\t\tconst {\n\t\t\t\t\tclientX,\n\t\t\t\t} = e\n\t\t\t\tthis._getRateCount(clientX)\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tmouseleave(e) {\n\t\t\t\t// #ifdef H5\n\t\t\t\tif (!this.IsPC()) return\n\t\t\t\tif (this.readonly || this.disabled || !this.touchable) return\n\t\t\t\tif (this.userRated) {\n\t\t\t\t\tthis.userRated = false\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis.valueSync = this.userLastRate\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// #ifdef H5\n\t\t\tIsPC() {\n\t\t\t\tvar userAgentInfo = navigator.userAgent;\n\t\t\t\tvar Agents = [\"Android\", \"iPhone\", \"SymbianOS\", \"Windows Phone\", \"iPad\", \"iPod\"];\n\t\t\t\tvar flag = true;\n\t\t\t\tfor (let v = 0; v < Agents.length - 1; v++) {\n\t\t\t\t\tif (userAgentInfo.indexOf(Agents[v]) > 0) {\n\t\t\t\t\t\tflag = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn flag;\n\t\t\t},\n\t\t\t// #endif\n\n\t\t\t/**\n\t\t\t * 获取星星个数\n\t\t\t */\n\t\t\t_getRateCount(clientX) {\n\t\t\t\tthis._getSize()\n\t\t\t\tconst size = Number(this.size)\n\t\t\t\tif (isNaN(size)) {\n\t\t\t\t\treturn new Error('size 属性只能设置为数字')\n\t\t\t\t}\n\t\t\t\tconst rateMoveRange = clientX - this._rateBoxLeft\n\t\t\t\tlet index = parseInt(rateMoveRange / (size + this.marginNumber))\n\t\t\t\tindex = index < 0 ? 0 : index;\n\t\t\t\tindex = index > this.max ? this.max : index;\n\t\t\t\tconst range = parseInt(rateMoveRange - (size + this.marginNumber) * index);\n\t\t\t\tlet value = 0;\n\t\t\t\tif (this._oldValue === index && !this.PC) return;\n\t\t\t\tthis._oldValue = index;\n\t\t\t\tif (this.allowHalf) {\n\t\t\t\t\tif (range > (size / 2)) {\n\t\t\t\t\t\tvalue = index + 1\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvalue = index + 0.5\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tvalue = index + 1\n\t\t\t\t}\n\n\t\t\t\tvalue = Math.max(0.5, Math.min(value, this.max))\n\t\t\t\tthis.valueSync = value\n\t\t\t\tthis._onChange()\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 触发动态修改\n\t\t\t */\n\t\t\t_onChange() {\n\n\t\t\t\tthis.$emit(\"input\", this.valueSync);\n\t\t\t\tthis.$emit(\"update:modelValue\", this.valueSync);\n\t\t\t\tthis.$emit(\"change\", {\n\t\t\t\t\tvalue: this.valueSync\n\t\t\t\t});\n\t\t\t},\n\t\t\t/**\n\t\t\t * 获取星星距离屏幕左侧距离\n\t\t\t */\n\t\t\t_getSize() {\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tuni.createSelectorQuery()\n\t\t\t\t\t.in(this)\n\t\t\t\t\t.select('.uni-rate')\n\t\t\t\t\t.boundingClientRect()\n\t\t\t\t\t.exec(ret => {\n\t\t\t\t\t\tif (ret) {\n\t\t\t\t\t\t\tthis._rateBoxLeft = ret[0].left\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tdom.getComponentRect(this.$refs['uni-rate'], (ret) => {\n\t\t\t\t\tconst size = ret.size\n\t\t\t\t\tif (size) {\n\t\t\t\t\t\tthis._rateBoxLeft = size.left\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\">\n\t.uni-rate {\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\tline-height: 1;\n\t\tfont-size: 0;\n\t\tflex-direction: row;\n\t\t/* #ifdef H5 */\n\t\tcursor: pointer;\n\t\t/* #endif */\n\t}\n\n\t.uni-rate__icon {\n\t\tposition: relative;\n\t\tline-height: 1;\n\t\tfont-size: 0;\n\t}\n\n\t.uni-rate__icon-on {\n\t\toverflow: hidden;\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tline-height: 1;\n\t\ttext-align: left;\n\t}\n\n\t.uni-cursor-not-allowed {\n\t\t/* #ifdef H5 */\n\t\tcursor: not-allowed !important;\n\t\t/* #endif */\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-rate.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-rate.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494342509\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}