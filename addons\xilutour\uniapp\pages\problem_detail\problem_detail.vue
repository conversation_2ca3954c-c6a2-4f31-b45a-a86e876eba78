<template>
	<view class="xilu">
		<view class="container">
			<view class="title">{{qa.name}}</view>
			<view class="flex-box pb40 mb40 m-hairline--bottom fs26 col-5">
				<view class="mr30 pr40">{{qa.view_num}}人已阅读</view>
				<view>{{qa.createtime_text}}</view>
			</view>
			<view class="mb30"><rich-text :nodes="qa.content"></rich-text></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				qaId:0,
				qa:{name: '',view_num: 0, content:'',createtime_text:''}
			};
		},
		onLoad(options) {
			this.qaId = options.id;
			this.fetchDetail()
		},
		methods:{
			fetchDetail(){
				this.$core.get({url: 'xilutour.article/detail',data: {article_id:this.qaId},loading: false,success: ret => {
					ret.data.content = this.$core.richTextnew(ret.data.content);
					this.qa = ret.data;
					},fail: err => {
						console.log(err);
					}
				});
			}
		}
	}
</script>

<style lang="less" scoped>
.xilu {
	.container{
		padding: 30rpx;
		font-size: 30rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		color: #333333;
		line-height: 44rpx;
	}
	.title{
		margin: 0 0 40rpx;
		font-size: 40rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		color: #101010;
		line-height: 42rpx;
	}
}
</style>
