<template>
	<view class="xilu">
		<view class="page-foot ptb15">
			<view class="g_order_foot1 flex-box">
				<view class="flex-1">
					<view class="col-price">
						<text class="fs30">¥</text>
						<text class="fs40">{{order.pay_price}}</text>
					</view>
					<view class="fs30 col-89 pl25">共计</view>
				</view>
				<button class="btn1" :loading="loading" :disabled="loading" @click="createOrder()">立即支付</button>
			</view>
		</view>
		<view class="container">
			<view class="xilu_travel flex-box mb30">
				<image :src="order.tour.thumb_image_text" mode="aspectFill" class="img mr30"></image>
				<view class="flex-1">
					<view class="fs36 col-10 mb20">{{order.tour.name}}</view>
					<view class="fs30 col-5 mb20">{{order.tour.sub_name}}｜{{order.tour.team_count}}人旅行团</view>
					<view class="col-price">
						<text class="fs30">¥</text>
						<text class="fs40">{{order.tour_date.salesprice}}</text>
					</view>
				</view>
			</view>

			<view class="xilu_travel_time flex-box">
				<view class="flex-1">
					<view class="fs24 col-89 mb15">出发日期</view>
					<view>
						<text class="fs36 col-10 mr15">{{order.tour_date.appoint_date_text}}</text>
						<text class="fs30 col-5">{{order.tour_date.appoint_date_week}}</text>
					</view>
				</view>
				<view class="line"></view>
				<view class="flex-1">
					<view class="fs24 col-89 mb15">结束日期</view>
					<view>
						<text class="fs36 col-10 mr15">{{order.tour_date.appoint_end_date_text}}</text>
						<text class="fs30 col-5">{{order.tour_date.appoint_end_date_week}}</text>
					</view>
				</view>
				<view class="fs30 col-normal">{{order.tour.series_days}}天</view>
			</view>

			<view class="xilu_title mt50 mb30">联系人信息</view>

			<view class="g_input_box flex-box mb30">
				<view class="fs30 col-5">姓名</view>
				<input class="flex-1 tr fs30 col-10" v-model="tour.contact_name" type="text" placeholder="请输入姓名" placeholder-class="col-10" />
			</view>
			<view class="g_input_box flex-box">
				<view class="fs30 col-5">手机号码</view>
				<input class="flex-1 tr fs30 col-10" v-model="tour.contact_mobile" maxlength="11" type="number" placeholder="请输入手机号码" placeholder-class="col-10" />
			</view>

			<view class="flex-box mt50">
				<view class="flex-1 xilu_title">出行人信息</view>
				<view class="fs24 col-5 m-arrow-right" @click="bindTraveler()">添加{{tour.buy_adult_count}}个成人{{tour.buy_child_count>0?'和'+tour.buy_child_count+'个儿童信息':''}}</view>
			</view>

			<view>
				<view class="xilu_traveler flex-box" v-for="(item,index) in travelerList" :key="index">
					<view class="flex-box flex-1">
						<view :class="item.adult_type == 1?'identity1':'identity2'">{{item.adult_type_text}}</view>
						<view class="fs30 col-10 mlr20">{{item.username}}</view>
						<image v-if="item.gender==1" src="../../static/icon/icon_gender1.png" mode="aspectFit"></image>
						<image v-else src="../../static/icon/icon_gender2.png" mode="aspectFit"></image>
					</view>
					<view class="btn_del" @click="bindDel(index)">
						<image src="../../static/icon/icon_del.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>

			<view class="xilu_title mt50 mb30">订单信息</view>

			<view class="g_order_info">
				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">支付方式</view>
					<image src="/static/icon/icon_wx.png" mode="aspectFit" class="g-icon30 mr15"></image>
					<view class="fs30 col-10">微信支付</view>
				</view>

				<view class="flex-box mb50">
					<view class="fs30 col-5 flex-1">商品金额</view>
					<view class="col-10">
						<text class="fs30">¥</text>
						<text class="fs40">{{order.total_price}}</text>
					</view>
				</view>

				<view class="flex-box mb50" v-if="order.coupon_list.length>0" @click="couponPopOpen">
					<view class="fs30 col-5 flex-1">优惠劵</view>
					<view class="col-10 m-arrow-right">
						<text class="fs24">-¥</text>
						<text class="fs34">{{order.coupon_price}}</text>
					</view>
				</view>

				<view class="flex-box flex-end">
					<view class="fs30 col-89 mr20">共计</view>
					<view class="col-price">
						<text class="fs30">¥</text>
						<text class="fs40">{{order.pay_price}}</text>
					</view>
				</view>

			</view>
			
			
			<uni-popup ref="couponPopup" type="bottom">
				<view class="g_coupon_pop">
					<view class="fs30 col-10 tc mb30">优惠券</view>
					<image src="/static/icon/icon_close.png" mode="aspectFit" class="icon_close" @click="couponPopClose"></image>
			
					<view class="pop_coupon_wrap">
						<view class="pop_coupon" v-for="(coupon,index) in order.coupon_list" :key="index">
							<image src="/static/icon/icon_coupon_bg1.png" mode="aspectFill" class="bg"></image>
							<view class="inner flex-box">
								<view class="left">
									<view class="fwb mb20">
										<text class="fs24">¥</text>
										<text class="fs50">{{coupon.money}}</text>
									</view>
									<view class="man">满{{coupon.at_least}}可用</view>
								</view>
								<view class="right flex-1 flex-box">
									<view class="flex-1">
										<view class="fs30 mb20">{{coupon.name}}</view>
										<view class="fs24">{{coupon.use_end_time_text}}到期</view>
									</view>
									<view class="use active" v-if="coupon.checked">
										<image src="/static/icon/icon_coupon_check.png" mode="aspectFit"></image>
									</view>
									<view class="use" @click="bindChooseCoupon(index)" v-else>选择</view>
									
									
								</view>
							</view>
						</view>
			
					</view>
			
			
					<view class="g-btn1" style="width: 670rpx;margin: 30rpx auto 0;">确定</view>
				</view>
			</uni-popup>

		</view>
	</view>
</template>

<script>
	var validate = require("../../xilu/validate.js");
	export default {
		data() {
			return {
				tour:{
					coupon_id:0,
					tour_id:0,
					buy_adult_count:0,
					buy_child_count:0,
					tour_date_id:0,
					contact_name:'',
					contact_mobile:''
				},
				order:{
					coupon_price:0,
					coupon_list:[],
					pay_price:0,
					total_price:0,
					tour:{
						name:'',
						sub_name:'',
						thumb_image_text:''
					},
					tour_date:{
						appoint_date_text: "",
						appoint_date_week: "",
						appoint_end_date_text: "",
						appoint_end_date_week: "",
						id: 0,
						originprice: 0,
						salesprice: 0,
						set_count: 0,
						status: "normal",
						tour_id: 0
					},
				},
				loading: false,
				travelerList:[],
			};

		},
		onLoad() {
			let page = this;
			this.getOpenerEventChannel().on("tourTransfor",function(tour){
				tour.coupon_id = 0;
				tour.contact_name = '';
				tour.contact_mobile = '';
				page.tour = tour;
				page.preOrder();
			})
		},
		methods:{
			preOrder() {
				this.$core.post({url: 'xilutour.tour_order/pre_order',data: this.tour,success: ret => {
						this.order = ret.data;
						if(ret.data.coupon) this.tour.coupon_id = ret.data.coupon.id;
					},fail: err => {
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel:false,
							success(res) {
								if(res.confirm){
									uni.navigateBack({});
								}
							}
						})
						return false;
					}
				});
			},
			//下单
			createOrder(){
				let order = this.tour;
				let travelerList = this.travelerList;
				if(travelerList.length<=0 || travelerList.length != (order.buy_adult_count+order.buy_child_count)){
					uni.showToast({
					    title: "请选择出行人信息",
					    icon: 'none'
					});
					return;
				}
				let travelerIds = [];
				travelerList.forEach(item=>travelerIds.push(item.id));
				order.traveler_ids = travelerIds.join(',');
				var rule = [
					{name: 'contact_name',nameChn: '姓名',rules: ['require'],errorMsg: {require: '请填写姓名'}},
					{name: 'contact_mobile',nameChn: '手机号',rules: ['require','length:11'],errorMsg: {require: '请填写手机号',length:"手机号错误"}},
					{name: 'tour_id',nameChn: '线路',rules: ['gt:0'],errorMsg: {gt: '线路错误'}},
					{name: 'tour_date_id',nameChn: '线路日期',rules: ['gt:0'],errorMsg: {gt: '线路日期错误'}},
				];
				// 是否全部通过，返回Boolean
				if (!validate.check(order, rule)) {
				    uni.showToast({
				        title: validate.getError()[0],
				        icon: 'none'
				    });
				    return;
				}
				this.loading = true;
				this.$core.post({url: 'xilutour.tour_order/create_order',data: order,loading: true,success: ret => {
					this.loading = false;
					//下单成功，前往收银台
					this.payment(ret.data);
					},
					fail: err => {
						this.loading = false;
						uni.showModal({
							title:'提示',
							content: err.msg,
							showCancel: err.code==3?true:false,
							success(res) {
								if(res.confirm){
									if(err.code==3){
										uni.redirectTo({
											url: '/pages/travel_order/travel_order'
										})
									}
								}
							}
						})
						return false;
					}
				});
			},
			payment(order){
				//#ifdef MP-WEIXIN
				this.$core.post({url:'xilutour.pay/pay',data:{pay_type:1,order_id:order.id,platform:'wxmini'},success:(ret)=>{
					let wxconfig =  ret.data;
					this.$core.payment(wxconfig,function(){
						uni.redirectTo({
							url: '/pages/travel_order/travel_order'
						})
					})
				}});
				//#endif
			},
			//添加出行人
			bindTraveler(){
				let tour = this.tour;
				uni.navigateTo({
					url: '/pages/choose_traveler/choose_traveler',
					events:{
						chooseSuccess: data=>{
							this.travelerList = data;
							this.$forceUpdate();
						},
					},
					success(res) {
						res.eventChannel.emit("travelTransfor",tour)
					}
				})
			},
			//删除
			bindDel(index){
				let travelerList = this.travelerList;
				travelerList.splice(index,1);
				this.travelerList = travelerList;
			},
			// 打开优惠券弹窗
			couponPopOpen() {
				this.$refs.couponPopup.open();
			},
			// 关闭优惠券弹窗
			couponPopClose() {
				this.$refs.couponPopup.close();
			},
			//选择优惠券
			bindChooseCoupon(index){
				this.tour.coupon_id = this.order.coupon_list[index].id;
				this.preOrder();
				this.couponPopClose();
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		&_travel {
			.img {
				display: block;
				width: 180rpx;
				height: 180rpx;
				border-radius: 15rpx;
			}
		}

		&_travel_time {
			padding: 0 40rpx 0 30rpx;
			height: 120rpx;
			background: #F7F9FB;
			border-radius: 20rpx;

			.line {
				margin: 0 60rpx;
				width: 25rpx;
				height: 2rpx;
				background-color: var(--normal);
			}
		}

		&_title {
			font-size: 36rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: bold;
			color: #101010;
			line-height: 40rpx;
		}

		&_traveler {
			margin: 30rpx 0 0;
			padding: 0 0 0 30rpx;
			width: 670rpx;
			height: 110rpx;
			background: #F7F9FB;
			border-radius: 30rpx;

			.identity1 {
				width: 60rpx;
				height: 36rpx;
				background: #FFAB29;
				border-radius: 5rpx;
				font-size: 24rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 36rpx;
				text-align: center;
			}

			.identity2 {
				width: 60rpx;
				height: 36rpx;
				background: var(--normal);
				border-radius: 5rpx;
				font-size: 24rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 36rpx;
				text-align: center;
			}

			.btn_del {
				padding: 40rpx 0 0 20rpx;
				width: 70rpx;
				height: 110rpx;
				background: var(--normal);
				border-radius: 0 30rpx 30rpx 0;
			}

			image {
				display: block;
				width: 30rpx;
				height: 30rpx;
			}
		}

		.container {
			padding: 30rpx 40rpx 200rpx !important;
		}
	}
</style>