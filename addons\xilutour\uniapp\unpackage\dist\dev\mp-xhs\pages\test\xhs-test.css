
.container.data-v-d5a5ad60 {
	padding: 20px;
	background-color: #f5f5f5;
	min-height: 100vh;
}
.header.data-v-d5a5ad60 {
	text-align: center;
	margin-bottom: 30px;
}
.title.data-v-d5a5ad60 {
	font-size: 24px;
	font-weight: bold;
	color: #333;
}
.test-section.data-v-d5a5ad60 {
	background-color: white;
	border-radius: 10px;
	padding: 20px;
	margin-bottom: 20px;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
.section-title.data-v-d5a5ad60 {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 15px;
}
.test-btn.data-v-d5a5ad60 {
	background-color: #ff2442;
	color: white;
	border: none;
	border-radius: 5px;
	padding: 10px 20px;
	font-size: 16px;
	margin-bottom: 15px;
}
.clear-btn.data-v-d5a5ad60 {
	background-color: #999;
	color: white;
	border: none;
	border-radius: 5px;
	padding: 8px 16px;
	font-size: 14px;
}
.user-info.data-v-d5a5ad60 {
	background-color: #f9f9f9;
	border-radius: 5px;
	padding: 15px;
}
.user-info text.data-v-d5a5ad60 {
	display: block;
	margin-bottom: 10px;
	color: #666;
}
.avatar.data-v-d5a5ad60 {
	width: 60px;
	height: 60px;
	border-radius: 30px;
	margin-top: 10px;
}
.login-result.data-v-d5a5ad60 {
	background-color: #f0f8ff;
	border-radius: 5px;
	padding: 15px;
}
.login-result text.data-v-d5a5ad60 {
	display: block;
	margin-bottom: 8px;
	color: #333;
}
.debug-info.data-v-d5a5ad60 {
	background-color: #f5f5f5;
	border-radius: 5px;
	padding: 15px;
	max-height: 300px;
	overflow-y: auto;
	margin-bottom: 10px;
}
.debug-log.data-v-d5a5ad60 {
	display: block;
	font-size: 12px;
	color: #666;
	margin-bottom: 5px;
	word-break: break-all;
}

