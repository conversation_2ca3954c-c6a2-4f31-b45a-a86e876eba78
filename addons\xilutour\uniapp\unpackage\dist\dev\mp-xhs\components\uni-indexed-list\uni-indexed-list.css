@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-indexed-list.data-v-acb53438 {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: row;
}
.uni-indexed-list__scroll.data-v-acb53438 {
  flex: 1;
}
.uni-indexed-list__menu.data-v-acb53438 {
  width: 24px;
  display: flex;
  flex-direction: column;
}
.uni-indexed-list__menu-item.data-v-acb53438 {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
}
.uni-indexed-list__menu-text.data-v-acb53438 {
  font-size: 12px;
  text-align: center;
  color: #555;
}
.uni-indexed-list__menu-text--active.data-v-acb53438 {
  border-radius: 16px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  background-color: #FFC100;
  color: #fff;
}
.uni-indexed-list__alert-wrapper.data-v-acb53438 {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.uni-indexed-list__alert.data-v-acb53438 {
  width: 80px;
  height: 80px;
  border-radius: 80px;
  text-align: center;
  line-height: 80px;
  font-size: 35px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
}
