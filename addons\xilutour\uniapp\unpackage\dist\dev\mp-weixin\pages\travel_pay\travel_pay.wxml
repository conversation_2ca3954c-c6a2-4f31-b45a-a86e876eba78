<view class="xilu data-v-59cce134"><view class="page-foot ptb15 data-v-59cce134"><view class="g_order_foot1 flex-box data-v-59cce134"><view class="flex-1 data-v-59cce134"><view class="col-price data-v-59cce134"><text class="fs30 data-v-59cce134">¥</text><text class="fs40 data-v-59cce134">{{order.pay_price}}</text></view><view class="fs30 col-89 pl25 data-v-59cce134">共计</view></view><button class="btn1 data-v-59cce134" loading="{{loading}}" disabled="{{loading}}" data-event-opts="{{[['tap',[['createOrder']]]]}}" bindtap="__e">立即支付</button></view></view><view class="container data-v-59cce134"><view class="xilu_travel flex-box mb30 data-v-59cce134"><image class="img mr30 data-v-59cce134" src="{{order.tour.thumb_image_text}}" mode="aspectFill"></image><view class="flex-1 data-v-59cce134"><view class="fs36 col-10 mb20 data-v-59cce134">{{order.tour.name}}</view><view class="fs30 col-5 mb20 data-v-59cce134">{{order.tour.sub_name+"｜"+order.tour.team_count+"人旅行团"}}</view><view class="col-price data-v-59cce134"><text class="fs30 data-v-59cce134">¥</text><text class="fs40 data-v-59cce134">{{order.tour_date.salesprice}}</text></view></view></view><view class="xilu_travel_time flex-box data-v-59cce134"><view class="flex-1 data-v-59cce134"><view class="fs24 col-89 mb15 data-v-59cce134">出发日期</view><view class="data-v-59cce134"><text class="fs36 col-10 mr15 data-v-59cce134">{{order.tour_date.appoint_date_text}}</text><text class="fs30 col-5 data-v-59cce134">{{order.tour_date.appoint_date_week}}</text></view></view><view class="line data-v-59cce134"></view><view class="flex-1 data-v-59cce134"><view class="fs24 col-89 mb15 data-v-59cce134">结束日期</view><view class="data-v-59cce134"><text class="fs36 col-10 mr15 data-v-59cce134">{{order.tour_date.appoint_end_date_text}}</text><text class="fs30 col-5 data-v-59cce134">{{order.tour_date.appoint_end_date_week}}</text></view></view><view class="fs30 col-normal data-v-59cce134">{{order.tour.series_days+"天"}}</view></view><view class="xilu_title mt50 mb30 data-v-59cce134">联系人信息</view><view class="g_input_box flex-box mb30 data-v-59cce134"><view class="fs30 col-5 data-v-59cce134">姓名</view><input class="flex-1 tr fs30 col-10 data-v-59cce134" type="text" placeholder="请输入姓名" placeholder-class="col-10" data-event-opts="{{[['input',[['__set_model',['$0','contact_name','$event',[]],['tour']]]]]}}" value="{{tour.contact_name}}" bindinput="__e"/></view><view class="g_input_box flex-box data-v-59cce134"><view class="fs30 col-5 data-v-59cce134">手机号码</view><input class="flex-1 tr fs30 col-10 data-v-59cce134" maxlength="11" type="number" placeholder="请输入手机号码" placeholder-class="col-10" data-event-opts="{{[['input',[['__set_model',['$0','contact_mobile','$event',[]],['tour']]]]]}}" value="{{tour.contact_mobile}}" bindinput="__e"/></view><view class="flex-box mt50 data-v-59cce134"><view class="flex-1 xilu_title data-v-59cce134">出行人信息</view><view data-event-opts="{{[['tap',[['bindTraveler']]]]}}" class="fs24 col-5 m-arrow-right data-v-59cce134" bindtap="__e">{{"添加"+tour.buy_adult_count+"个成人"+(tour.buy_child_count>0?'和'+tour.buy_child_count+'个儿童信息':'')}}</view></view><view class="data-v-59cce134"><block wx:for="{{travelerList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="xilu_traveler flex-box data-v-59cce134"><view class="flex-box flex-1 data-v-59cce134"><view class="{{['data-v-59cce134',item.adult_type==1?'identity1':'identity2']}}">{{item.adult_type_text}}</view><view class="fs30 col-10 mlr20 data-v-59cce134">{{item.username}}</view><block wx:if="{{item.gender==1}}"><image src="../../static/icon/icon_gender1.png" mode="aspectFit" class="data-v-59cce134"></image></block><block wx:else><image src="../../static/icon/icon_gender2.png" mode="aspectFit" class="data-v-59cce134"></image></block></view><view data-event-opts="{{[['tap',[['bindDel',[index]]]]]}}" class="btn_del data-v-59cce134" bindtap="__e"><image src="../../static/icon/icon_del.png" mode="aspectFit" class="data-v-59cce134"></image></view></view></block></view><view class="xilu_title mt50 mb30 data-v-59cce134">订单信息</view><view class="g_order_info data-v-59cce134"><view class="flex-box mb50 data-v-59cce134"><view class="fs30 col-5 flex-1 data-v-59cce134">支付方式</view><image class="g-icon30 mr15 data-v-59cce134" src="/static/icon/icon_wx.png" mode="aspectFit"></image><view class="fs30 col-10 data-v-59cce134">微信支付</view></view><view class="flex-box mb50 data-v-59cce134"><view class="fs30 col-5 flex-1 data-v-59cce134">商品金额</view><view class="col-10 data-v-59cce134"><text class="fs30 data-v-59cce134">¥</text><text class="fs40 data-v-59cce134">{{order.total_price}}</text></view></view><block wx:if="{{$root.g0>0}}"><view data-event-opts="{{[['tap',[['couponPopOpen',['$event']]]]]}}" class="flex-box mb50 data-v-59cce134" bindtap="__e"><view class="fs30 col-5 flex-1 data-v-59cce134">优惠劵</view><view class="col-10 m-arrow-right data-v-59cce134"><text class="fs24 data-v-59cce134">-¥</text><text class="fs34 data-v-59cce134">{{order.coupon_price}}</text></view></view></block><view class="flex-box flex-end data-v-59cce134"><view class="fs30 col-89 mr20 data-v-59cce134">共计</view><view class="col-price data-v-59cce134"><text class="fs30 data-v-59cce134">¥</text><text class="fs40 data-v-59cce134">{{order.pay_price}}</text></view></view></view><uni-popup vue-id="dfce4340-1" type="bottom" data-ref="couponPopup" class="data-v-59cce134 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="g_coupon_pop data-v-59cce134"><view class="fs30 col-10 tc mb30 data-v-59cce134">优惠券</view><image class="icon_close data-v-59cce134" src="/static/icon/icon_close.png" mode="aspectFit" data-event-opts="{{[['tap',[['couponPopClose',['$event']]]]]}}" bindtap="__e"></image><view class="pop_coupon_wrap data-v-59cce134"><block wx:for="{{order.coupon_list}}" wx:for-item="coupon" wx:for-index="index" wx:key="index"><view class="pop_coupon data-v-59cce134"><image class="bg data-v-59cce134" src="/static/icon/icon_coupon_bg1.png" mode="aspectFill"></image><view class="inner flex-box data-v-59cce134"><view class="left data-v-59cce134"><view class="fwb mb20 data-v-59cce134"><text class="fs24 data-v-59cce134">¥</text><text class="fs50 data-v-59cce134">{{coupon.money}}</text></view><view class="man data-v-59cce134">{{"满"+coupon.at_least+"可用"}}</view></view><view class="right flex-1 flex-box data-v-59cce134"><view class="flex-1 data-v-59cce134"><view class="fs30 mb20 data-v-59cce134">{{coupon.name}}</view><view class="fs24 data-v-59cce134">{{coupon.use_end_time_text+"到期"}}</view></view><block wx:if="{{coupon.checked}}"><view class="use active data-v-59cce134"><image src="/static/icon/icon_coupon_check.png" mode="aspectFit" class="data-v-59cce134"></image></view></block><block wx:else><view data-event-opts="{{[['tap',[['bindChooseCoupon',[index]]]]]}}" class="use data-v-59cce134" bindtap="__e">选择</view></block></view></view></view></block></view><view class="g-btn1 data-v-59cce134" style="width:670rpx;margin:30rpx auto 0;">确定</view></view></uni-popup></view></view>