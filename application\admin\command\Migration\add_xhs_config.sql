-- 添加小红书配置项的迁移脚本
-- 执行时间: 2025-08-01

-- 检查是否已存在小红书配置，如果不存在则添加
INSERT IGNORE INTO `fa_xilutour_config` (`name`, `group`, `title`, `tip`, `type`, `visible`, `value`, `content`, `rule`, `extend`, `setting`) 
VALUES ('xhs', 'platform', '小红书配置', '小红书小程序登录配置', 'array', '', '{"xhs_appid":"","xhs_appsecret":"","xhs_status":"0"}', NULL, '', '', '');

-- 更新配置表，确保小红书配置项存在
UPDATE `fa_xilutour_config` SET 
    `title` = '小红书配置',
    `tip` = '小红书小程序登录配置',
    `type` = 'array',
    `value` = '{"xhs_appid":"","xhs_appsecret":"","xhs_status":"0"}'
WHERE `name` = 'xhs' AND `group` = 'platform';
