<view class="xilu data-v-57280228"><view class="container data-v-57280228" style="padding-bottom:30rpx;"><image class="m-backdrop data-v-57280228" src="/static/icon/icon_bg1.png" mode="widthFix"></image><view class="m-header data-v-57280228"><image class="m-backdrop data-v-57280228" src="/static/icon/icon_bg1.png" mode="widthFix"></image><view class="g-custom-nav flex-box plr30 data-v-57280228" style="{{'padding-top:'+(statusBarHeight+'px')+';'+('height:'+('calc(90rpx + '+statusBarHeight+'px)')+';')}}"><view data-event-opts="{{[['tap',[['bindMessage']]]]}}" class="{{['message','data-v-57280228',(messageCount>0)?'active':'']}}" bindtap="__e"><image src="/static/icon/icon_message.png" mode="aspectFit" class="data-v-57280228"></image><view class="data-v-57280228">消息</view></view><view class="search_box flex-box data-v-57280228"><view data-event-opts="{{[['tap',[['bindCityChange']]]]}}" class="addr m-ellipsis data-v-57280228" bindtap="__e">{{currentCity?currentCity.name:''}}</view><image class="icon_arrow data-v-57280228" src="/static/icon/icon_arrow.png"></image><view class="line data-v-57280228"></view><image class="icon_search data-v-57280228" src="/static/icon/icon_search.png"></image><input class="input flex-1 data-v-57280228" disabled="true" type="text" placeholder="出发城市/目的地" placeholder-class="cola" data-event-opts="{{[['tap',[['bindSearch']]]]}}" bindtap="__e"/></view></view></view><view class="pr data-v-57280228" style="{{'padding-top:'+('calc(90rpx + '+statusBarHeight+'px)')+';'}}"><view class="xilu_swiper data-v-57280228"><swiper class="swiper data-v-57280228" current="{{swiperCurrent}}" circular="{{true}}" previous-margin="38px" next-margin="38px" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{bannerList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="data-v-57280228"><view data-event-opts="{{[['tap',[['bannerjump',['$0'],[[['bannerList','',index,'minapp_url']]]]]]]}}" class="{{['nav','data-v-57280228',(swiperCurrent!==index)?'scale':'']}}" bindtap="__e"><image class="img data-v-57280228" src="{{item.thumb_image_text}}" mode="aspectFill"></image></view></swiper-item></block></swiper><view class="swiper_dots flex-box flex-center data-v-57280228"><block wx:for="{{bannerList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['dots','data-v-57280228',(swiperCurrent==index)?'active':'']}}"></view></block></view></view><view class="plr40 data-v-57280228"><view class="xilu_menu flex-box flex-between data-v-57280228"><block wx:for="{{navigationList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navigation',[index]]]]]}}" class="item data-v-57280228" bindtap="__e"><image src="{{item.icon_text}}" mode="aspectFill" class="data-v-57280228"></image><view class="fs30 col-3 data-v-57280228">{{item.name}}</view></view></block></view><block wx:if="{{$root.g0>0}}"><navigator class="xilu_banner data-v-57280228" url="{{adList[0].minapp_url}}" hover-class="none"><image src="{{adList[0].thumb_image_text}}" mode="aspectFill" class="data-v-57280228"></image></navigator></block><view class="xilu_title flex-box data-v-57280228"><view class="flex-1 title data-v-57280228">景点推荐</view><navigator class="more data-v-57280228" url="/pages/more_landscape/more_landscape" hover-class="none">更多+</navigator></view><view class="xilu_landscape data-v-57280228"><block wx:for="{{sceneryList}}" wx:for-item="item" wx:for-index="index"><navigator class="item data-v-57280228" url="{{'/pages/landscape_detail/landscape_detail?id='+item.id}}" hover-class="none"><image src="{{item.thumb_image_text}}" mode="aspectFill" class="data-v-57280228"></image><view class="name m-ellipsis mb20 data-v-57280228">{{item.name}}</view><block wx:if="{{item.project}}"><view class="col-price data-v-57280228"><text class="fs30 data-v-57280228">¥</text><text class="fs40 data-v-57280228">{{item.project.salesprice}}</text><text class="fs30 data-v-57280228">起</text></view></block></navigator></block></view><view class="xilu_title flex-box data-v-57280228"><view class="flex-1 title data-v-57280228">线路推荐</view><navigator class="more data-v-57280228" url="/pages/popular_travel/popular_travel" hover-class="none">更多+</navigator></view><view class="g_travel_list data-v-57280228"><tour-list vue-id="8dd740cc-1" tourList="{{tourList}}" class="data-v-57280228" bind:__l="__l"></tour-list><block wx:if="{{tourListMore.nothing}}"><view class="nothing data-v-57280228"><image src="/static/icon/icon_nothing.png" mode="aspectFit" class="data-v-57280228"></image><text class="data-v-57280228">暂无内容</text></view></block><block wx:else><view class="g-btn3-wrap data-v-57280228"><view data-event-opts="{{[['tap',[['fetchTourList',['$event']]]]]}}" class="g-btn3 data-v-57280228" bindtap="__e">{{tourListMore.text}}</view></view></block></view></view></view></view></view>