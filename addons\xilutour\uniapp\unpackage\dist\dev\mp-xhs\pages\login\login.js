(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/login/login"],{

/***/ 70:
/*!********************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/main.js?{"page":"pages%2Flogin%2Flogin"} ***!
  \********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 3);
__webpack_require__(/*! uni-pages */ 25);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 24));
var _login = _interopRequireDefault(__webpack_require__(/*! ./pages/login/login.vue */ 71));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_login.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["createPage"]))

/***/ }),

/***/ 71:
/*!*************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue ***!
  \*************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./login.vue?vue&type=template&id=b237504c& */ 72);
/* harmony import */ var _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./login.vue?vue&type=script&lang=js& */ 74);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./login.vue?vue&type=style&index=0&lang=css& */ 76);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 31);

var renderjs





/* normalize component */

var component = Object(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["render"],
  _login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/login/login.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 72:
/*!********************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=template&id=b237504c& ***!
  \********************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c& */ 73);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 73:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=template&id=b237504c& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 397))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 74:
/*!**************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js& */ 75);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 75:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      logo: '',
      mobile: '',
      disabledCode: false,
      codeText: '验证码',
      code: '',
      agree: true,
      isAgree: true,
      content: '',
      userAgreement: {},
      privacyAgreement: {}
    };
  },
  onLoad: function onLoad() {},
  methods: {
    closeLogin: function closeLogin() {
      uni.navigateBack({
        delta: 1
      });
    },
    agreePopOpen: function agreePopOpen(type) {
      //let title = type == 'user_agreement' ? '用户协议' : '隐私协议';
      var content = type == 'user_agreement' ? this.userAgreement.content : this.privacyAgreement.content;
      this.content = content;
      this.$refs.agreePopup.open();
    },
    agreePopClose: function agreePopClose() {
      this.$refs.agreePopup.close();
    },
    getArticle: function getArticle() {
      var _this = this;
      this.$core.post({
        url: 'xilutour.singlepage/config_page',
        data: {},
        success: function success(ret) {
          _this.userAgreement = ret.data.user_agreement;
          _this.privacyAgreement = ret.data.privacy_agreement;
        },
        loading: false
      });
    },
    toggleAgree: function toggleAgree() {
      this.isAgree = !this.isAgree;
    },
    //获取验证码
    getCode: function getCode() {
      var _this2 = this;
      if (this.disabledCode) return false;
      var mobile = this.mobile;
      if (!mobile) {
        uni.showToast({
          title: '手机号不得为空',
          icon: 'none'
        });
        return false;
      }
      this.$core.post({
        url: 'sms/send',
        data: {
          mobile: mobile,
          event: 'mobilelogin'
        },
        success: function success(ret) {
          _this2.timeCut();
        }
      });
    },
    // 倒计时
    timeCut: function timeCut() {
      var _this3 = this;
      if (this.disabledCode) return;
      this.disabledCode = true;
      var n = 60;
      this.codeText = n + 's';
      var run = setInterval(function () {
        n -= 1;
        if (n < 0) {
          clearInterval(run);
        }
        _this3.codeText = n + 's';
        if (_this3.codeText < 0 + 's') {
          _this3.disabledCode = false;
          _this3.codeText = '验证码';
        }
      }, 1000);
    },
    mobilelogin: function mobilelogin() {
      var that = this;
      if (!that.isAgree) {
        uni.showToast({
          title: '请同意协议',
          icon: 'none'
        });
        return false;
      }
      var mobile = this.mobile;
      var code = this.code;
      if (!mobile || !code) {
        uni.showToast({
          title: '手机号/验证码必填',
          icon: 'none'
        });
        return false;
      }
      var puserId = this.$core.getCache("puser_id") || 0;
      this.$core.post({
        url: 'xilutour.user/mobilelogin',
        data: {
          puser_id: puserId,
          mobile: mobile,
          code: code
        },
        success: function success(ret) {
          var userinfo = ret.data.userinfo;
          that.$core.setUserinfo(userinfo);
          uni.$emit("user_update", {});
          uni.navigateBack({
            delta: 1
          });
        }
      });
    },
    getPhoneNumber: function getPhoneNumber(e) {
      var that = this;
      var version = uni.getSystemInfoSync().SDKVersion;
      if (e.detail.errMsg == "getPhoneNumber:ok") {
        if (that.compareVersion(version, '2.21.2') >= 0) {
          that.phoneNumber(that, {
            code: e.detail.code
          });
        } else {
          uni.login({
            provider: 'weixin',
            success: function success(auth) {
              that.phoneNumber(that, {
                iv: e.detail.iv,
                encryptedData: e.detail.encryptedData
              });
            },
            fail: function fail() {
              uni.showToast({
                'title': '微信登录授权失败',
                icon: "none"
              });
            }
          });
        }
      } else {
        console.log("用户点击了拒绝");
      }
    },
    phoneNumber: function phoneNumber(that, data) {
      var wxAccount = that.$core.getCache('wx_account');
      data['third_id'] = wxAccount.third_id;
      var puserId = this.$core.getCache("puser_id") || 0;
      data['puser_id'] = puserId;
      that.$core.post({
        url: 'xilutour.user/get_mobile',
        data: data,
        success: function success(ret, response) {
          wxAccount['bindind'] = 1;
          var userinfo = ret.data.userinfo;
          that.$core.setCache('wx_account', wxAccount);
          that.$core.setUserinfo(userinfo);
          getApp().globalData.userinfo = userinfo;
          uni.navigateBack({});
          uni.$emit("user_update", {});
          uni.showToast({
            title: '登录成功'
          });
        },
        fail: function fail(ret, response) {
          //失败，重试
          uni.showToast({
            'title': "获取失败",
            icon: "none"
          });
          return false;
        }
      });
    },
    //版本比较
    compareVersion: function compareVersion(v1, v2) {
      v1 = v1.split('.');
      v2 = v2.split('.');
      var len = Math.max(v1.length, v2.length);
      while (v1.length < len) {
        v1.push('0');
      }
      while (v2.length < len) {
        v2.push('0');
      }
      for (var i = 0; i < len; i++) {
        var num1 = parseInt(v1[i]);
        var num2 = parseInt(v2[i]);
        if (num1 > num2) {
          return 1;
        } else if (num1 < num2) {
          return -1;
        }
      }
      return 0;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"]))

/***/ }),

/***/ 76:
/*!**********************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=style&index=0&lang=css& ***!
  \**********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=css& */ 77);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 77:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=style&index=0&lang=css& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[70,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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