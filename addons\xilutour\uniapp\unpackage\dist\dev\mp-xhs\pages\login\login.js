(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/login/login"],{

/***/ 70:
/*!********************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/main.js?{"page":"pages%2Flogin%2Flogin"} ***!
  \********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 3);
__webpack_require__(/*! uni-pages */ 25);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 24));
var _login = _interopRequireDefault(__webpack_require__(/*! ./pages/login/login.vue */ 71));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_login.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["createPage"]))

/***/ }),

/***/ 71:
/*!*************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue ***!
  \*************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./login.vue?vue&type=template&id=b237504c& */ 72);
/* harmony import */ var _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./login.vue?vue&type=script&lang=js& */ 74);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./login.vue?vue&type=style&index=0&lang=css& */ 76);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 31);

var renderjs





/* normalize component */

var component = Object(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["render"],
  _login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/login/login.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 72:
/*!********************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=template&id=b237504c& ***!
  \********************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c& */ 73);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 73:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=template&id=b237504c& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 405))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 74:
/*!**************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js& */ 75);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 75:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      logo: '',
      mobile: '',
      disabledCode: false,
      codeText: '验证码',
      code: '',
      agree: true,
      isAgree: true,
      content: '',
      userAgreement: {},
      privacyAgreement: {}
    };
  },
  onLoad: function onLoad() {
    this.logo = getApp().globalData.config.logo;
    this.$core.xhsLogin(function (data) {
      getApp().globalData.userinfo = data;
      uni.navigateBack({});
      uni.$emit("user_update", {});
      uni.showToast({
        title: '登录成功'
      });
    });
  },
  methods: {
    closeLogin: function closeLogin() {
      uni.navigateBack({
        delta: 1
      });
    },
    agreePopOpen: function agreePopOpen(type) {
      //let title = type == 'user_agreement' ? '用户协议' : '隐私协议';
      var content = type == 'user_agreement' ? this.userAgreement.content : this.privacyAgreement.content;
      this.content = content;
      this.$refs.agreePopup.open();
    },
    agreePopClose: function agreePopClose() {
      this.$refs.agreePopup.close();
    },
    getArticle: function getArticle() {
      var _this = this;
      this.$core.post({
        url: 'xilutour.singlepage/config_page',
        data: {},
        success: function success(ret) {
          _this.userAgreement = ret.data.user_agreement;
          _this.privacyAgreement = ret.data.privacy_agreement;
        },
        loading: false
      });
    },
    toggleAgree: function toggleAgree() {
      this.isAgree = !this.isAgree;
    },
    //获取验证码
    getCode: function getCode() {
      var _this2 = this;
      if (this.disabledCode) return false;
      var mobile = this.mobile;
      if (!mobile) {
        uni.showToast({
          title: '手机号不得为空',
          icon: 'none'
        });
        return false;
      }
      this.$core.post({
        url: 'sms/send',
        data: {
          mobile: mobile,
          event: 'mobilelogin'
        },
        success: function success(ret) {
          _this2.timeCut();
        }
      });
    },
    // 倒计时
    timeCut: function timeCut() {
      var _this3 = this;
      if (this.disabledCode) return;
      this.disabledCode = true;
      var n = 60;
      this.codeText = n + 's';
      var run = setInterval(function () {
        n -= 1;
        if (n < 0) {
          clearInterval(run);
        }
        _this3.codeText = n + 's';
        if (_this3.codeText < 0 + 's') {
          _this3.disabledCode = false;
          _this3.codeText = '验证码';
        }
      }, 1000);
    },
    mobilelogin: function mobilelogin() {
      var that = this;
      if (!that.isAgree) {
        uni.showToast({
          title: '请同意协议',
          icon: 'none'
        });
        return false;
      }
      var mobile = this.mobile;
      var code = this.code;
      if (!mobile || !code) {
        uni.showToast({
          title: '手机号/验证码必填',
          icon: 'none'
        });
        return false;
      }
      var puserId = this.$core.getCache("puser_id") || 0;
      this.$core.post({
        url: 'xilutour.user/mobilelogin',
        data: {
          puser_id: puserId,
          mobile: mobile,
          code: code
        },
        success: function success(ret) {
          var userinfo = ret.data.userinfo;
          that.$core.setUserinfo(userinfo);
          uni.$emit("user_update", {});
          uni.navigateBack({
            delta: 1
          });
        }
      });
    },
    getPhoneNumber: function getPhoneNumber(e) {
      var that = this;
      var version = uni.getSystemInfoSync().SDKVersion;
      if (e.detail.errMsg == "getPhoneNumber:ok") {
        if (that.compareVersion(version, '2.21.2') >= 0) {
          that.phoneNumber(that, {
            code: e.detail.code
          });
        } else {
          uni.login({
            provider: 'weixin',
            success: function success(auth) {
              that.phoneNumber(that, {
                iv: e.detail.iv,
                encryptedData: e.detail.encryptedData
              });
            },
            fail: function fail() {
              uni.showToast({
                'title': '微信登录授权失败',
                icon: "none"
              });
            }
          });
        }
      } else {
        console.log("用户点击了拒绝");
      }
    },
    phoneNumber: function phoneNumber(that, data) {
      var wxAccount = that.$core.getCache('wx_account');
      data['third_id'] = wxAccount.third_id;
      var puserId = this.$core.getCache("puser_id") || 0;
      data['puser_id'] = puserId;
      that.$core.post({
        url: 'xilutour.user/get_mobile',
        data: data,
        success: function success(ret, response) {
          wxAccount['bindind'] = 1;
          var userinfo = ret.data.userinfo;
          that.$core.setCache('wx_account', wxAccount);
          that.$core.setUserinfo(userinfo);
          getApp().globalData.userinfo = userinfo;
          uni.navigateBack({});
          uni.$emit("user_update", {});
          uni.showToast({
            title: '登录成功'
          });
        },
        fail: function fail(ret, response) {
          //失败，重试
          uni.showToast({
            'title': "获取失败",
            icon: "none"
          });
          return false;
        }
      });
    },
    //版本比较
    compareVersion: function compareVersion(v1, v2) {
      v1 = v1.split('.');
      v2 = v2.split('.');
      var len = Math.max(v1.length, v2.length);
      while (v1.length < len) {
        v1.push('0');
      }
      while (v2.length < len) {
        v2.push('0');
      }
      for (var i = 0; i < len; i++) {
        var num1 = parseInt(v1[i]);
        var num2 = parseInt(v2[i]);
        if (num1 > num2) {
          return 1;
        } else if (num1 < num2) {
          return -1;
        }
      }
      return 0;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"]))

/***/ }),

/***/ 76:
/*!**********************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=style&index=0&lang=css& ***!
  \**********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=css& */ 77);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 77:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=style&index=0&lang=css& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[70,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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