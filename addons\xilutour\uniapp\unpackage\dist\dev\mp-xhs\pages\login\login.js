(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/login/login"],{

/***/ 70:
/*!********************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/main.js?{"page":"pages%2Flogin%2Flogin"} ***!
  \********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 3);
__webpack_require__(/*! uni-pages */ 25);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 24));
var _login = _interopRequireDefault(__webpack_require__(/*! ./pages/login/login.vue */ 71));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_login.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["createPage"]))

/***/ }),

/***/ 71:
/*!*************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue ***!
  \*************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./login.vue?vue&type=template&id=b237504c& */ 72);
/* harmony import */ var _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./login.vue?vue&type=script&lang=js& */ 74);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./login.vue?vue&type=style&index=0&lang=css& */ 76);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 31);

var renderjs





/* normalize component */

var component = Object(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["render"],
  _login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/login/login.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 72:
/*!********************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=template&id=b237504c& ***!
  \********************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c& */ 73);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_template_id_b237504c___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 73:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=template&id=b237504c& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 397))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 74:
/*!**************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js& */ 75);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 75:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      logo: '',
      mobile: '',
      disabledCode: false,
      codeText: '验证码',
      code: '',
      agree: true,
      isAgree: true,
      content: '',
      userAgreement: {},
      privacyAgreement: {}
    };
  },
  onLoad: function onLoad() {
    this.logo = getApp().globalData.config.logo;
    this.$core.xhsLogin(function (data) {
      getApp().globalData.userinfo = data;
      uni.navigateBack({});
      uni.$emit("user_update", {});
      uni.showToast({
        title: '登录成功'
      });
    });
  },
  methods: {
    closeLogin: function closeLogin() {
      uni.navigateBack({
        delta: 1
      });
    },
    agreePopOpen: function agreePopOpen(type) {
      //let title = type == 'user_agreement' ? '用户协议' : '隐私协议';
      var content = type == 'user_agreement' ? this.userAgreement.content : this.privacyAgreement.content;
      this.content = content;
      this.$refs.agreePopup.open();
    },
    agreePopClose: function agreePopClose() {
      this.$refs.agreePopup.close();
    },
    getArticle: function getArticle() {
      var _this = this;
      this.$core.post({
        url: 'xilutour.singlepage/config_page',
        data: {},
        success: function success(ret) {
          _this.userAgreement = ret.data.user_agreement;
          _this.privacyAgreement = ret.data.privacy_agreement;
        },
        loading: false
      });
    },
    toggleAgree: function toggleAgree() {
      this.isAgree = !this.isAgree;
    },
    //获取验证码
    getCode: function getCode() {
      var _this2 = this;
      if (this.disabledCode) return false;
      var mobile = this.mobile;
      if (!mobile) {
        uni.showToast({
          title: '手机号不得为空',
          icon: 'none'
        });
        return false;
      }
      this.$core.post({
        url: 'sms/send',
        data: {
          mobile: mobile,
          event: 'mobilelogin'
        },
        success: function success(ret) {
          _this2.timeCut();
        }
      });
    },
    // 倒计时
    timeCut: function timeCut() {
      var _this3 = this;
      if (this.disabledCode) return;
      this.disabledCode = true;
      var n = 60;
      this.codeText = n + 's';
      var run = setInterval(function () {
        n -= 1;
        if (n < 0) {
          clearInterval(run);
        }
        _this3.codeText = n + 's';
        if (_this3.codeText < 0 + 's') {
          _this3.disabledCode = false;
          _this3.codeText = '验证码';
        }
      }, 1000);
    },
    mobilelogin: function mobilelogin() {
      var that = this;
      if (!that.isAgree) {
        uni.showToast({
          title: '请同意协议',
          icon: 'none'
        });
        return false;
      }
      var mobile = this.mobile;
      var code = this.code;
      if (!mobile || !code) {
        uni.showToast({
          title: '手机号/验证码必填',
          icon: 'none'
        });
        return false;
      }
      var puserId = this.$core.getCache("puser_id") || 0;
      this.$core.post({
        url: 'xilutour.user/mobilelogin',
        data: {
          puser_id: puserId,
          mobile: mobile,
          code: code
        },
        success: function success(ret) {
          var userinfo = ret.data.userinfo;
          that.$core.setUserinfo(userinfo);
          uni.$emit("user_update", {});
          uni.navigateBack({
            delta: 1
          });
        }
      });
    },
    getPhoneNumber: function getPhoneNumber(e) {
      var that = this;
      var version = uni.getSystemInfoSync().SDKVersion;
      if (e.detail.errMsg == "getPhoneNumber:ok") {
        if (that.compareVersion(version, '2.21.2') >= 0) {
          that.phoneNumber(that, {
            code: e.detail.code
          });
        } else {
          uni.login({
            provider: 'weixin',
            success: function success(auth) {
              that.phoneNumber(that, {
                iv: e.detail.iv,
                encryptedData: e.detail.encryptedData
              });
            },
            fail: function fail() {
              uni.showToast({
                'title': '微信登录授权失败',
                icon: "none"
              });
            }
          });
        }
      } else {
        console.log("用户点击了拒绝");
      }
    },
    phoneNumber: function phoneNumber(that, data) {
      var wxAccount = that.$core.getCache('wx_account');
      data['third_id'] = wxAccount.third_id;
      var puserId = this.$core.getCache("puser_id") || 0;
      data['puser_id'] = puserId;
      that.$core.post({
        url: 'xilutour.user/get_mobile',
        data: data,
        success: function success(ret, response) {
          wxAccount['bindind'] = 1;
          var userinfo = ret.data.userinfo;
          that.$core.setCache('wx_account', wxAccount);
          that.$core.setUserinfo(userinfo);
          getApp().globalData.userinfo = userinfo;
          uni.navigateBack({});
          uni.$emit("user_update", {});
          uni.showToast({
            title: '登录成功'
          });
        },
        fail: function fail(ret, response) {
          //失败，重试
          uni.showToast({
            'title': "获取失败",
            icon: "none"
          });
          return false;
        }
      });
    },
    // 小红书获取手机号
    getXhsPhoneNumber: function getXhsPhoneNumber(e) {
      var that = this;
      if (e.detail.errMsg == "getPhoneNumber:ok") {
        var xhsAccount = that.$core.getCache("xhs_account") || {};
        var puserId = that.$core.getCache("puser_id") || 0;

        // 使用新版API获取手机号
        if (e.detail.code) {
          that.$core.xhsGetMobile({
            third_id: xhsAccount.third_id,
            code: e.detail.code,
            puser_id: puserId
          }, function (userinfo) {
            getApp().globalData.userinfo = userinfo;
            uni.navigateBack({});
            uni.$emit("user_update", {});
            uni.showToast({
              title: '登录成功'
            });
          });
        } else {
          // 使用旧版解密方式
          that.$core.xhsGetMobile({
            third_id: xhsAccount.third_id,
            iv: e.detail.iv,
            encryptedData: e.detail.encryptedData,
            puser_id: puserId
          }, function (userinfo) {
            getApp().globalData.userinfo = userinfo;
            uni.navigateBack({});
            uni.$emit("user_update", {});
            uni.showToast({
              title: '登录成功'
            });
          });
        }
      } else {
        console.log("用户点击了拒绝");
      }
    },
    //版本比较
    compareVersion: function compareVersion(v1, v2) {
      v1 = v1.split('.');
      v2 = v2.split('.');
      var len = Math.max(v1.length, v2.length);
      while (v1.length < len) {
        v1.push('0');
      }
      while (v2.length < len) {
        v2.push('0');
      }
      for (var i = 0; i < len; i++) {
        var num1 = parseInt(v1[i]);
        var num2 = parseInt(v2[i]);
        if (num1 > num2) {
          return 1;
        } else if (num1 < num2) {
          return -1;
        }
      }
      return 0;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"]))

/***/ }),

/***/ 76:
/*!**********************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=style&index=0&lang=css& ***!
  \**********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=css& */ 77);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_login_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 77:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?vue&type=style&index=0&lang=css& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[70,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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