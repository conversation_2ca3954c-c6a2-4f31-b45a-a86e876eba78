<view class="xilu data-v-bff09234"><view class="container data-v-bff09234"><view class="xilu_data_box flex-box data-v-bff09234"><view class="flex-1 data-v-bff09234"><view class="num data-v-bff09234">{{totalCount}}</view><view class="fs28 col-f data-v-bff09234">我的好友</view></view><view data-event-opts="{{[['tap',[['sharePopOpen',['$event']]]]]}}" class="btn data-v-bff09234" bindtap="__e">邀请好友</view></view><view class="fs36 col-10 mb30 data-v-bff09234">我的好友</view><block wx:for="{{teamList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="xilu_friend flex-box data-v-bff09234"><image class="img data-v-bff09234" src="{{item.user.avatar}}" mode="aspectFill"></image><view class="flex-1 data-v-bff09234"><view class="name data-v-bff09234">{{item.user.nickname}}</view><view class="time data-v-bff09234">{{item.bindtime_text}}</view></view></view></block><view class="g-btn3-wrap data-v-bff09234"><view data-event-opts="{{[['tap',[['fetch',['$event']]]]]}}" class="g-btn3 data-v-bff09234" bindtap="__e">{{teamListMore.text}}</view></view><uni-popup vue-id="60efa6e0-1" data-ref="sharePopup" class="data-v-bff09234 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['share-pop','data-v-bff09234',(screenHeight<750)?'scale':'']}}" catchtouchmove="true"><image class="img_post data-v-bff09234" src="{{posterPath}}" mode="widthFix"></image><view class="flex-box flex-between tc data-v-bff09234" style="padding:0 116rpx;"><button class="pop_btn fs24 col-f data-v-bff09234" open-type="share"><image class="icon data-v-bff09234" src="/static/icon/icon_wx1.png" mode="aspectFit"></image><view class="data-v-bff09234">微信好友</view></button><button data-event-opts="{{[['tap',[['saveImage',['$event']]]]]}}" class="pop_btn fs24 col-f data-v-bff09234" bindtap="__e"><image class="icon data-v-bff09234" src="/static/icon/icon_down.png" mode="aspectFit"></image><view class="data-v-bff09234">保存图片</view></button></view></view></uni-popup><l-painter vue-id="60efa6e0-2" useCORS="{{true}}" isCanvasToTempFilePath="{{true}}" custom-style="position: fixed; left: 200%;" css="width: 640rpx;height: 1068rpx;border-radius: 20rpx;background-color:#FFF;" data-ref="painter" data-event-opts="{{[['^success',[['e0']]]]}}" bind:success="__e" class="data-v-bff09234 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><l-painter-image vue-id="{{('60efa6e0-3')+','+('60efa6e0-2')}}" src="{{poster.poster_img}}" css="object-fit: cover; object-position: 50% 50%; width: 100%; height: 868rpx;border-radius: 20rpx 20rpx 0 0;display:block;" class="data-v-bff09234" bind:__l="__l"></l-painter-image><l-painter-image vue-id="{{('60efa6e0-4')+','+('60efa6e0-2')}}" src="{{poster.img1}}" css="position: absolute;bottom: 50rpx;left: 30rpx; object-fit: cover; object-position: 50% 50%; width: 100rpx; height: 100rpx;border-radius: 50%;" class="data-v-bff09234" bind:__l="__l"></l-painter-image><l-painter-view vue-id="{{('60efa6e0-5')+','+('60efa6e0-2')}}" css="position: absolute;top:918rpx;left:144rpx;right:210rpx;" class="data-v-bff09234" bind:__l="__l" vue-slots="{{['default']}}"><l-painter-text vue-id="{{('60efa6e0-6')+','+('60efa6e0-5')}}" text="{{poster.text1}}" css="font-size:34rpx;font-weight:bold;color:#333;line-height:48rpx;" class="data-v-bff09234" bind:__l="__l"></l-painter-text></l-painter-view><l-painter-view vue-id="{{('60efa6e0-7')+','+('60efa6e0-2')}}" css="position: absolute;top:974rpx;left:144rpx;right:210rpx;" class="data-v-bff09234" bind:__l="__l" vue-slots="{{['default']}}"><l-painter-text vue-id="{{('60efa6e0-8')+','+('60efa6e0-7')}}" text="{{poster.text2}}" css="font-size:24rpx;font-weight:500;color:#999999;line-height:32rpx;" class="data-v-bff09234" bind:__l="__l"></l-painter-text></l-painter-view><l-painter-image vue-id="{{('60efa6e0-9')+','+('60efa6e0-2')}}" src="{{poster.img2}}" css="position: absolute;bottom: 12rpx;right: 30rpx; object-fit: cover; object-position: 50% 50%; width: 176rpx; height: 176rpx;" class="data-v-bff09234" bind:__l="__l"></l-painter-image></l-painter></view></view>