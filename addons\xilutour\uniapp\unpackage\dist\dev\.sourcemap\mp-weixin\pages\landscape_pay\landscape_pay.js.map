{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_pay/landscape_pay.vue?b088", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_pay/landscape_pay.vue?94c2", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_pay/landscape_pay.vue?1773", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_pay/landscape_pay.vue?d0ee", "uni-app:///pages/landscape_pay/landscape_pay.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_pay/landscape_pay.vue?7e17", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_pay/landscape_pay.vue?65bb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "sceneryId", "projectId", "buyNum", "couponId", "order", "pay_price", "coupon", "coupon_list", "coupon_price", "project", "salesprice", "attention", "scenery", "name", "total_price", "loading", "onLoad", "methods", "preOrder", "url", "project_id", "scenery_id", "coupon_id", "buy_num", "success", "fail", "uni", "title", "content", "showCancel", "bindChangeCount", "createOrder", "console", "payment", "pay_type", "order_id", "platform", "type", "callphone", "phoneNumber", "bindOpenLocation", "latitude", "longitude", "address", "couponPopOpen", "couponPopClose", "bindChooseCoupon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgI11B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;QACA;QACAC;UACAC;QAEA;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAAC;QAAApB;UAAAqB;UAAAC;UAAAC;UAAAC;QAAA;QAAAR;QAAAS;UACA;QACA;QACAC;UACAC;YACAC;YACAC;YACAC;YACAL;cACA;gBACAE;cACA;YACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAI;MACA;QACA;MACA;QACA;UACA;QACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QAAAZ;QAAApB;UAAAqB;UAAAC;UAAAC;UAAAC;QAAA;QAAAR;QAAAS;UACA;UACA;UACA;QACA;QAAAC;UACA;UACAO;QACA;MACA;IACA;IACAC;MAAA;MAEA;QAAAd;QAAApB;UAAAmC;UAAAC;UAAAC;UAAAC;QAAA;QAAAb;UACA;UACA;YACAE;cACAP;YACA;UACA;QACA;MAAA;IAEA;IACA;IACAmB;MACA;MACAZ;QACAa;MACA;IACA;IACA;IACAC;MACA;MACA;MACAd;QACAe;QACAC;QACA7B;QACA8B;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtPA;AAAA;AAAA;AAAA;AAAyhD,CAAgB,05CAAG,EAAC,C;;;;;;;;;;;ACA7iD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/landscape_pay/landscape_pay.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/landscape_pay/landscape_pay.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./landscape_pay.vue?vue&type=template&id=540e25e0&scoped=true&\"\nvar renderjs\nimport script from \"./landscape_pay.vue?vue&type=script&lang=js&\"\nexport * from \"./landscape_pay.vue?vue&type=script&lang=js&\"\nimport style0 from \"./landscape_pay.vue?vue&type=style&index=0&id=540e25e0&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"540e25e0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/landscape_pay/landscape_pay.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_pay.vue?vue&type=template&id=540e25e0&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.order.coupon_list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_pay.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_pay.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"page-foot ptb15\">\r\n\t\t\t<view class=\"g_order_foot1 flex-box\">\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"col-price\">\r\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs40\">{{order.pay_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"fs30 col-89 pl25\">共计</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn1\" @click=\"createOrder()\"  :loading=\"loading\" :disabled=\"loading\">立即支付</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"ptb30 plr40 bg-white\">\r\n\t\t\t\t<view class=\"xilu_travel flex-box mb30\">\r\n\t\t\t\t\t<image :src=\"order.scenery.thumb_image_text\" mode=\"aspectFill\" class=\"img mr30\"></image>\r\n\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t<view class=\"fs36 col-10 mb15\">{{order.scenery.name}}</view>\r\n\t\t\t\t\t\t<view class=\"fs30 col-3 mb15 m-ellipsis\">{{order.project.name}}</view>\r\n\t\t\t\t\t\t<view class=\"fs26 col-89 mb15\">{{order.project.attention}}</view>\r\n\t\t\t\t\t\t<view class=\"col-price\">\r\n\t\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t\t<text class=\"fs40\">{{order.project.salesprice}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"opening_box\">\r\n\t\t\t\t\t<view class=\"flex-box mb30\">\r\n\t\t\t\t\t\t<view class=\"col-normal fs30 mr30\">开放时间</view>\r\n\t\t\t\t\t\t<view class=\"fs30 col-5 mr15\">{{order.scenery.work_time}}</view>\r\n\t\t\t\t\t\t<image @click=\"callphone()\" class=\"icon\" src=\"/static/icon/icon_phone.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box flex-align-start\">\r\n\t\t\t\t\t\t<view class=\"flex-1 mr10 fs30 col-5\">{{order.scenery.city?order.scenery.city.name:''}}{{order.scenery.district?order.scenery.district.name:''}}{{order.scenery.address}}</view>\r\n\t\t\t\t\t\t<image v-if=\"order.scenery.lat\" @click=\"bindOpenLocation()\" class=\"icon\" src=\"/static/icon/icon_go.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ptb30 plr40\">\r\n\t\t\t\t<view class=\"xilu_number flex-box mb30\">\r\n\t\t\t\t\t<view class=\"flex-1\">出行人数</view>\r\n\t\t\t\t\t<image src=\"/static/icon/icon_jian.png\" @click=\"bindChangeCount('minus')\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<view class=\"num\">{{buyNum}}</view>\r\n\t\t\t\t\t<image src=\"/static/icon/icon_jia.png\" @click=\"bindChangeCount('plus')\" mode=\"aspectFit\"></image>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"g_order_info\">\r\n\t\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">支付方式</view>\r\n\t\t\t\t\t\t<image src=\"/static/icon/icon_wx.png\" mode=\"aspectFit\" class=\"g-icon30 mr15\"></image>\r\n\t\t\t\t\t\t<view class=\"fs30 col-10\">微信支付</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">商品金额</view>\r\n\t\t\t\t\t\t<view class=\"col-10\">\r\n\t\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t\t<text class=\"fs40\">{{order.total_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"flex-box mb50\" v-if=\"order.coupon_list.length>0\" @click=\"couponPopOpen\">\r\n\t\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">优惠劵</view>\r\n\t\t\t\t\t\t<view class=\"col-10 m-arrow-right\">\r\n\t\t\t\t\t\t\t<text class=\"fs24\">-¥</text>\r\n\t\t\t\t\t\t\t<text class=\"fs34\">{{order.coupon_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"flex-box flex-end\">\r\n\t\t\t\t\t\t<view class=\"fs30 col-89 mr20\">共计</view>\r\n\t\t\t\t\t\t<view class=\"col-price\">\r\n\t\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t\t<text class=\"fs40\">{{order.total_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t<uni-popup ref=\"couponPopup\" type=\"bottom\">\r\n\t\t\t\t<view class=\"g_coupon_pop\">\r\n\t\t\t\t\t<view class=\"fs30 col-10 tc mb30\">优惠券</view>\r\n\t\t\t\t\t<image src=\"/static/icon/icon_close.png\" mode=\"aspectFit\" class=\"icon_close\" @click=\"couponPopClose\"></image>\r\n\t\t\t\r\n\t\t\t\t\t<view class=\"pop_coupon_wrap\">\r\n\t\t\t\t\t\t<view class=\"pop_coupon\" v-for=\"(coupon,index) in order.coupon_list\" :key=\"index\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_coupon_bg1.png\" mode=\"aspectFill\" class=\"bg\"></image>\r\n\t\t\t\t\t\t\t<view class=\"inner flex-box\">\r\n\t\t\t\t\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"fwb mb20\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"fs24\">¥</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"fs50\">{{coupon.money}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"man\">满{{coupon.at_least}}可用</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"right flex-1 flex-box\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"fs30 mb20\">{{coupon.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"fs24\">{{coupon.use_end_time_text}}到期</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"use active\" v-if=\"order.coupon.id == coupon.id\">\n\t\t\t\t\t\t\t\t\t\t<image src=\"/static/icon/icon_coupon_check.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"use\" @click=\"bindChooseCoupon(index)\" v-else>选择</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\t\t<view class=\"g-btn1\" style=\"width: 670rpx;margin: 30rpx auto 0;\">确定</view>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\n\t\t\t\tsceneryId: 0,\r\n\t\t\t\tprojectId: 0,\n\t\t\t\tbuyNum:1,\n\t\t\t\tcouponId:0,\n\t\t\t\torder:{\n\t\t\t\t\tpay_price:'0.00',\n\t\t\t\t\tcoupon:null,\n\t\t\t\t\tcoupon_list:[],\n\t\t\t\t\tcoupon_price:'0.00',\n\t\t\t\t\tproject:{\n\t\t\t\t\t\tsalesprice:'0.00',\n\t\t\t\t\t\tattention:''\n\t\t\t\t\t},\n\t\t\t\t\tscenery:{\n\t\t\t\t\t\tname: '',\n\t\t\t\t\t\t\n\t\t\t\t\t},\n\t\t\t\t\ttotal_price:'0.00'\n\t\t\t\t},\n\t\t\t\tloading: false,\r\n\t\t\t};\r\n\t\t},\n\t\tonLoad(options) {\n\t\t\tthis.sceneryId = options.scenery_id;\n\t\t\tthis.projectId = options.project_id;\n\t\t\tthis.preOrder();\n\t\t},\n\t\tmethods:{\n\t\t\tpreOrder(){\n\t\t\t\tthis.$core.post({url:'xilutour.scenery_order/pre_order',data:{project_id: this.projectId,scenery_id:this.sceneryId,coupon_id:this.couponId,buy_num:this.buyNum},loading:true,success:(ret)=>{\n\t\t\t\t\tthis.order = ret.data;\n\t\t\t\t },\n\t\t\t\t fail:(ret)=>{\n\t\t\t\t\t uni.showModal({\n\t\t\t\t\t \ttitle: '提示',\n\t\t\t\t\t\tcontent: ret.msg,\n\t\t\t\t\t\tshowCancel:false,\n\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t\tif(res.confirm){\n\t\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t })\n\t\t\t\t\t return false;\n\t\t\t\t }\n\t\t\t\t });\n\t\t\t},\n\t\t\t//增减用户\n\t\t\tbindChangeCount(type){\n\t\t\t\tif(type == 'plus'){\n\t\t\t\t\tthis.buyNum = ++this.buyNum;\n\t\t\t\t}else{\n\t\t\t\t\tif(this.buyNum<=1){\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tthis.buyNum = --this.buyNum;\n\t\t\t\t}\n\t\t\t\tthis.preOrder()\n\t\t\t},\n\t\t\tcreateOrder(){\n\t\t\t\tthis.loading = true;\n\t\t\t\tthis.$core.post({url: 'xilutour.scenery_order/create_order',data: {project_id: this.projectId,scenery_id:this.sceneryId,coupon_id:this.couponId,buy_num:this.buyNum},loading: true,success: ret => {\n\t\t\t\t\t//下单成功，前往收银台\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t\tthis.payment(ret.data);\n\t\t\t\t\t},fail: err => {\n\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\tconsole.log(err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tpayment(order){\n\t\t\t\t//#ifdef MP-WEIXIN\n\t\t\t\tthis.$core.post({url:'xilutour.pay/pay',data:{pay_type:1,order_id:order.id,platform:'wxmini',type:'scenery_order'},success:(ret)=>{\n\t\t\t\t\tlet wxconfig =  ret.data;\n\t\t\t\t\tthis.$core.payment(wxconfig,function(){\n\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\turl: '/pages/landscape_order/landscape_order'\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t}});\n\t\t\t\t//#endif\n\t\t\t},\n\t\t\t//拨打电话\n\t\t\tcallphone(){\n\t\t\t\tlet tel = this.order.scenery.tel;\n\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\tphoneNumber: tel\n\t\t\t\t})\n\t\t\t},\n\t\t\t//导航\n\t\t\tbindOpenLocation(){\n\t\t\t\tlet scenery = this.order.scenery;\n\t\t\t\tlet address = (scenery.city?scenery.city.name:'') + (scenery.district?scenery.district.name:'') + (scenery.address)\n\t\t\t\tuni.openLocation({\n\t\t\t\t\tlatitude: Number(scenery.lat),\n\t\t\t\t\tlongitude: Number(scenery.lng),\n\t\t\t\t\tname: scenery.name,\n\t\t\t\t\taddress: address\n\t\t\t\t})\n\t\t\t},\r\n\t\t\t// 打开优惠券弹窗\r\n\t\t\tcouponPopOpen() {\r\n\t\t\t\tthis.$refs.couponPopup.open();\r\n\t\t\t},\r\n\t\t\t// 关闭优惠券弹窗\r\n\t\t\tcouponPopClose() {\r\n\t\t\t\tthis.$refs.couponPopup.close();\r\n\t\t\t},\n\t\t\t//选择优惠券\n\t\t\tbindChooseCoupon(index){\n\t\t\t\tthis.couponId = this.order.coupon_list[index].id;\n\t\t\t\tthis.preOrder();\n\t\t\t}\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.xilu {\r\n\t\t.g_order_info{\r\n\t\t\tbackground-color: #fff;\r\n\t\t}\r\n\t\t\r\n\t\t&_travel {\r\n\t\t\t.img {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 180rpx;\r\n\t\t\t\theight: 180rpx;\r\n\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_number {\r\n\t\t\tpadding: 0 35rpx 0 30rpx;\r\n\t\t\twidth: 670rpx;\r\n\t\t\theight: 110rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tbox-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);\r\n\t\t\tborder-radius: 25rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #555555;\r\n\t\t\tline-height: 34rpx;\r\n\r\n\t\t\timage {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.num {\r\n\t\t\t\twidth: 62rpx;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #101010;\r\n\t\t\t\tline-height: 40rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t\t.container {\r\n\t\t\tpadding: 0 0 200rpx !important;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t}\r\n\r\n\t\t.opening_box {\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t.icon {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 34rpx;\r\n\t\t\t\theight: 34rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.page-foot {\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t}\r\n\r\n\t\t.page-foot::after {\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_pay.vue?vue&type=style&index=0&id=540e25e0&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_pay.vue?vue&type=style&index=0&id=540e25e0&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341211\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}