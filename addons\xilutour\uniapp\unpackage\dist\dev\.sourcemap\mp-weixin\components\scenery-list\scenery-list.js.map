{"version": 3, "sources": ["webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/components/scenery-list/scenery-list.vue?45a9", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/components/scenery-list/scenery-list.vue?1379", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/components/scenery-list/scenery-list.vue?c66f", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/components/scenery-list/scenery-list.vue?35f2", "uni-app:///components/scenery-list/scenery-list.vue"], "names": ["name", "props", "sceneryList", "type", "default", "data"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;;;AAG3D;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwBz1B;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA,QAEA;EACA;AACA;AAAA,2B", "file": "components/scenery-list/scenery-list.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./scenery-list.vue?vue&type=template&id=3bef5c84&\"\nvar renderjs\nimport script from \"./scenery-list.vue?vue&type=script&lang=js&\"\nexport * from \"./scenery-list.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/scenery-list/scenery-list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scenery-list.vue?vue&type=template&id=3bef5c84&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scenery-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./scenery-list.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<navigator class=\"item\" :url=\"'/pages/landscape_detail/landscape_detail?id='+item.id\" hover-class=\"none\" v-for=\"(item,index) in sceneryList\">\n\t\t\t<image :src=\"item.thumb_image_text\" mode=\"aspectFill\" class=\"img\"></image>\n\t\t\t<view class=\"mb20\">\n\t\t\t\t<text class=\"level\" v-if=\"item.level\">{{item.level.name}}</text>\n\t\t\t\t\t<text class=\"g_feng\">{{item.points}}</text>\n\t\t\t\t<text class=\"fs36 col-10\">{{item.name}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"desc m-ellipsis mb15\">{{item.introduce}}</view>\n\t\t\t<view class=\"flex-box\">\n\t\t\t\t<view class=\"flex-1 col-price\" v-if=\"item.project\">\n\t\t\t\t\t<text class=\"fs30\">¥</text>\n\t\t\t\t\t<text class=\"fs40\">{{item.project.salesprice}}</text>\n\t\t\t\t\t<text class=\"fs30\">起</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"fs28 col-a\">{{item.view_count}}人浏览</view>\n\t\t\t</view>\n\t\t</navigator>\n\t\t\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tname:\"scenery-list\",\n\t\tprops:{\n\t\t\tsceneryList:{\n\t\t\t\ttype: Array,\n\t\t\t\tdefault:[]\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t};\n\t\t}\n\t}\n</script>\n\n<style>\n\n</style>"], "sourceRoot": ""}