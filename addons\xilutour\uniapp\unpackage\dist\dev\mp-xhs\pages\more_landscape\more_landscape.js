(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/more_landscape/more_landscape"],{

/***/ 319:
/*!**************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/main.js?{"page":"pages%2Fmore_landscape%2Fmore_landscape"} ***!
  \**************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 3);
__webpack_require__(/*! uni-pages */ 25);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 24));
var _more_landscape = _interopRequireDefault(__webpack_require__(/*! ./pages/more_landscape/more_landscape.vue */ 320));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_more_landscape.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["createPage"]))

/***/ }),

/***/ 320:
/*!*******************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/more_landscape/more_landscape.vue ***!
  \*******************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _more_landscape_vue_vue_type_template_id_6b0d96b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./more_landscape.vue?vue&type=template&id=6b0d96b4&scoped=true& */ 321);
/* harmony import */ var _more_landscape_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./more_landscape.vue?vue&type=script&lang=js& */ 323);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _more_landscape_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _more_landscape_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 31);

var renderjs




/* normalize component */

var component = Object(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _more_landscape_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _more_landscape_vue_vue_type_template_id_6b0d96b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _more_landscape_vue_vue_type_template_id_6b0d96b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "6b0d96b4",
  null,
  false,
  _more_landscape_vue_vue_type_template_id_6b0d96b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/more_landscape/more_landscape.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 321:
/*!**************************************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/more_landscape/more_landscape.vue?vue&type=template&id=6b0d96b4&scoped=true& ***!
  \**************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_more_landscape_vue_vue_type_template_id_6b0d96b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./more_landscape.vue?vue&type=template&id=6b0d96b4&scoped=true& */ 322);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_more_landscape_vue_vue_type_template_id_6b0d96b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_more_landscape_vue_vue_type_template_id_6b0d96b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_more_landscape_vue_vue_type_template_id_6b0d96b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_more_landscape_vue_vue_type_template_id_6b0d96b4_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 322:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/more_landscape/more_landscape.vue?vue&type=template&id=6b0d96b4&scoped=true& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    sceneryList: function () {
      return __webpack_require__.e(/*! import() | components/scenery-list/scenery-list */ "components/scenery-list/scenery-list").then(__webpack_require__.bind(null, /*! @/components/scenery-list/scenery-list.vue */ 400))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 323:
/*!********************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/more_landscape/more_landscape.vue?vue&type=script&lang=js& ***!
  \********************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_more_landscape_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./more_landscape.vue?vue&type=script&lang=js& */ 324);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_more_landscape_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_more_landscape_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_more_landscape_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_more_landscape_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_more_landscape_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 324:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/more_landscape/more_landscape.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var sceneryList = function sceneryList() {
  __webpack_require__.e(/*! require.ensure | components/scenery-list/scenery-list */ "components/scenery-list/scenery-list").then((function () {
    return resolve(__webpack_require__(/*! @/components/scenery-list/scenery-list.vue */ 400));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var app = getApp();
var _default = {
  components: {
    sceneryList: sceneryList
  },
  data: function data() {
    return {
      statusBarHeight: 20,
      categoryList: [],
      bannerList: [],
      currentCity: null,
      sceneryList: [],
      sceneryListMore: {
        page: 1
      },
      query: {
        q: '',
        sort: 'weigh',
        order: 'desc',
        category_id: -1
      },
      searchTab: 0,
      isShowSelect: false
    };
  },
  onLoad: function onLoad(options) {
    this.statusBarHeight = getApp().globalData.statusBarHeight;
    if (options.category_id) {
      this.query.category_id = options.category_id;
    }
    if (options.tag_id) {
      this.query.tag_ids = options.tag_id;
    }
    var page = this;
    this.currentCity = this.$core.getCurrentCity();
    page.refreshPage();
    uni.$on(app.globalData.Event.CurrentCityChange, function (currentCity) {
      page.currentCity = currentCity;
      page.refresh();
    });
  },
  onShareAppMessage: function onShareAppMessage() {},
  onShareTimeline: function onShareTimeline() {},
  onReachBottom: function onReachBottom() {
    this.fetch();
  },
  onUnload: function onUnload() {
    uni.$off(app.globalData.Event.CurrentCityChange, this);
  },
  methods: {
    navBack: function navBack() {
      uni.navigateBack();
    },
    tabClick: function tabClick(id) {
      this.query.category_id = id;
      this.refresh();
    },
    //更换城市
    bindCityChange: function bindCityChange() {
      uni.navigateTo({
        url: '/pages/change_city/change_city'
      });
    },
    search: function search() {
      this.refresh();
    },
    //排序,0=综合（弹窗），1=价格，2=热门（弹窗)
    bindChangeSearchTab: function bindChangeSearchTab(searchTabIndex) {
      var _this = this;
      if (searchTabIndex == 0 || searchTabIndex == 2) {
        this.isShowSelect = this.isShowSelect && this.searchTab == searchTabIndex ? false : true;
      } else if (searchTabIndex == 1) {
        this.query.sort = 'salesprice';
        this.query.order = this.query.order == 'asc' ? 'desc' : 'asc';
        this.isShowSelect = false;
        this.refresh();
      } else if (searchTabIndex == 3) {
        var query = this.query;
        uni.navigateTo({
          url: '/pages/filtrate/filtrate?type=scenery',
          events: {
            searchSuccess: function searchSuccess(data) {
              _this.query = data;
              _this.refresh();
            }
          },
          success: function success(res) {
            res.eventChannel.emit("searchTransform", query);
          }
        });
      }
      this.searchTab = searchTabIndex;
    },
    //二级排序
    bindChangeSort: function bindChangeSort(sort) {
      this.query.sort = sort;
      this.query.order = 'desc';
      this.isShowSelect = false;
      this.refresh();
    },
    refreshPage: function refreshPage() {
      var _this2 = this;
      //轮播
      this.$core.get({
        url: 'xilutour.banner/index',
        data: {
          group: 'scenery_index'
        },
        loading: false,
        success: function success(ret) {
          _this2.bannerList = ret.data;
        }
      });
      //分类
      this.$core.get({
        url: 'xilutour.common/scenery_category',
        data: {},
        loading: false,
        success: function success(ret) {
          _this2.categoryList = ret.data;
        }
      });
      //列表
      this.refresh();
      uni.stopPullDownRefresh();
    },
    refresh: function refresh() {
      var cacheQuery = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
      this.sceneryList = [];
      this.sceneryListMore = {
        page: 1
      };
      if (cacheQuery) this.query = {};
      this.fetch();
    },
    fetch: function fetch() {
      var query = this.query;
      query.pagesize = 10;
      this.$util.fetch(this, 'xilutour.scenery/lists', query, 'sceneryListMore', 'sceneryList', 'data', function (data) {});
    },
    closeSelect: function closeSelect() {
      this.isShowSelect = false;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"]))

/***/ })

},[[319,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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