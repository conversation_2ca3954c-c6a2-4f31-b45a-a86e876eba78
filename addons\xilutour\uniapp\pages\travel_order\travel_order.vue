<template>
	<view class="xilu">
		<view class="page-head bg-white">
			<view class="g_tab">
				<view class="item" :class="{'active': tabIdx == -1}" @click="tabClick(-1)">全部</view>
				<view class="item" :class="{'active': tabIdx == 0}" @click="tabClick(0)">待付款</view>
				<view class="item" :class="{'active': tabIdx == 1}" @click="tabClick(1)">已支付</view>
				<view class="item" :class="{'active': tabIdx == 2}" @click="tabClick(2)">已完成</view>
				<view class="item" :class="{'active': tabIdx == 3}" @click="tabClick(3)">退款/取消</view>
			</view>
		</view>
		<view class="container">
			<view class="g_order1" v-for="(order,index) in tourList" :key="index">
				<view class="flex-box mb25">
					<view class="fs26 col-89">订单号</view>
					<view class="flex-1 fs26 col-5">{{order.order_no}}</view>
					<view class="fs30 " :class="order.state==0?'col-price':(order.state==1?'col-10':(order.state==2?'col-5':'col-5'))">{{order.state_text}}</view>
				</view>
				<view class="flex-box" @click="orderDetail(index)">
					<image class="img" :src="order.order_tour.thumb_image" mode="aspectFill"></image>
					<view class="flex-1">
						<view class="m-ellipsis fs36 col-10 mb20">{{order.order_tour.tour_name}}</view>
						<view class="flex-box col-3 mb35">
							<text class="fs24">¥</text>
							<text class="fs30 flex-1">{{order.order_tour.tour_date_salesprice}}</text>
							<view class="fs30 col-89">数量 {{order.total_count}}</view>
						</view>
						<view>
							<text class="fs30 col-89">实付款 </text>
							<text class="fs30 col-price">¥</text>
							<text class="fs40 col-price">{{order.pay_price}}</text>
						</view>
					</view>
				</view>
				<view class="pt30 flex-box flex-end" v-if="order.state==0">
					<view class="flex-1 col-price fs24">
						<view>请在{{order.expiretime_text}}前支付</view>
						<view>过期自动取消</view>
					</view>
					<view class="foot_btn1" @click="orderCancel(index)">取消订单</view>
					<view class="foot_btn2" @click="payment(order)">立即支付</view>
				</view>
				<view class="pt30 flex-box flex-end" v-else-if="order.state==1">
					<button class="foot_btn1" open-type="contact">联系客服</button>
				</view>
				<view class="pt30 flex-box flex-end" v-else-if="order.state==2 && order.comment_status==0" @click="bindComment(index)" >
					<button class="foot_btn1">立即评价</button>
				</view>

			</view>
			<view class="g-btn3-wrap">
				<view class="g-btn3" @click="fetch">{{tourListMore.text}}</view>
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabIdx: -1,
				tourList:[],
				tourListMore:{page:1}
			};
		},
		onLoad() {
			this.fetch();
		},
		methods: {
			tabClick(i) {
				this.tabIdx = i;
				this.refresh();
			},
			refresh(){
				this.tourList = [];
				this.tourListMore = {page:1};
				this.fetch()
			},
			fetch(){
				this.$util.fetch(this, 'xilutour.tour_order/lists', {state:this.tabIdx,pagesize:10}, 'tourListMore', 'tourList', 'data', data=>{
				  
				})
			},
			//订单详情
			orderDetail(index){
				let tourList = this.tourList;
				let order = tourList[index];
				uni.navigateTo({
					url: '/pages/travel_order_detail/travel_order_detail?id='+order.id,
					events:{
						
					}
				})
			},
			//取消
			orderCancel(index){
				let page = this;
				let tourList = this.tourList;
				uni.showModal({
					title:'提示',
					content: '确认取消订单？',
					success(res) {
						if(res.confirm){
							page.$core.post({url:'xilutour.tour_order/cancel',data:{order_id: tourList[index].id},loading:true,success:(ret)=>{
								page.refresh();
							 }});
						}
					}
				})
			},
			payment(order){
				//#ifdef MP-WEIXIN
				this.$core.post({url:'xilutour.pay/pay',data:{pay_type:1,order_id:order.id,platform:'wxmini'},success:(ret)=>{
					let wxconfig =  ret.data;
					this.$core.payment(wxconfig,function(){
						
					})
				}});
				//#endif
			},
			//评价
			bindComment(index){
				let orderList = this.tourList;
				let order = orderList[index];
				uni.navigateTo({
					url: '/pages/tour_evaluation/tour_evaluation',
					events:{
						commentSuccess: data=>{
							order.comment_status=1;
							this.tourList[index] = order;
						}
					},
					success(res) {
						res.eventChannel.emit("addComment",order)
					}
				})
			},
		}
	}
</script>

<style lang="less" scoped>


	.xilu {
		.page-head {
			height: 96rpx;
		}

		.container {
			padding: 126rpx 30rpx 30rpx;
			background: #F7F9FB;
		}
	}
</style>