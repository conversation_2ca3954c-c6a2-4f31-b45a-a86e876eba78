.xilu.data-v-ffd78488 {
  display: flex;
  width: 100vw;
  height: 100vh;
  flex-direction: column;
}
.xilu .change_city.data-v-ffd78488::after {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-width: 14rpx 10rpx 0;
  border-color: #999 transparent transparent;
  border-style: solid;
  margin-top: 8rpx;
}
.xilu .list_box.data-v-ffd78488 {
  position: relative;
  width: 100vw;
  flex: 1;
}
.xilu .city_item.data-v-ffd78488 {
  width: 190rpx;
  height: 65rpx;
  line-height: 65rpx;
  background: #F5F6F7;
  border-radius: 33rpx;
  margin-right: 30rpx;
  margin-bottom: 30rpx;
  font-size: 26rpx;
  color: #333;
}

