<template>
	<view>
		<view class="container p30">
			<view class="title">{{article.name}}</view>
			<view class="desc">{{article.createtime_text}}</view>
			<!-- <image src="../../static/img1.png" mode="widthFix" style="display: block;width: 100%;" class="mb20"></image> -->
			<view class="mb30"><rich-text :nodes="article.content"></rich-text></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				articleId:0,
				article:{name: '',view_num: 0, content:'',createtime_text:''}
			};
		},
		onLoad(options) {
			this.articleId = options.id;
			this.fetchDetail()
		},
		methods:{
			fetchDetail(){
				this.$core.get({url: 'xilutour.article/detail',data: {article_id:this.articleId},loading: false,success: ret => {
					ret.data.content = this.$core.richTextnew(ret.data.content);
					this.article = ret.data;
					},fail: err => {
						console.log(err);
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
.title{
	margin-bottom: 20rpx;
	font-size: 32rpx;
	color: #333333;
	line-height: 46rpx;
	text-align: justify;
	font-weight: bold;
}
.desc {
		margin-bottom:  20rpx;
		font-size: 26rpx;
		color: #666666;
		line-height: 34rpx;
		text-align: justify;
	}
</style>
