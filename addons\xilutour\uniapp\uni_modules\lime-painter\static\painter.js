!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Painter={})}(this,(function(t){"use strict";var e=function(){return(e=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)};function i(t,e,i,o){return new(i||(i=Promise))((function(n,r){function s(t){try{h(o.next(t))}catch(t){r(t)}}function a(t){try{h(o.throw(t))}catch(t){r(t)}}function h(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(s,a)}h((o=o.apply(t,e||[])).next())}))}function o(t,e){var i,o,n,r,s={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(r){return function(a){return function(r){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,o&&(n=2&r[0]?o.return:r[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,r[1])).done)return n;switch(o=0,n&&(r=[2&r[0],n.value]),r[0]){case 0:case 1:n=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(!(n=s.trys,(n=n.length>0&&n[n.length-1])||6!==r[0]&&2!==r[0])){s=0;continue}if(3===r[0]&&(!n||r[1]>n[0]&&r[1]<n[3])){s.label=r[1];break}if(6===r[0]&&s.label<n[1]){s.label=n[1],n=r;break}if(n&&s.label<n[2]){s.label=n[2],s.ops.push(r);break}n[2]&&s.ops.pop(),s.trys.pop();continue}r=e.call(t,s)}catch(t){r=[6,t],o=0}finally{i=n=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,a])}}}var n,r,s={MP_WEIXIN:"mp-weixin",MP_QQ:"mp-qq",MP_ALIPAY:"mp-alipay",MP_BAIDU:"mp-baidu",MP_TOUTIAO:"mp-toutiao",MP_DINGDING:"mp-dingding",H5:"h5",WEB:"web",PLUS:"plus"},a="view",h="text",d="image",l="qrcode",f="block",c="inline-block",u="none",g="absolute",v="fixed",p="transparent",b="fill",x={display:f,color:"#000000",lineHeight:"1.4em",fontSize:14,fontWeight:400,fontFamily:"sans-serif",lineCap:"butt",textAlign:"left",position:"static",transformOrigin:"center center"},y={upx2px:function(t){return window.innerWidth/750*t},getSystemInfoSync:function(){return{screenWidth:window.innerWidth}},getImageInfo:function(t){var e=t.src,i=t.success,o=t.fail,n=new Image;n.onload=function(){i({width:this.naturalWidth,height:this.naturalHeight,path:this.src,src:e})},n.onerror=o,n.src=e}},w="object",m="undefined",S=typeof window==w?typeof uni==m||typeof uni!==m&&!uni.addInterceptor?s.WEB:s.H5:typeof swan==w?s.MP_BAIDU:typeof tt==w?s.MP_TOUTIAO:typeof plus===w?s.PLUS:typeof wx==w?s.MP_WEIXIN:void 0,z=S==s.MP_WEIXIN?wx:typeof uni!=m?uni.getImageInfo?{upx2px:function(t){return uni.upx2px(t)},getSystemInfoSync:function(){return uni.getSystemInfoSync()},getImageInfo:function(t){return uni.getImageInfo(t)},downloadFile:function(t){return uni.downloadFile(t)}}:Object.assign(uni,y):typeof window!=m?y:uni;if(!z.upx2px){var B=(null!==(r=z.getSystemInfoSync&&(null===(n=uni.getSystemInfoSync())||void 0===n?void 0:n.screenWidth))&&void 0!==r?r:375)/750;z.upx2px=function(t){return B*t}}function M(t){return/^-?\d+(\.\d+)?$/.test(t)}function P(t,e,i){if("number"==typeof t)return t;if(M(t))return 1*t;if("string"==typeof t){var o=/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|px|%)$/g.exec(t);if(!t||!o)return 0;var n=o[3];t=parseFloat(t);var r=0;return"rpx"===n?r=z.upx2px(t):"px"===n?r=1*t:"%"===n&&e?r=t*P(e)/100:"em"===n&&e&&(r=t*P(e||14)),1*r.toFixed(2)}return 0}var I=function(t){return!(!t||!t.startsWith("linear")&&!t.startsWith("radial"))},L=function(t,e,i,o,n,r){t.startsWith("linear")?function(t,e,i,o,n,r){for(var s=function(t,e,i,o,n){void 0===o&&(o=0);void 0===n&&(n=0);var r=t.match(/([-]?\d{1,3})deg/),s=r&&r[1]?parseFloat(r[1]):0;s>=360&&(s-=360);s<0&&(s+=360);if(0===(s=Math.round(s)))return{x0:Math.round(e/2)+o,y0:i+n,x1:Math.round(e/2)+o,y1:n};if(180===s)return{x0:Math.round(e/2)+o,y0:n,x1:Math.round(e/2)+o,y1:i+n};if(90===s)return{x0:o,y0:Math.round(i/2)+n,x1:e+o,y1:Math.round(i/2)+n};if(270===s)return{x0:e+o,y0:Math.round(i/2)+n,x1:o,y1:Math.round(i/2)+n};var a=Math.round(180*Math.asin(e/Math.sqrt(Math.pow(e,2)+Math.pow(i,2)))/Math.PI);if(s===a)return{x0:o,y0:i+n,x1:e+o,y1:n};if(s===180-a)return{x0:o,y0:n,x1:e+o,y1:i+n};if(s===180+a)return{x0:e+o,y0:n,x1:o,y1:i+n};if(s===360-a)return{x0:e+o,y0:i+n,x1:o,y1:n};var h=0,d=0,l=0,f=0;if(s<a||s>180-a&&s<180||s>180&&s<180+a||s>360-a){var c=s*Math.PI/180,u=s<a||s>360-a?i/2:-i/2,g=Math.tan(c)*u,v=s<a||s>180-a&&s<180?e/2-g:-e/2-g;h=-(l=g+(p=Math.pow(Math.sin(c),2)*v)),d=-(f=u+p/Math.tan(c))}if(s>a&&s<90||s>90&&s<90+a||s>180+a&&s<270||s>270&&s<360-a){var p;c=(90-s)*Math.PI/180,g=s>a&&s<90||s>90&&s<90+a?e/2:-e/2,u=Math.tan(c)*g,v=s>a&&s<90||s>270&&s<360-a?i/2-u:-i/2-u;h=-(l=g+(p=Math.pow(Math.sin(c),2)*v)/Math.tan(c)),d=-(f=u+p)}return h=Math.round(h+e/2)+o,d=Math.round(i/2-d)+n,l=Math.round(l+e/2)+o,f=Math.round(i/2-f)+n,{x0:h,y0:d,x1:l,y1:f}}(n,t,e,i,o),a=s.x0,h=s.y0,d=s.x1,l=s.y1,f=r.createLinearGradient(a,h,d,l),c=n.match(/linear-gradient\((.+)\)/)[1],u=R(c.substring(c.indexOf(",")+1)),g=0;g<u.colors.length;g++)f.addColorStop(u.percents[g],u.colors[g]);r.setFillStyle(f)}(e,i,o,n,t,r):t.startsWith("radial")&&function(t,e,i,o,n,r){for(var s=R(n.match(/radial-gradient\((.+)\)/)[1]),a=Math.round(t/2)+i,h=Math.round(e/2)+o,d=r.createRadialGradient(a,h,0,a,h,Math.max(t,e)/2),l=0;l<s.colors.length;l++)d.addColorStop(s.percents[l],s.colors[l]);r.setFillStyle(d)}(e,i,o,n,t,r)};function R(t){for(var e=[],i=[],o=0,n=t.substring(0,t.length-1).split("%,");o<n.length;o++){var r=n[o];e.push(r.substring(0,r.lastIndexOf(" ")).trim()),i.push(r.substring(r.lastIndexOf(" "),r.length)/100)}return{colors:e,percents:i}}function W(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function k(){return(k=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o])}return t}).apply(this,arguments)}var T=d,O=h,A=a,C=f,F=c,j=g,H=v,$=0,E={left:null,top:null,width:null,height:null},U=function(){function t(t,e,i,o){var n=this;W(this,"id",$++),W(this,"style",{left:null,top:null,width:null,height:null}),W(this,"computedStyle",{}),W(this,"originStyle",{}),W(this,"children",{}),W(this,"layoutBox",k({},E)),W(this,"contentSize",k({},E,{maxLineHeight:0})),W(this,"clientSize",k({},E)),W(this,"borderSize",k({},E)),W(this,"offsetSize",k({},E)),this.ctx=o,this.root=i,e&&(this.parent=e),this.name=t.name||t.type,this.attributes=this.getAttributes(t);var r=this.getComputedStyle(t,null==e?void 0:e.computedStyle);this.isAbsolute=r.position==j,this.isFixed=r.position==H,this.originStyle=r,Object.keys(r).forEach((function(t){Object.defineProperty(n.style,t,{configurable:!0,enumerable:!0,get:function(){return r[t]},set:function(e){r[t]=e}})}));var s={contentSize:k({},this.contentSize),clientSize:k({},this.clientSize),borderSize:k({},this.borderSize),offsetSize:k({},this.offsetSize)};Object.keys(s).forEach((function(t){Object.keys(n[t]).forEach((function(e){Object.defineProperty(n[t],e,{configurable:!0,enumerable:!0,get:function(){return s[t][e]},set:function(i){s[t][e]=i}})}))})),this.computedStyle=this.style}var e=t.prototype;return e.add=function(t){t.parent=this,this.children[t.id]=t},e.getChildren=function(){var t=this;return Object.keys(this.children).map((function(e){return t.children[e]}))},e.getComputedStyle=function(t,e){var i=["color","fontSize","lineHeight","verticalAlign","fontWeight","textAlign"],o=t.css,n=void 0===o?{}:o,r=t.type,s=void 0===r?A:r,a=k({},x);if(["text","image","qrcode"].includes(s)&&!n.display&&(a.display="inline-block"),e)for(var h=0;h<i.length;h++){var d=i[h];(n[d]||e[d])&&(n[d]=n[d]||e[d])}for(var l=function(){var t=c[f];/-/.test(t)&&console.error(t,"请使用驼峰");var e=n[t];if(/^(box)?shadow$/i.test(t)){var i=[];return e.replace(/((\d+(rpx|px)?\s+?){3})(.+)/,(function(){for(var t=arguments.length,e=new Array(t),o=0;o<t;o++)e[o]=arguments[o];i=e[1].match(/\d+(rpx|px)?/g).map((function(t){return P(t)})).concat(e[4])})),a.boxShadow=i,"continue"}if(/^border/i.test(t)&&!/radius$/i.test(t)){var o,r=t.match(/^border([BTRLa-z]+)?/)[0],h=t.match(/[W|S|C][a-z]+/),d=e.replace(/([\(,])\s+|\s+([\),])/g,"$1$2").split(" ").map((function(t){return/^\d/.test(t)?P(t,""):t}));return a[r]=((o={})[r+"Width"]=M(d[0])?d[0]:0,o[r+"Style"]=d[1]||"solid",o[r+"Color"]=d[2]||"black",o),1==d.length&&h&&(a[r][r+h[0]]=d[0]),"continue"}if(/^background(color)?$/i.test(t))return a.backgroundColor=e,"continue";if(/^objectPosition$/i.test(t))return a[t]=e.split(" "),"continue";if(/padding|margin|radius/i.test(t)){var l=/radius$/i.test(t),u=l?"borderRadius":t.match(/[a-z]+/)[0],g=[0,0,0,0].map((function(t,e){return l?["borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"][e]:[u+"Top",u+"Right",u+"Bottom",u+"Left"][e]})),v="margin",p="auto";if("padding"===t||t===v||/^(border)?radius$/i.test(t)){var b,x=(null==e?void 0:e.split(" ").map((function(e){return/^\d+(rpx|px)?$/.test(e)?P(e):t!=v&&/auto/.test(e)?0:e}),[]))||[0],y=l?"borderRadius":t,w=x[0],m=x[1],S=x[2],z=x[3];a[y]=((b={})[g[0]]=w==p?0:w,b[g[1]]=M(m)?m:w,b[g[2]]=(M(S)?S:w)==p?0:M(S)?S:w,b[g[3]]=M(z)?z:m||w,b)}else{var B;if("object"==typeof a[u])a[u][t]=u==v&&e==p||/%$/.test(e)?e:P(e);else a[u]=((B={})[g[0]]=a[u]||0,B[g[1]]=a[u]||0,B[g[2]]=a[u]||0,B[g[3]]=a[u]||0,B),a[u][t]=u==v&&e==p||/%$/.test(e)?e:P(e)}return"continue"}if(/^transform$/i.test(t))return a[t]={},e.replace(/([a-zA-Z]+)\(([0-9,-\.%rpxdeg\s]+)\)/g,(function(e,i,o){var r=o.split(",").map((function(t){return t.replace(/(^\s*)|(\s*$)/g,"")})),s=function(t,e){return t.includes("deg")?1*t:e&&!/%$/.test(e)?P(t,e):t};i.includes("matrix")?a[t][i]=r.map((function(t){return 1*t})):i.includes("rotate")?a[t][i]=1*o.match(/^-?\d+(\.\d+)?/)[0]:/[X, Y]/.test(i)?a[t][i]=/[X]/.test(i)?s(r[0],n.width):s(r[0],n.height):(a[t][i+"X"]=s(r[0],n.width),a[t][i+"Y"]=s(r[1]||r[0],n.height))})),"continue";/^font$/i.test(t)&&console.error("font 不支持简写"),/^left|top$/i.test(t)&&![j,H].includes(n.position)?a[t]=0:a[t]=/^[\d\.]+(px|rpx)?$/.test(e)?P(e):/em$/.test(e)&&"text"==s?P(e,n.fontSize):e},f=0,c=Object.keys(n);f<c.length;f++)l();return a},e.setPosition=function(t,e){var i={left:"width",top:"height",right:"width",bottom:"height"};Object.keys(i).forEach((function(o){var n="right"==o?"left":"top";["right","bottom"].includes(o)&&void 0!==t.style[o]&&"number"!=typeof t.originStyle[n]?t.style[n]=e[i[o]]-t.offsetSize[i[o]]-P(t.style[o],e[i[o]]):t.style[o]=P(t.style[o],e[i[o]])}))},e.getAttributes=function(t){var e=t.attributes||{};return(t.url||t.src)&&(e.src=e.src||t.url||t.src),t.replace&&(e.replace=t.replace),t.text&&(e.text=t.text),e},e.getOffsetSize=function(t,e,i){void 0===i&&(i="offsetSize");var o=e||{},n=o.margin,r=(n=void 0===n?{}:n).marginLeft,s=void 0===r?0:r,a=n.marginTop,h=void 0===a?0:a,d=n.marginRight,l=void 0===d?0:d,f=n.marginBottom,c=void 0===f?0:f,u=o.padding,g=(u=void 0===u?{}:u).paddingLeft,v=void 0===g?0:g,p=u.paddingTop,b=void 0===p?0:p,x=u.paddingRight,y=void 0===x?0:x,w=u.paddingBottom,m=void 0===w?0:w,S=o.border,z=(S=void 0===S?{}:S).borderWidth,B=void 0===z?0:z,M=o.borderTop,P=(M=void 0===M?{}:M).borderTopWidth,I=void 0===P?B:P,L=o.borderBottom,R=(L=void 0===L?{}:L).borderBottomWidth,W=void 0===R?B:R,k=o.borderRight,T=(k=void 0===k?{}:k).borderRightWidth,O=void 0===T?B:T,A=o.borderLeft,C=(A=void 0===A?{}:A).borderLeftWidth,F=void 0===C?B:C,j=s<0&&l<0?Math.abs(s+l):0,H=h<0&&c<0?Math.abs(h+c):0,$=s>=0&&l<0,E=h>=0&&c<0;return"contentSize"==i&&(this[i].left=t.left+s+v+F+($?2*-l:0),this[i].top=t.top+h+b+I+(E?2*-c:0),this[i].width=t.width+(this[i].widthAdd?0:j),this[i].height=t.height+(this[i].heightAdd?0:H),this[i].widthAdd=j,this[i].heightAdd=H),"clientSize"==i&&(this[i].left=t.left+s+F+($<0?-l:0),this[i].top=t.top+h+I+(E?-c:0),this[i].width=t.width+v+y,this[i].height=t.height+b+m),"borderSize"==i&&(this[i].left=t.left+s+F/2+($<0?-l:0),this[i].top=t.top+h+I/2+(E?-c:0),this[i].width=t.width+v+y+F/2+O/2,this[i].height=t.height+b+m+W/2+I/2),"offsetSize"==i&&(this[i].left=t.left+($<0?-l:0),this[i].top=t.top+(E?-c:0),this[i].width=t.width+v+y+F+O+s+l,this[i].height=t.height+b+m+W+I+c+h),this[i]},e.layoutBoxUpdate=function(t,e,i,o){if(void 0===i&&(i=-1),"border-box"==e.boxSizing){var n=e||{},r=n.border,s=(r=void 0===r?{}:r).borderWidth,a=void 0===s?0:s,h=n.borderTop,d=(h=void 0===h?{}:h).borderTopWidth,l=void 0===d?a:d,f=n.borderBottom,c=(f=void 0===f?{}:f).borderBottomWidth,u=void 0===c?a:c,g=n.borderRight,v=(g=void 0===g?{}:g).borderRightWidth,p=void 0===v?a:v,b=n.borderLeft,x=(b=void 0===b?{}:b).borderLeftWidth,y=void 0===x?a:x,w=n.padding,m=(w=void 0===w?{}:w).paddingTop,S=void 0===m?0:m,z=w.paddingRight,B=void 0===z?0:z,M=w.paddingBottom,P=void 0===M?0:M,I=w.paddingLeft,L=void 0===I?0:I;i||(t.width-=L+B+p+y),1!==i||o||(t.height-=S+P+l+u)}this.layoutBox&&(this.layoutBox.contentSize=this.getOffsetSize(t,e,"contentSize"),this.layoutBox.clientSize=this.getOffsetSize(t,e,"clientSize"),this.layoutBox.borderSize=this.getOffsetSize(t,e,"borderSize"),this.layoutBox.offsetSize=this.getOffsetSize(t,e,"offsetSize"),this.layoutBox=Object.assign({},this.layoutBox,this.layoutBox.borderSize))},e.getBoxPosition=function(t){var e=this.computedStyle,i=this.getChildren(),o=e.verticalAlign,n=e.left,r=void 0===n?0:n,s=e.top,a=void 0===s?0:s,h=e.textAlign,d=k({},this.contentSize,{left:r,top:a}),l=this.contentSize.top-this.offsetSize.top,f=this.contentSize.left-this.offsetSize.left,c=0;if("bottom"==o&&this.contentSize.maxLineHeight?c=this.contentSize.maxLineHeight-this.contentSize.height:"middle"==o&&this.contentSize.maxLineHeight&&(c=(this.contentSize.maxLineHeight-this.contentSize.height)/2),d.top+=c,i.length){r+=f,a+=l;for(var u=null,g=null,v=!1,p=0,b=0;b<i.length;b++){var x=i[b];if(x.isAbsolute||x.isFixed)p++,x.isAbsolute?(x.setPosition(x,d),x.style.left+=r,x.style.top+=a,x.getBoxPosition()):(x.setPosition(x,this.root),x.getBoxPosition());else{var y=b-p,w=0;if(h&&this.isInline(x)){var m=this.contentSize.width-x.contentSize.maxLineWidth||0;"center"==h&&m>1&&(w=m/2),"right"==h&&m>1&&(w=m)}if(0==y)x.style.left+=r+w,x.style.top+=a,x.getBoxPosition(),u=x,g=x;else{var S,z,B,M;(null==(S=g)?void 0:S.offsetSize.height)<(null==(z=u)?void 0:z.offsetSize.height)&&(g=u);var P=(null==(B=u)?void 0:B.offsetSize.left)+(null==(M=u)?void 0:M.offsetSize.width)+x.offsetSize.width>d.left+d.width+f;if(this.getBoxState(u,x)||P){var I,L,R,W,T,O;if(x.style.left+=r+w,(null==(I=u)?void 0:I.offsetSize.height)>=(null==(L=g)?void 0:L.offsetSize.height))x.style.top+=(null==(R=u)?void 0:R.offsetSize.top)+(null==(W=u)?void 0:W.offsetSize.height)||0;else x.style.top+=(null==(T=g)?void 0:T.offsetSize.top)+(null==(O=g)?void 0:O.offsetSize.height)||0;x.getBoxPosition(),u=x,g=x,v=!0}else{var A,C,F;if(x.style.left+=(null==(A=u)?void 0:A.offsetSize.left)+(null==(C=u)?void 0:C.offsetSize.width)||0,v)x.style.top+=null==(F=u)?void 0:F.offsetSize.top;else x.style.top+=a;x.getBoxPosition(),u=x}}}}this.layoutBoxUpdate(d,e)}else this.layoutBoxUpdate(d,e);return this.layoutBox},e.setMaxLineHeight=function(t,e,i){for(var o=t;o>=0&&!e[o].contentSize.maxLineHeight;)e[o].contentSize.maxLineHeight=i,o--},e.setMaxLineWidth=function(t,e,i){for(var o=t;o>=0&&(null==(n=e[o])||null==(r=n.contentSize)||!r.maxLineWidth);){var n,r;e[o].contentSize.maxLineWidth=i,o--}},e.getBoxState=function(t,e){return this.isBlock(t)||this.isBlock(e)},e.isBlock=function(t){return t&&t.style.display==C},e.isInline=function(t){return!this.isBlock(t)},e.getBoxHieght=function(){var t,e=this,i=this.name,o=this.computedStyle,n=this.attributes,r=this.parent,s=this.getChildren(),a=o.top,h=o.bottom,d=o.height,l=void 0===d?0:d,f=o.fontSize,c=o.position,u=o.lineHeight,g=o.minHeight,v=o.maxHeight,p=k({},this.contentSize);if(/%$/.test(g)&&r.contentSize.height&&(g=P(g,r.contentSize.height)),/%$/.test(v)&&r.contentSize.height&&(v=P(v,r.contentSize.height)),i==T&&null==l){var b=n.width,x=n.height;n.mode,p.height=this.contrastSize(Math.round(p.width*x/b)||0,g,v),this.layoutBoxUpdate(p,o,1)}else if(l)if(s.length){p.height=this.contrastSize(p.height,g,v),this.layoutBoxUpdate(p,o);var y=null,w=0,m=0;s.forEach((function(t,i){var o=i==s.length-1;if(t.getBoxHieght(),t.isAbsolute||t.isFixed);else{var n=w+t.offsetSize.width>p.left+p.width,r=e.getBoxState(y,t);if(n||r){if(n){for(var a=i-1;a>=0&&!s[a].contentSize.maxLineHeight;)m<s[a].contentSize.height&&(m=s[a].contentSize.height),a--;e.setMaxLineHeight(i-1,s,m),e.setMaxLineWidth(i-1,s,w),m=0,w=t.offsetSize.width}o&&e.setMaxLineWidth(i,s,t.offsetSize.width)}else{if(w+=t.offsetSize.width,o){for(var h=i;h>=0&&!s[h].contentSize.maxLineHeight;)m<s[h].contentSize.height&&(m=s[h].contentSize.height),h--;e.setMaxLineHeight(i,s,m),e.setMaxLineWidth(i,s,w),m=0,w=t.offsetSize.width}y=t}}}))}else p.height=this.contrastSize(p.height,g,v),this.layoutBoxUpdate(p,o,1);else{var S=0;if(null!=a?a:this.isAbsolute||this.isFixed&&r.contentSize.height){var z="absolute"==c?r.contentSize.height:this.root.height;S=z-(/%$/.test(a)?P(a,z):a)-(/%$/.test(h)?P(h,z):h)}if(i==O)u=P(u,f),p.height=S||this.contrastSize(this.attributes.lines*u,g,v),this.layoutBoxUpdate(p,o,1,!0);else if(s.length){var B=0,M=null,I=0;p.height=s.reduce((function(t,i,o){var n=o==s.length-1;if(i.isAbsolute||i.isFixed)return i.getBoxHieght(),n?t+B:t;i.getBoxHieght();var r=e.getBoxState(M,i),a=I+i.offsetSize.width>p.width;if(a||r){var h=0;return a||M&&e.isInline(M)?(e.setMaxLineHeight(o-1,s,B),e.setMaxLineWidth(o-1,s,I),n&&(e.setMaxLineHeight(o,s,B),e.setMaxLineWidth(o,s,i.offsetSize.width),B+=i.offsetSize.height),h=t+B,B=i.offsetSize.height,I=i.offsetSize.width,M=i,h):(I=0,B=0,t+i.offsetSize.height)}return I+=i.offsetSize.width,B=Math.max(B,i.offsetSize.height)||0,n?(e.setMaxLineHeight(o,s,B),e.setMaxLineWidth(o,s,I),t+B):(M=i,t)}),0),S&&(p.height=S),this.layoutBoxUpdate(p,o)}else S&&(p.height=S),this.layoutBoxUpdate(p,o,1)}if(o.borderRadius&&null!=(t=this.borderSize)&&t.width)for(var L in o.borderRadius)Object.hasOwnProperty.call(o.borderRadius,L)&&(o.borderRadius[L]=P(o.borderRadius[L],this.borderSize.width));return this.layoutBox},e.contrastSize=function(t,e,i){var o=t;return i&&(o=Math.min(o,i)),e&&(o=Math.max(o,e)),o},e.measureText=function(t,e){var i=this.ctx.measureText(t);return{width:i.width,fontHeight:(i.actualBoundingBoxAscent||.7*e)+1}},e.getBoxWidth=function(){var t,e=this,i=this.name,o=this.computedStyle,n=this.attributes,r=this.parent,s=void 0===r?{}:r,a=this.ctx,h=this.getChildren(),d=o.left,l=void 0===d?0:d,f=o.top,c=void 0===f?0:f,u=o.right,g=o.width,v=void 0===g?0:g,p=o.minWidth,b=o.maxWidth,x=o.height,y=void 0===x?0:x,w=o.fontSize,m=void 0===w?14:w,S=o.fontWeight,z=o.fontFamily,B=o.fontStyle,M=o.position,I=o.display,L=o.lineClamp,R=o.padding,W=void 0===R?{}:R,k=o.margin,j=void 0===k?{}:k,H=o.border,$=(H=void 0===H?{}:H).borderWidth,E=void 0===$?0:$,U=o.borderRight,_=(U=void 0===U?{}:U).borderRightWidth,D=void 0===_?E:_,N=o.borderLeft,X=(N=void 0===N?{}:N).borderLeftWidth,G=void 0===X?E:X;if(/%$/.test(v)&&s.contentSize.width&&(v=P(v,s.contentSize.width)),/%$/.test(y)&&s.contentSize.height&&(y=P(y,s.contentSize.height)),/%$/.test(p)&&s.contentSize.width&&(p=P(p,s.contentSize.width)),/%$/.test(b)&&s.contentSize.width&&(b=P(b,s.contentSize.width)),o.padding&&null!=(t=s.contentSize)&&t.width)for(var q in o.padding)Object.hasOwnProperty.call(o.padding,q)&&(o.padding[q]=P(o.padding[q],s.contentSize.width));var V=W.paddingRight,Y=void 0===V?0:V,J=W.paddingLeft,Q=void 0===J?0:J;if(o.margin&&[o.margin.marginLeft,o.margin.marginRight].includes("auto"))if(v){var Z=s.contentSize.width&&s.contentSize.width-v-Y-Q-G-D||0;o.margin.marginLeft==o.margin.marginRight?o.margin.marginLeft=o.margin.marginRight=Z/2:"auto"==o.margin.marginLeft?o.margin.marginLeft=Z:o.margin.marginRight=Z}else o.margin.marginLeft=o.margin.marginRight=0;var K=j.marginRight,tt=void 0===K?0:K,et=j.marginLeft,it={width:v,height:y,left:0,top:0},ot=Q+Y+G+D+(void 0===et?0:et)+tt;if(i==O&&!this.attributes.widths){var nt=n.text||"";a.save(),a.setFonts({fontFamily:z,fontSize:m,fontWeight:S,fontStyle:B});var rt=new Map;nt.split("\n").map((function(t){var i=t.split("").map((function(t){var i=rt.get(t);if(i)return i;var o=e.measureText(t,m).width;return rt.set(t,o),o}));e.attributes.fontHeight=e.measureText(t,m).fontHeight,e.attributes.widths||(e.attributes.widths=[]),e.attributes.widths.push({widths:i,total:i.reduce((function(t,e){return t+e}),0)})})),a.restore()}if(i==T&&null==v){var st=n.width,at=n.height;it.width=this.contrastSize(Math.round(st*y/at)||0,p,b),this.layoutBoxUpdate(it,o,0)}else if(v)h.length?(it.width=this.contrastSize(it.width,p,b),this.layoutBoxUpdate(it,o,0),h.forEach((function(t){t.getBoxWidth()}))):(it.width=this.contrastSize(it.width,p,b),this.layoutBoxUpdate(it,o,0));else{var ht=0;if((this.isAbsolute||this.isFixed)&&s.contentSize.width&&i!=O){var dt="absolute"==M?s.contentSize.width:this.root.width;ht=dt-(/%$/.test(l)?P(l,dt):l)-(/%$/.test(u)?P(u,dt):u)}if(i==O){var lt=this.attributes.widths,ft=Math.max.apply(Math,lt.map((function(t){return t.total})));if(s&&s.contentSize.width>0&&(ft>s.contentSize.width||I==C)&&!this.isAbsolute&&!this.isFixed)ft=s.contentSize.width-ot;it.width=ht||this.contrastSize(ft,p,b),this.layoutBoxUpdate(it,o,0)}else if(i==A&&s&&s.children&&s.contentSize.width&&I!==F&&!this.isAbsolute&&!this.isFixed)it.width=this.contrastSize(s.contentSize.width-ot,p,b),this.layoutBoxUpdate(it,o),h.length&&h.forEach((function(t){t.getBoxWidth()}));else if(h.length){for(var ct=0,ut=null,gt=0;h.length>gt;){var vt=h[gt],pt=gt==h.length-1,bt=this.getBoxState(ut,vt);if(vt.isFixed||vt.isAbsolute)vt.getBoxWidth();else if(!ut||bt){var xt=vt.getBoxWidth();ct=Math.max(ct,xt.offsetSize.width)||0,ut=vt}else if(s.contentSiz&&s.contentSize.width&&ut.offsetSize.left+ut.offsetSize.width+vt.offsetSize.width<s.contentSize.width&&gt!==h.length-1){ct+=vt.getBoxWidth().offsetSize.width,ut=vt}else{var yt=vt.getBoxWidth();pt?(ct+=yt.offsetSize.width,ut=null):s.contentSize&&s.contentSize.width?ct=s.contentSize.width:ct+=yt.offsetSize.width}gt++}it.width=ht&&c?ht:this.contrastSize(Math.ceil(ct),p,b),h.forEach((function(t){t.style.display!=C||t.name!=O||t.isFixed||t.isAbsolute||t.style.width||(t.style.width=it.width,t.getBoxWidth())})),this.layoutBoxUpdate(it,o,0)}else it.width=ht,this.layoutBoxUpdate(it,o,0)}if(i==O&&!this.attributes.lines){var wt=this.attributes.widths.length;this.attributes.widths.forEach((function(t){return t.widths.reduce((function(t,e,i){return t+e>it.width?(wt++,e):t+e}),0)})),wt=L&&wt>L?L:wt,this.attributes.lines=wt}return this.layoutBox},e.layout=function(){return this.getBoxWidth(),this.getBoxHieght(),this.getBoxPosition(),this.offsetSize},t}(),_=function(){var t,e,i,o,n,r,s=[0,11,15,19,23,27,31,16,18,20,22,24,26,28,20,22,24,24,26,28,28,22,24,24,26,26,28,28,24,24,26,26,26,28,28,24,26,26,26,28,28],a=[3220,1468,2713,1235,3062,1890,2119,1549,2344,2936,1117,2583,1330,2470,1667,2249,2028,3780,481,4011,142,3098,831,3445,592,2517,1776,2234,1951,2827,1070,2660,1345,3177],h=[30660,29427,32170,30877,26159,25368,27713,26998,21522,20773,24188,23371,17913,16590,20375,19104,13663,12392,16177,14854,9396,8579,11994,11245,5769,5054,7399,6608,1890,597,3340,2107],d=[1,0,19,7,1,0,16,10,1,0,13,13,1,0,9,17,1,0,34,10,1,0,28,16,1,0,22,22,1,0,16,28,1,0,55,15,1,0,44,26,2,0,17,18,2,0,13,22,1,0,80,20,2,0,32,18,2,0,24,26,4,0,9,16,1,0,108,26,2,0,43,24,2,2,15,18,2,2,11,22,2,0,68,18,4,0,27,16,4,0,19,24,4,0,15,28,2,0,78,20,4,0,31,18,2,4,14,18,4,1,13,26,2,0,97,24,2,2,38,22,4,2,18,22,4,2,14,26,2,0,116,30,3,2,36,22,4,4,16,20,4,4,12,24,2,2,68,18,4,1,43,26,6,2,19,24,6,2,15,28,4,0,81,20,1,4,50,30,4,4,22,28,3,8,12,24,2,2,92,24,6,2,36,22,4,6,20,26,7,4,14,28,4,0,107,26,8,1,37,22,8,4,20,24,12,4,11,22,3,1,115,30,4,5,40,24,11,5,16,20,11,5,12,24,5,1,87,22,5,5,41,24,5,7,24,30,11,7,12,24,5,1,98,24,7,3,45,28,15,2,19,24,3,13,15,30,1,5,107,28,10,1,46,28,1,15,22,28,2,17,14,28,5,1,120,30,9,4,43,26,17,1,22,28,2,19,14,28,3,4,113,28,3,11,44,26,17,4,21,26,9,16,13,26,3,5,107,28,3,13,41,26,15,5,24,30,15,10,15,28,4,4,116,28,17,0,42,26,17,6,22,28,19,6,16,30,2,7,111,28,17,0,46,28,7,16,24,30,34,0,13,24,4,5,121,30,4,14,47,28,11,14,24,30,16,14,15,30,6,4,117,30,6,14,45,28,11,16,24,30,30,2,16,30,8,4,106,26,8,13,47,28,7,22,24,30,22,13,15,30,10,2,114,28,19,4,46,28,28,6,22,28,33,4,16,30,8,4,122,30,22,3,45,28,8,26,23,30,12,28,15,30,3,10,117,30,3,23,45,28,4,31,24,30,11,31,15,30,7,7,116,30,21,7,45,28,1,37,23,30,19,26,15,30,5,10,115,30,19,10,47,28,15,25,24,30,23,25,15,30,13,3,115,30,2,29,46,28,42,1,24,30,23,28,15,30,17,0,115,30,10,23,46,28,10,35,24,30,19,35,15,30,17,1,115,30,14,21,46,28,29,19,24,30,11,46,15,30,13,6,115,30,14,23,46,28,44,7,24,30,59,1,16,30,12,7,121,30,12,26,47,28,39,14,24,30,22,41,15,30,6,14,121,30,6,34,47,28,46,10,24,30,2,64,15,30,17,4,122,30,29,14,46,28,49,10,24,30,24,46,15,30,4,18,122,30,13,32,46,28,48,14,24,30,42,32,15,30,20,4,117,30,40,7,47,28,43,22,24,30,10,67,15,30,19,6,118,30,18,31,47,28,34,34,24,30,20,61,15,30],l=[255,0,1,25,2,50,26,198,3,223,51,238,27,104,199,75,4,100,224,14,52,141,239,129,28,193,105,248,200,8,76,113,5,138,101,47,225,36,15,33,53,147,142,218,240,18,130,69,29,181,194,125,106,39,249,185,201,154,9,120,77,228,114,166,6,191,139,98,102,221,48,253,226,152,37,179,16,145,34,136,54,208,148,206,143,150,219,189,241,210,19,92,131,56,70,64,30,66,182,163,195,72,126,110,107,58,40,84,250,133,186,61,202,94,155,159,10,21,121,43,78,212,229,172,115,243,167,87,7,112,192,247,140,128,99,13,103,74,222,237,49,197,254,24,227,165,153,119,38,184,180,124,17,68,146,217,35,32,137,46,55,63,209,91,149,188,207,205,144,135,151,178,220,252,190,97,242,86,211,171,20,42,93,158,132,60,57,83,71,109,65,162,31,45,67,216,183,123,164,118,196,23,73,236,127,12,111,246,108,161,59,82,41,157,85,170,251,96,134,177,187,204,62,90,203,89,95,176,156,169,160,81,11,245,22,235,122,117,44,215,79,174,213,233,230,231,173,232,116,214,244,234,168,80,88,175],f=[1,2,4,8,16,32,64,128,29,58,116,232,205,135,19,38,76,152,45,90,180,117,234,201,143,3,6,12,24,48,96,192,157,39,78,156,37,74,148,53,106,212,181,119,238,193,159,35,70,140,5,10,20,40,80,160,93,186,105,210,185,111,222,161,95,190,97,194,153,47,94,188,101,202,137,15,30,60,120,240,253,231,211,187,107,214,177,127,254,225,223,163,91,182,113,226,217,175,67,134,17,34,68,136,13,26,52,104,208,189,103,206,129,31,62,124,248,237,199,147,59,118,236,197,151,51,102,204,133,23,46,92,184,109,218,169,79,158,33,66,132,21,42,84,168,77,154,41,82,164,85,170,73,146,57,114,228,213,183,115,230,209,191,99,198,145,63,126,252,229,215,179,123,246,241,255,227,219,171,75,150,49,98,196,149,55,110,220,165,87,174,65,130,25,50,100,200,141,7,14,28,56,112,224,221,167,83,166,81,162,89,178,121,242,249,239,195,155,43,86,172,69,138,9,18,36,72,144,61,122,244,245,247,243,251,235,203,139,11,22,44,88,176,125,250,233,207,131,27,54,108,216,173,71,142,0],c=[],u=[],g=[],v=[],p=[],b=2;function x(t,e){var i;t>e&&(i=t,t=e,e=i),i=e,i*=e,i+=e,i>>=1,v[i+=t]=1}function y(t,i){var o;for(g[t+e*i]=1,o=-2;o<2;o++)g[t+o+e*(i-2)]=1,g[t-2+e*(i+o+1)]=1,g[t+2+e*(i+o)]=1,g[t+o+1+e*(i+2)]=1;for(o=0;o<2;o++)x(t-1,i+o),x(t+1,i-o),x(t-o,i-1),x(t+o,i+1)}function w(t){for(;t>=255;)t=((t-=255)>>8)+(255&t);return t}var m=[];function S(t,e,i,o){var n,r,s;for(n=0;n<o;n++)c[i+n]=0;for(n=0;n<e;n++){if(255!=(s=l[c[t+n]^c[i]]))for(r=1;r<o;r++)c[i+r-1]=c[i+r]^f[w(s+m[o-r])];else for(r=i;r<i+o;r++)c[r]=c[r+1];c[i+o-1]=255==s?0:f[w(s+m[0])]}}function z(t,e){var i;return t>e&&(i=t,t=e,e=i),i=e,i+=e*e,i>>=1,v[i+=t]}function B(t){var i,o,n,r;switch(t){case 0:for(o=0;o<e;o++)for(i=0;i<e;i++)i+o&1||z(i,o)||(g[i+o*e]^=1);break;case 1:for(o=0;o<e;o++)for(i=0;i<e;i++)1&o||z(i,o)||(g[i+o*e]^=1);break;case 2:for(o=0;o<e;o++)for(n=0,i=0;i<e;i++,n++)3==n&&(n=0),n||z(i,o)||(g[i+o*e]^=1);break;case 3:for(r=0,o=0;o<e;o++,r++)for(3==r&&(r=0),n=r,i=0;i<e;i++,n++)3==n&&(n=0),n||z(i,o)||(g[i+o*e]^=1);break;case 4:for(o=0;o<e;o++)for(n=0,r=o>>1&1,i=0;i<e;i++,n++)3==n&&(n=0,r=!r),r||z(i,o)||(g[i+o*e]^=1);break;case 5:for(r=0,o=0;o<e;o++,r++)for(3==r&&(r=0),n=0,i=0;i<e;i++,n++)3==n&&(n=0),(i&o&1)+!(!n|!r)||z(i,o)||(g[i+o*e]^=1);break;case 6:for(r=0,o=0;o<e;o++,r++)for(3==r&&(r=0),n=0,i=0;i<e;i++,n++)3==n&&(n=0),(i&o&1)+(n&&n==r)&1||z(i,o)||(g[i+o*e]^=1);break;case 7:for(r=0,o=0;o<e;o++,r++)for(3==r&&(r=0),n=0,i=0;i<e;i++,n++)3==n&&(n=0),(n&&n==r)+(i+o&1)&1||z(i,o)||(g[i+o*e]^=1)}}function M(t){var e,i=0;for(e=0;e<=t;e++)p[e]>=5&&(i+=3+p[e]-5);for(e=3;e<t-1;e+=2)p[e-2]==p[e+2]&&p[e+2]==p[e-1]&&p[e-1]==p[e+1]&&3*p[e-1]==p[e]&&(0==p[e-3]||e+3>t||3*p[e-3]>=4*p[e]||3*p[e+3]>=4*p[e])&&(i+=40);return i}function P(){var t,i,o,n,r,s=0,a=0;for(i=0;i<e-1;i++)for(t=0;t<e-1;t++)(g[t+e*i]&&g[t+1+e*i]&&g[t+e*(i+1)]&&g[t+1+e*(i+1)]||!(g[t+e*i]||g[t+1+e*i]||g[t+e*(i+1)]||g[t+1+e*(i+1)]))&&(s+=3);for(i=0;i<e;i++){for(p[0]=0,o=n=t=0;t<e;t++)(r=g[t+e*i])==n?p[o]++:p[++o]=1,a+=(n=r)?1:-1;s+=M(o)}a<0&&(a=-a);var h=a,d=0;for(h+=h<<2,h<<=1;h>e*e;)h-=e*e,d++;for(s+=10*d,t=0;t<e;t++){for(p[0]=0,o=n=i=0;i<e;i++)(r=g[t+e*i])==n?p[o]++:p[++o]=1,n=r;s+=M(o)}return s}var I=null;return{api:{get ecclevel(){return b},set ecclevel(t){b=t},get size(){return _size},set size(t){_size=t},get canvas(){return I},set canvas(t){I=t},getFrame:function(p){return function(p){var M,I,L,R,W,k,T,O;R=p.length,t=0;do{if(t++,L=4*(b-1)+16*(t-1),i=d[L++],o=d[L++],n=d[L++],r=d[L],R<=(L=n*(i+o)+o-3+(t<=9)))break}while(t<40);for(e=17+4*t,W=n+(n+r)*(i+o)+o,R=0;R<W;R++)u[R]=0;for(c=p.slice(0),R=0;R<e*e;R++)g[R]=0;for(R=0;R<(e*(e+1)+1)/2;R++)v[R]=0;for(R=0;R<3;R++){for(L=0,I=0,1==R&&(L=e-7),2==R&&(I=e-7),g[I+3+e*(L+3)]=1,M=0;M<6;M++)g[I+M+e*L]=1,g[I+e*(L+M+1)]=1,g[I+6+e*(L+M)]=1,g[I+M+1+e*(L+6)]=1;for(M=1;M<5;M++)x(I+M,L+1),x(I+1,L+M+1),x(I+5,L+M),x(I+M+1,L+5);for(M=2;M<4;M++)g[I+M+e*(L+2)]=1,g[I+2+e*(L+M+1)]=1,g[I+4+e*(L+M)]=1,g[I+M+1+e*(L+4)]=1}if(t>1)for(R=s[t],I=e-7;;){for(M=e-7;M>R-3&&(y(M,I),!(M<R));)M-=R;if(I<=R+9)break;y(6,I-=R),y(I,6)}for(g[8+e*(e-8)]=1,I=0;I<7;I++)x(7,I),x(e-8,I),x(7,I+e-7);for(M=0;M<8;M++)x(M,7),x(M+e-8,7),x(M,e-8);for(M=0;M<9;M++)x(M,8);for(M=0;M<8;M++)x(M+e-8,8),x(8,M);for(I=0;I<7;I++)x(8,I+e-7);for(M=0;M<e-14;M++)1&M?(x(8+M,6),x(6,8+M)):(g[8+M+6*e]=1,g[6+e*(8+M)]=1);if(t>6)for(R=a[t-7],L=17,M=0;M<6;M++)for(I=0;I<3;I++,L--)1&(L>11?t>>L-12:R>>L)?(g[5-M+e*(2-I+e-11)]=1,g[2-I+e-11+e*(5-M)]=1):(x(5-M,2-I+e-11),x(2-I+e-11,5-M));for(I=0;I<e;I++)for(M=0;M<=I;M++)g[M+e*I]&&x(M,I);for(W=c.length,k=0;k<W;k++)u[k]=c.charCodeAt(k);if(c=u.slice(0),W>=(M=n*(i+o)+o)-2&&(W=M-2,t>9&&W--),k=W,t>9){for(c[k+2]=0,c[k+3]=0;k--;)R=c[k],c[k+3]|=255&R<<4,c[k+2]=R>>4;c[2]|=255&W<<4,c[1]=W>>4,c[0]=64|W>>12}else{for(c[k+1]=0,c[k+2]=0;k--;)R=c[k],c[k+2]|=255&R<<4,c[k+1]=R>>4;c[1]|=255&W<<4,c[0]=64|W>>4}for(k=W+3-(t<10);k<M;)c[k++]=236,c[k++]=17;for(m[0]=1,k=0;k<r;k++){for(m[k+1]=1,T=k;T>0;T--)m[T]=m[T]?m[T-1]^f[w(l[m[T]]+k)]:m[T-1];m[0]=f[w(l[m[0]]+k)]}for(k=0;k<=r;k++)m[k]=l[m[k]];for(L=M,I=0,k=0;k<i;k++)S(I,n,L,r),I+=n,L+=r;for(k=0;k<o;k++)S(I,n+1,L,r),I+=n+1,L+=r;for(I=0,k=0;k<n;k++){for(T=0;T<i;T++)u[I++]=c[k+T*n];for(T=0;T<o;T++)u[I++]=c[i*n+k+T*(n+1)]}for(T=0;T<o;T++)u[I++]=c[i*n+k+T*(n+1)];for(k=0;k<r;k++)for(T=0;T<i+o;T++)u[I++]=c[M+k+T*r];for(c=u,M=I=e-1,L=W=1,O=(n+r)*(i+o)+o,k=0;k<O;k++)for(R=c[k],T=0;T<8;T++,R<<=1){128&R&&(g[M+e*I]=1);do{W?M--:(M++,L?0!=I?I--:(L=!L,6==(M-=2)&&(M--,I=9)):I!=e-1?I++:(L=!L,6==(M-=2)&&(M--,I-=8))),W=!W}while(z(M,I))}for(c=g.slice(0),R=0,I=3e4,L=0;L<8&&(B(L),(M=P())<I&&(I=M,R=L),7!=R);L++)g=c.slice(0);for(R!=L&&B(R),I=h[R+(b-1<<3)],L=0;L<8;L++,I>>=1)1&I&&(g[e-1-L+8*e]=1,L<6?g[8+e*L]=1:g[8+e*(L+1)]=1);for(L=0;L<7;L++,I>>=1)1&I&&(g[8+e*(e-7+L)]=1,L?g[6-L+8*e]=1:g[7+8*e]=1);return g}(p)},utf16to8:function(t){var e,i,o,n;for(e="",o=t.length,i=0;i<o;i++)(n=t.charCodeAt(i))>=1&&n<=127?e+=t.charAt(i):n>2047?(e+=String.fromCharCode(224|n>>12&15),e+=String.fromCharCode(128|n>>6&63),e+=String.fromCharCode(128|n>>0&63)):(e+=String.fromCharCode(192|n>>6&31),e+=String.fromCharCode(128|n>>0&63));return e},draw:function(t,i,o,n,r){i.drawView(o,n);var s=i.ctx,a=o.contentSize,h=a.width,d=a.height,l=a.left,f=a.top;n.borderRadius,n.backgroundColor;var c=n.color,u=void 0===c?"#000000":c;n.border,o.contentSize.left,o.borderSize.left,o.contentSize.top,o.borderSize.top;if(b=r||b,s){s.save(),i.setOpacity(n),i.setTransform(o,n);var g=Math.min(h,d);t=this.utf16to8(t);var v=this.getFrame(t),p=g/e;s.setFillStyle(u);for(var x=0;x<e;x++)for(var y=0;y<e;y++)v[y*e+x]&&s.fillRect(l+p*x,f+p*y,p,p);s.restore(),i.setBorder(o,n)}else console.warn("No canvas provided to draw QR code in!")}}}}(),D=d,N=h,X=l,G=a,q=function(){function t(t,i){var o,n=this;this.id=null,this.pixelRatio=1,this.width=0,this.height=0,this.sleep=1e3/30,this.count=0,this.isRate=!1,this.isDraw=!0,this.isCache=!0,this.fixed="",this.imageBus=[],this.createImage=function(t){return new Promise((function(e,i){var o=null;(o=n.canvas.createImage?n.canvas.createImage():new Image).src=t,o.onload=function(){e({width:this.naturalWidth||this.width,height:this.naturalHeight||this.height,path:o,src:this.src})},o.onerror=function(t){i(t)}}))},this.options=t,Object.assign(this,t),this.component=i,this.ctx=((o=t.context).setFonts=function(t){var e=t.fontFamily,i=void 0===e?"sans-serif":e,n=t.fontSize,r=void 0===n?14:n,a=t.fontWeight,h=void 0===a?"normal":a,d=t.fontStyle,l=void 0===d?"normal":d;S==s.MP_TOUTIAO&&(h="bold"==h?"bold":"",l="italic"==l?"italic":""),o.font=l+" "+h+" "+r+"px "+i},o.draw?o:Object.assign(o,{setStrokeStyle:function(t){o.strokeStyle=t},setLineWidth:function(t){o.lineWidth=t},setLineCap:function(t){o.lineCap=t},setFillStyle:function(t){o.fillStyle=t},setFontSize:function(t){o.font=String(t)+"px sans-serif"},setGlobalAlpha:function(t){o.globalAlpha=t},setLineJoin:function(t){o.lineJoin=t},setTextAlign:function(t){o.textAlign=t},setMiterLimit:function(t){o.miterLimit=t},setShadow:function(t,e,i,n){o.shadowOffsetX=t,o.shadowOffsetY=e,o.shadowBlur=i,o.shadowColor=n},setTextBaseline:function(t){o.textBaseline=t},createCircularGradient:function(){},draw:function(){}})),this.progress=0,this.root={width:t.width,height:t.height,fontSizeRate:1},this.init();var r=e({},this.size);Object.defineProperty(this,"size",{configurable:!0,set:function(t){Object.keys(t).forEach((function(e){r[e]=t[e],n.fixed.includes(e)||(n.root[e]=t[e])}))},get:function(){return r}});var a=0;Object.defineProperty(this,"progress",{configurable:!0,set:function(t){a=t,n.lifecycle("onProgress",t/n.count)},get:function(){return a||0}})}return t.prototype.lifecycle=function(t,e){this.options.listen&&this.options.listen[t]&&this.options.listen[t](e)},t.prototype.init=function(){(this.canvas.height||s.WEB==S)&&(this.canvas.height=this.root.height*this.pixelRatio,this.canvas.width=this.root.width*this.pixelRatio,this.ctx.scale(this.pixelRatio,this.pixelRatio))},t.prototype.clear=function(){this.ctx.clearRect(0,0,this.root.width,this.root.height)},t.prototype.clipPath=function(t,e,i,o,n,r,s){void 0===r&&(r=!1),void 0===s&&(s=!1);var a=this.ctx;if(/polygon/.test(n)){var h=n.match(/-?\d+(rpx|px|%)?\s+-?\d+(rpx|px|%)?/g)||[];a.beginPath(),h.map((function(n){var r=n.split(" "),s=r[0],a=r[1];return[P(s,i)+t,P(a,o)+e]})).forEach((function(t,e){0==e?a.moveTo(t[0],t[1]):a.lineTo(t[0],t[1])})),a.closePath(),s&&a.stroke(),r&&a.fill()}},t.prototype.roundRect=function(t,e,i,o,n,r,s){if(void 0===r&&(r=!1),void 0===s&&(s=!1),!(n<0)){var a=this.ctx;if(a.beginPath(),n){var h=n||{},d=h.borderTopLeftRadius,l=void 0===d?n||0:d,f=h.borderTopRightRadius,c=void 0===f?n||0:f,u=h.borderBottomRightRadius,g=void 0===u?n||0:u,v=h.borderBottomLeftRadius,p=void 0===v?n||0:v;a.arc(t+i-g,e+o-g,g,0,.5*Math.PI),a.lineTo(t+p,e+o),a.arc(t+p,e+o-p,p,.5*Math.PI,Math.PI),a.lineTo(t,e+l),a.arc(t+l,e+l,l,Math.PI,1.5*Math.PI),a.lineTo(t+i-c,e),a.arc(t+i-c,e+c,c,1.5*Math.PI,2*Math.PI),a.lineTo(t+i,e+o-g)}else a.rect(t,e,i,o);a.closePath(),s&&a.stroke(),r&&a.fill()}},t.prototype.setTransform=function(t,e){var i=e.transform,o=e.transformOrigin,n=this.ctx,r=i||{},s=r.scaleX,a=void 0===s?1:s,h=r.scaleY,d=void 0===h?1:h,l=r.translateX,f=void 0===l?0:l,c=r.translateY,u=void 0===c?0:c,g=r.rotate,v=void 0===g?0:g,p=r.skewX,b=void 0===p?0:p,x=r.skewY,y=void 0===x?0:x,w=t.left,m=t.top,S=t.width,z=t.height;f=P(f,S)||0,u=P(u,z)||0;var B={top:P("0%",1),center:P("50%",1),bottom:P("100%",1)},I={left:P("0%",1),center:P("50%",1),right:P("100%",1)};if(o=o.split(" ").filter((function(t,e){return e<2})).reduce((function(t,e){if(/\d+/.test(e)){var i=P(e,1)/(/px|rpx$/.test(e)?M(t.x)?z:S:1);return M(t.x)?Object.assign(t,{y:i}):Object.assign(t,{x:i})}return M(I[e])&&!M(t.x)?Object.assign(t,{x:I[e]}):Object.assign(t,{y:B[e]||.5})}),{}),(f||u)&&n.translate(f,u),(a||d)&&n.scale(a,d),v){var L=w+S*o.x,R=m+z*o.y;n.translate(L,R),n.rotate(v*Math.PI/180),n.translate(-L,-R)}(b||y)&&n.transform(1,Math.tan(y*Math.PI/180),Math.tan(b*Math.PI/180),1,0,0)},t.prototype.setBackground=function(t,e,i,o,n){var r=this.ctx;t&&t!=p?I(t)?L(t,e,i,o,n,r):r.setFillStyle(t):[s.MP_TOUTIAO,s.MP_BAIDU].includes(S)?r.setFillStyle("rgba(0,0,0,0)"):r.setFillStyle(p)},t.prototype.setShadow=function(t){var e=t.boxShadow,i=void 0===e?[]:e,o=this.ctx;if(i.length){var n=i[0],r=i[1],s=i[2],a=i[3];o.setShadow(n,r,s,a)}},t.prototype.setBorder=function(t,e){var i=this.ctx,o=t.width,n=t.height,r=t.left,s=t.top,a=e.border,h=e.borderBottom,d=e.borderTop,l=e.borderRight,f=e.borderLeft,c=e.borderRadius,u=e.lineCap,g=a||{},v=g.borderWidth,p=void 0===v?0:v,b=g.borderStyle,x=g.borderColor,y=h||{},w=y.borderBottomWidth,m=void 0===w?p:w,z=y.borderBottomStyle,B=void 0===z?b:z,M=y.borderBottomColor,P=void 0===M?x:M,I=d||{},L=I.borderTopWidth,R=void 0===L?p:L,W=I.borderTopStyle,k=void 0===W?b:W,T=I.borderTopColor,O=void 0===T?x:T,A=l||{},C=A.borderRightWidth,F=void 0===C?p:C,j=A.borderRightStyle,H=void 0===j?b:j,$=A.borderRightColor,E=void 0===$?x:$,U=f||{},_=U.borderLeftWidth,D=void 0===_?p:_,N=U.borderLeftStyle,X=void 0===N?b:N,G=U.borderLeftColor,q=void 0===G?x:G,V=c||{},Y=V.borderTopLeftRadius,J=void 0===Y?c||0:Y,Q=V.borderTopRightRadius,Z=void 0===Q?c||0:Q,K=V.borderBottomRightRadius,tt=void 0===K?c||0:K,et=V.borderBottomLeftRadius,it=void 0===et?c||0:et;if(h||f||d||l||a){var ot=function(t,e,o){"dashed"==e?/mp/.test(S)?i.setLineDash([Math.ceil(4*t/3),Math.ceil(4*t/3)]):i.setLineDash([Math.ceil(6*t),Math.ceil(6*t)]):"dotted"==e&&i.setLineDash([t,t]),i.setStrokeStyle(o)},nt=function(t,e,o,n,r,s,a,h,d,l,f,c,g,v,p){i.save(),i.setLineCap(p?"square":u),i.setLineWidth(c),ot(c,g,v),i.beginPath(),i.arc(t,e,a,Math.PI*d,Math.PI*l),i.lineTo(o,n),i.arc(r,s,h,Math.PI*l,Math.PI*f),i.stroke(),i.restore()};if(i.save(),a&&!h&&!f&&!d&&!l)return i.setLineWidth(p),ot(p,b,x),this.roundRect(r,s,o,n,c,!1,!!x),void i.restore();m&&nt(r+o-tt,s+n-tt,r+it,s+n,r+it,s+n-it,tt,it,.25,.5,.75,m,B,P,D&&F),D&&nt(r+it,s+n-it,r,s+J,r+J,s+J,it,J,.75,1,1.25,D,X,q,R&&m),R&&nt(r+J,s+J,r+o-Z,s,r+o-Z,s+Z,J,Z,1.25,1.5,1.75,R,k,O,D&&F),F&&nt(r+o-Z,s+Z,r+o,s+n-tt,r+o-tt,s+n-tt,Z,tt,1.75,2,.25,F,H,E,R&&m)}},t.prototype.setOpacity=function(t){var e=t.opacity,i=void 0===e?1:e;this.ctx.setGlobalAlpha(i)},t.prototype.drawPattern=function(t,e,n){return i(this,void 0,void 0,(function(){var i=this;return o(this,(function(o){return[2,new Promise((function(o,r){i.drawView(e,n,!0,!1,!0);var s=i,a=s.ctx;s.canvas;var h=e.width,d=e.height,l=e.left,f=e.top,c=n||{},u=c.borderRadius,g=void 0===u?0:u,v=c.backgroundImage,p=c.backgroundRepeat,b=void 0===p?"repeat":p;v&&function(t){var r=a.createPattern(t.src,b);a.setFillStyle(r),i.roundRect(l,f,h,d,g,!0,!1),i.setBorder(e,n),o()}(t)}))]}))}))},t.prototype.drawView=function(t,e,i,o,n){void 0===i&&(i=!0),void 0===o&&(o=!0),void 0===n&&(n=!0);var r=this.ctx,s=t.width,a=t.height,h=t.left,d=t.top,l=e||{},f=l.borderRadius,c=void 0===f?0:f,u=l.backgroundColor,g=void 0===u?p:u,v=l.overflow;e.opacity&&this.setOpacity(e),this.setTransform(t,e),n&&(r.save(),this.setShadow(e)),i&&this.setBackground(g,s,a,h,d),e.clipPath?this.clipPath(h,d,s,a,e.clipPath,i,!1):this.roundRect(h,d,s,a,c,i,!1),n&&r.restore(),o&&this.setBorder(t,e),"hidden"==v&&r.clip()},t.prototype.drawImage=function(t,e,n,r){return void 0===e&&(e={}),void 0===n&&(n={}),void 0===r&&(r=!0),i(this,void 0,void 0,(function(){var a=this;return o(this,(function(h){switch(h.label){case 0:return[4,new Promise((function(h,d){return i(a,void 0,void 0,(function(){var i,a,d,l,f,c,u,g,v,x,y,w,m,z,B,M,I,L,R,W=this;return o(this,(function(o){return n.boxShadow&&this.drawView(e,Object.assign(n,{backgroundColor:n.backgroundColor||n.boxShadow&&(n.backgroundColor||"#ffffff")}),!0,!1,!0),i=this.ctx,a=n.borderRadius,d=void 0===a?0:a,l=n.backgroundColor,f=void 0===l?p:l,c=n.objectFit,u=void 0===c?b:c,g=n.backgroundSize,v=void 0===g?b:g,x=n.objectPosition,y=n.backgroundPosition,n.backgroundImage&&(u=v,x=y),w=e.width,m=e.height,z=e.left,B=e.top,i.save(),M=e.contentSize.left-e.borderSize.left,I=e.contentSize.top-e.borderSize.top,r||(this.setOpacity(n),this.setTransform(e,n),this.setBackground(f,w,m,z,B),this.roundRect(z,B,w,m,d,!!d,!1)),z+=M,B+=I,i.clip(),L=function(t){if(u!==b){var o=function(t,e,i){var o=t.objectFit,n=t.objectPosition,r=e.width/e.height,s=i.width/i.height,a=1;"contain"==o&&r>=s||"cover"==o&&r<s?a=e.height/i.height:("contain"==o&&r<s||"cover"==o&&r>=s)&&(a=e.width/i.width);var h=i.width*a,d=i.height*a,l=/^\d+px|rpx$/.test(null==n?void 0:n[0])?P(null==n?void 0:n[0],e.width):(e.width-h)*(/%$/.test(null==n?void 0:n[0])?P(null==n?void 0:n[0],1):{left:0,center:.5,right:1}[(null==n?void 0:n[0])||"center"]),f=/^\d+px|rpx$/.test(null==n?void 0:n[1])?P(null==n?void 0:n[1],e.height):(e.height-d)*(/%$/.test(null==n?void 0:n[1])?P(null==n?void 0:n[1],1):{top:0,center:.5,bottom:1}[(null==n?void 0:n[1])||"center"]),c=function(t,e){return[(t-l)/a,(e-f)/a]},u=c(0,0),g=u[0],v=u[1],p=c(e.width,e.height),b=p[0],x=p[1];return{sx:Math.max(g,0),sy:Math.max(v,0),sw:Math.min(b-g,i.width),sh:Math.min(x-v,i.height),dx:Math.max(l,0),dy:Math.max(f,0),dw:Math.min(h,e.width),dh:Math.min(d,e.height)}}({objectFit:u,objectPosition:x},e.contentSize,t),n=o.sx,r=o.sy,a=o.sh,h=o.sw,d=o.dx,l=o.dy,f=o.dh,c=o.dw;S==s.MP_BAIDU?i.drawImage(t.src,d+z,l+B,c,f,n,r,h,a):i.drawImage(t.src,n,r,h,a,d+z,l+B,c,f)}else i.drawImage(t.src,z,B,w,m)},R=function(){i.restore(),W.drawView(e,n,!1,!0,!1),h(1)},function(t){L(t),R()}(t),[2]}))}))}))];case 1:return h.sent(),[2]}}))}))},t.prototype.drawText=function(t,e,i,o){var n,r,s=this.ctx,a=e.borderSize,h=e.contentSize,d=e.left,l=e.top,f=h.width,c=h.height,u=h.left-a.left,g=h.top-a.top,v=i.color,p=void 0===v?"#000000":v,b=i.lineHeight,x=void 0===b?"1.4em":b,y=i.fontSize,w=void 0===y?14:y,m=i.fontWeight,S=i.fontFamily,z=i.fontStyle,B=i.textAlign,M=void 0===B?"left":B,I=i.verticalAlign,L=void 0===I?"middle":I,R=i.backgroundColor,W=i.lineClamp,k=i.backgroundClip,T=i.textDecoration;if(this.drawView(e,i,k!=N),x=P(x,w),t){switch(s.save(),d+=u,l+=g,s.setFonts({fontFamily:S,fontSize:w,fontWeight:m,fontStyle:z}),s.setTextBaseline("middle"),s.setTextAlign(M),k?this.setBackground(R,f,c,d,l):s.setFillStyle(p),l+=w/2,M){case"left":break;case"center":d+=.5*f;break;case"right":d+=f}var O=o.lines*x,A=Math.ceil((c-O)/2);switch(A<0&&(A=0),L){case"top":break;case"middle":l+=A;break;case"bottom":l+=2*A}var C=(x-o.fontHeight)/2,F=function(t,e,i){var n=t;switch(M){case"left":t=t,n+=i;break;case"center":n=(t-=i/2)+i;break;case"right":n=t,t-=i}T&&(s.setLineWidth(w/13),s.beginPath(),e-=C,/\bunderline\b/.test(T)&&(s.moveTo(t,e-.5*o.fontHeight),s.lineTo(n,e-.5*o.fontHeight)),/\boverline\b/.test(T)&&(s.moveTo(t,e-1.5*o.fontHeight),s.lineTo(n,e-1.5*o.fontHeight)),/\bline-through\b/.test(T)&&(s.moveTo(t,e-o.fontHeight),s.lineTo(n,e-o.fontHeight)),s.closePath(),s.setStrokeStyle(p),s.stroke())};if(!o.widths||1==o.widths.length&&o.widths[0].total<=h.width)return s.fillText(t,d,l+C),F(d,l+=x,(null===(r=null===(n=null==o?void 0:o.widths)||void 0===n?void 0:n[0])||void 0===r?void 0:r.total)||o.text),s.restore(),void this.setBorder(e,i);for(var j=t.split(""),H=l,$=d,E="",U=0,_=0;_<=j.length;_++){var D=j[_]||"",X="\n"===D,G=""==D,q=E+(D=X?"":D),V=s.measureText(q).width;if(U>=W)break;if($=d,(V=V)>h.width||X||G){if(U++,E=G&&V<=h.width?q:E,U===W&&V>f){for(;s.measureText(E+"...").width>h.width&&!(E.length<=1);)E=E.substring(0,E.length-1);E+="..."}if(s.fillText(E,$,l+C),F($,l+=x,V),E=D,l>H+c)break}else E=q}s.restore()}},t.prototype.source=function(t){var e;return i(this,void 0,void 0,(function(){var i,n,r,s=this;return o(this,(function(o){switch(o.label){case 0:if(i=+new Date,"{}"==JSON.stringify(t))return[2];if(!t.type)for(n in t.type=G,t.css=t.css||{},t)["views","children","type","css"].includes(n)||(t.css[n]=t[n],delete t[n]);return(null===(e=t.css)||void 0===e?void 0:e.width)||t.css||(t.css={}),[4,this.create(t)];case 1:return r=o.sent(),this.size=(null==r?void 0:r.layout())||{},this.node=r,this.onEffectFinished().then((function(t){return s.lifecycle("onEffectSuccess",t)})).catch((function(t){return s.lifecycle("onEffectFail",t)})),console.log("布局用时："+(+new Date-i)+"ms"),[2,this.root]}}))}))},t.prototype.getImageInfo=function(t){return this.imageBus[t]||(this.imageBus[t]=this.createImage(t)),this.imageBus[t]},t.prototype.create=function(t,n){var r,s;return i(this,void 0,void 0,(function(){var i,a,h,d,l,f,c,g,v,p,b,x,y,w,m,S,z;return o(this,(function(o){switch(o.label){case 0:if(i=t.type==D,a=[N,X].includes(t.type),h=t.css||{},d=h.backgroundImage,l=h.display,a&&(t.text=String(t.text)),i&&!t.src&&!t.url||a&&!t.text)return[2];if(l==u)return[2];if(!(i||t.type==G&&d))return[3,4];f=i?t.src:"",c=/url\((.+)\)/,d&&(null===(r=c.exec(d))||void 0===r?void 0:r[1])&&(f=(null===(s=c.exec(d))||void 0===s?void 0:s[1])||""),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this.getImageInfo(f)];case 2:return g=o.sent(),v=g.width,p=g.height,!(b=g.path)&&i?[2]:(b&&(t.attributes=Object.assign(t.attributes||{},{width:v,height:p,path:b,src:b,naturalSrc:f})),[3,4]);case 3:return x=o.sent(),t.type!=G?[2]:(this.lifecycle("onEffectFail",e(e({},x),{src:f})),[3,4]);case 4:if(this.count+=1,y=new U(t,n,this.root,this.ctx),!(w=t.views||t.children))return[3,8];m=0,o.label=5;case 5:return m<w.length?(S=w[m],[4,this.create(S,y)]):[3,8];case 6:(z=o.sent())&&y.add(z),o.label=7;case 7:return m++,[3,5];case 8:return[2,y]}}))}))},t.prototype.drawNode=function(t){return i(this,void 0,void 0,(function(){var e,i,n,r,s,a,h,d,l,f,c,u;return o(this,(function(o){switch(o.label){case 0:return e=t.layoutBox,i=t.computedStyle,n=t.attributes,r=t.name,s=t.children,a=t.attributes,h=a.src,d=a.text,this.ctx.save(),r!==G?[3,7]:h?i.backgroundRepeat?[4,this.drawPattern(n,e,i)]:[3,2]:[3,5];case 1:return o.sent(),[3,4];case 2:return[4,this.drawImage(n,e,i,!1)];case 3:o.sent(),o.label=4;case 4:return[3,6];case 5:this.drawView(e,i),o.label=6;case 6:return[3,10];case 7:return r===D&&h?[4,this.drawImage(n,e,i,!1)]:[3,9];case 8:return o.sent(),[3,10];case 9:r===N?this.drawText(d,e,i,n):r===X&&_.api&&_.api.draw(d,this,e,i),o.label=10;case 10:if(this.progress+=1,!s)return[3,14];l=Object.values?Object.values(s):Object.keys(s).map((function(t){return s[t]})),f=0,c=l,o.label=11;case 11:return f<c.length?(u=c[f],[4,this.drawNode(u)]):[3,14];case 12:o.sent(),o.label=13;case 13:return f++,[3,11];case 14:return this.ctx.restore(),[2]}}))}))},t.prototype.render=function(){var t=this;return new Promise((function(e,n){return i(t,void 0,void 0,(function(){var t,i;return o(this,(function(o){switch(o.label){case 0:return t=+new Date,this.init(),[4,(r=30,void 0===r&&(r=0),new Promise((function(t){return setTimeout(t,r)})))];case 1:o.sent(),o.label=2;case 2:return o.trys.push([2,6,,7]),this.node?[4,this.drawNode(this.node)]:[3,4];case 3:return o.sent(),e(this.node),[3,5];case 4:this.lifecycle("onEffectFail","node is empty"),o.label=5;case 5:return[3,7];case 6:return i=o.sent(),this.lifecycle("onEffectFail",i),n(i),[3,7];case 7:return console.log("渲染用时："+(+new Date-t-30)+"ms"),[2]}var r}))}))}))},t.prototype.onEffectFinished=function(){var t=this,e=Object.keys(this.imageBus).map((function(e){return t.imageBus[e]}));return Promise.all(e)},t.prototype.destroy=function(){this.node=[]},t.prototype.save=function(t){try{var e=t||{},i=e.fileType,o=void 0===i?"png":i,n=e.quality,r=void 0===n?1:n;return this.canvas.toDataURL("image/"+o,r)}catch(t){return this.lifecycle("onEffectFail","image cross domain"),t}},t}();s.WEB==S&&(window.Painter=q),t.Painter=q,t.default=q,Object.defineProperty(t,"__esModule",{value:!0})}));
