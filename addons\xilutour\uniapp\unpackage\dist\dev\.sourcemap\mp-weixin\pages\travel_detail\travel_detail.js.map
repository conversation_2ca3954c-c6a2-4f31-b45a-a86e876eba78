{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_detail/travel_detail.vue?2c5d", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_detail/travel_detail.vue?11e7", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_detail/travel_detail.vue?c3b3", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_detail/travel_detail.vue?b4d0", "uni-app:///pages/travel_detail/travel_detail.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_detail/travel_detail.vue?62bd", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_detail/travel_detail.vue?43d1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "bar<PERSON>itle", "statusBarHeight", "swiper<PERSON><PERSON>rent", "tabIdx", "tourId", "tour", "work_time", "address", "name", "worktime", "view_count", "images_text", "tags", "gather_city", "coupons", "total", "commentList", "commentListMore", "page", "setCol", "onLoad", "onReachBottom", "onShareAppMessage", "path", "title", "onShareTimeline", "query", "onPageScroll", "methods", "swiper<PERSON><PERSON>e", "tabClick", "navBack", "uni", "fetchDetail", "url", "tour_id", "loading", "success", "ret", "fail", "console", "content", "showCancel", "complete", "fetch", "moreCoupon", "toggleCollection", "bindReceive", "coupon_id", "bindEnroll", "bindEnrollDate", "couponPopOpen", "couponPopClose", "bindPrev", "urls", "current"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4N11B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAL;QACA;QACAM;MACA;MAEAC;MACAC;MACAC;QAAAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACAC;IACA;IACA;MACAC;MACAD;MACA;IACA;EACA;EACAE;IACA;IACA;IACA;MACAC;IACA;IACA;MACAF;MACAE;IACA;EACA;EACAC;IACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAC;IACA;IACAC;MAAA;MACA;QAAAC;QAAAnC;UAAAoC;QAAA;QAAAC;QAAAC;UACAC;UACAA;UACAA;UACAA;UACA;UACA;QACA;QAAAC;UACAC;UACAR;YACAR;YACAiB;YACAC;YACAC;cACAX;YACA;UACA;UACA;QACA;MACA;IACA;IACAY;MAAA;MACA;QAAAT;MAAA;MACAT;MACA;QACA;MACA;IACA;IACA;IACAmB;MACAb;QACAE;MACA;IACA;IACA;IACAY;MAAA;MACA;QACA;MACA;MACA;QACAZ;QACAnC;UACAoC;QACA;QACAE;UACA;QACA;QACAE;MACA;IACA;IACA;IACAQ;MAAA;MACA;MACA;MACA;QACA;MACA;MACA;QACAb;QACAnC;UACAiD;QACA;QACAX;UACAL;YACAR;UACA;UACAV;UACA;QACA;QACAyB;UACAP;YACAR;YACAiB;UACA;UACA;QACA;MACA;IACA;IAEA;IACAQ;MACAjB;QACAE;MACA;IACA;IACA;IACAgB;MACA;MACA;MACAlB;QACAE;MACA;IACA;IACA;IACAiB;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACArB;QACAsB;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxZA;AAAA;AAAA;AAAA;AAAyjD,CAAgB,y7CAAG,EAAC,C;;;;;;;;;;;ACA7kD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/travel_detail/travel_detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/travel_detail/travel_detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./travel_detail.vue?vue&type=template&id=72bcdb74&scoped=true&\"\nvar renderjs\nimport script from \"./travel_detail.vue?vue&type=script&lang=js&\"\nexport * from \"./travel_detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./travel_detail.vue?vue&type=style&index=0&id=72bcdb74&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"72bcdb74\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/travel_detail/travel_detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_detail.vue?vue&type=template&id=72bcdb74&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.tour.tags.length\n  var l0 =\n    _vm.tabIdx == 5\n      ? _vm.__map(_vm.commentList, function (comment, index) {\n          var $orig = _vm.__get_orig(comment)\n          var g1 = comment.images_text.length\n          return {\n            $orig: $orig,\n            g1: g1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"page-foot\">\r\n\t\t\t<view class=\"g_order_foot1 flex-box\">\r\n\t\t\t\t<navigator class=\"nav\" url=\"/pages/index/index\" open-type=\"switchTab\" hover-class=\"none\">\r\n\t\t\t\t\t<image src=\"/static/icon/icon_btn1.png\" mode=\"aspectFit\" class=\"icon\"></image>\r\n\t\t\t\t\t<view>首页</view>\r\n\t\t\t\t</navigator>\r\n\t\t\t\t<button class=\"nav\" open-type=\"contact\" hover-class=\"none\">\r\n\t\t\t\t\t<image src=\"/static/icon/icon_btn2.png\" mode=\"aspectFit\" class=\"icon\"></image>\r\n\t\t\t\t\t<view>客服</view>\r\n\t\t\t\t</button>\r\n\t\t\t\t<view class=\"nav\" @click=\"toggleCollection()\">\n\t\t\t\t\t<image  :src=\"'/static/icon/icon_btn3'+(tour.is_collection_count == 1 ? 'on' : '')+'.png'\" mode=\"aspectFit\" class=\"icon\"></image>\r\n\t\t\t\t\t<!-- <image v-else src=\"/static/icon/icon_btn3.png\" mode=\"aspectFit\" class=\"icon\"></image> -->\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view>{{tour.is_collection_count == 1?'已收藏':'收藏'}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn1\" @click=\"bindEnroll()\">我要报名</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"m-header\" :style=\"{ background: setCol ? 'var(--normal)' : 'unset'}\">\r\n\t\t\t\t<view class=\"g-custom-nav flex-box plr30\"\r\n\t\t\t\t\t:style=\"{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }\">\r\n\t\t\t\t\t<image @click=\"navBack\" src=\"/static/icon/icon_back1.png\" mode=\"aspectFit\" class=\"icon_back\"></image>\r\n\t\t\t\t\t<view class=\"flex-1 pr35 mr30 fs26 col-f tc\">{{barTitle}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_swiper\">\r\n\t\t\t\t<swiper class=\"swiper\" :current=\"swiperCurrent\" circular @change=\"swiperChange\">\r\n\t\t\t\t\t<swiper-item v-for=\"(item,index) in tour.images_text\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"nav\" :class=\"{'scale': swiperCurrent !==index}\">\r\n\t\t\t\t\t\t\t<image :src=\"item\" mode=\"aspectFill\" class=\"img\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t\t<view class=\"swiper_dots flex-box flex-center\">\r\n\t\t\t\t\t<view class=\"dots\" v-for=\"(item,index) in tour.images_text\" :key=\"index\"\r\n\t\t\t\t\t\t:class=\"{'active': swiperCurrent == index}\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_info_wrap\">\r\n\t\t\t\t<view class=\"g_travel_list\">\r\n\t\t\t\t\t<view class=\"flex-box mb30\">\r\n\t\t\t\t\t\t<view class=\"flex-1 col-price\" v-if=\"tour.tour_date\">\r\n\t\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t\t<text class=\"fs40\">{{tour.tour_date.salesprice}}</text>\r\n\t\t\t\t\t\t\t<text class=\"fs30\">起</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"fs28 col-a\">{{tour.view_count}}人浏览</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box flex-wrap pb10\" v-if=\"tour.tags.length>0\">\r\n\t\t\t\t\t\t<view class=\"label\" v-for=\"(tag,index) in tour.tags\">{{tag.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mb20\">\r\n\t\t\t\t\t\t<text class=\"g_feng\">{{tour.points}}</text>\r\n\t\t\t\t\t\t<text class=\"fs36 col-10\">{{tour.name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"desc\">{{tour.sub_name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"bg-white\">\r\n\t\t\t\r\n\t\t\t<view class=\"xilu_coupon_box\">\r\n\t\t\t\t<!-- v-if=\"tour.coupons.length>0\"  @click=\"moreCoupon()\"-->\r\n\t\t\t\t<view class=\"flex-box ptb25 m-hairline--bottom\" @click=\"couponPopOpen\">\r\n\t\t\t\t\t<view class=\"col-price fs30 mr20\">优惠券</view>\r\n\t\t\t\t\t<view class=\"coupon_list\">\r\n\t\t\t\t\t\t<view class=\"coupon\" v-for=\"(item,index) in tour.coupons\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_coupon_bg3.png\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t<view class=\"inner\">{{item.name}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image src=\"/static/icon/icon_arrow2.png\" mode=\"aspectFit\" class=\"g-icon30\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-box ptb35 flex-between tc\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class=\"fs30 col-3 mb20\">{{tour.team_count}}</view>\r\n\t\t\t\t\t\t<view class=\"fs24 col-89\">限制人数</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class=\"fs30 col-3 mb20\">{{tour.age_introduce}}</view>\r\n\t\t\t\t\t\t<view class=\"fs24 col-89\">年龄限制</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class=\"fs30 col-3 mb20\">{{tour.gather_city.name}}</view>\r\n\t\t\t\t\t\t<view class=\"fs24 col-89\">集合地点</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class=\"fs30 col-3 mb20\">{{tour.gather_city.name}}</view>\r\n\t\t\t\t\t\t<view class=\"fs24 col-89\">解散地点</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"fs36 m30 col-10\">近期出发</view>\r\n\r\n\t\t\t<view class=\"xilu_regis\">\r\n\t\t\t\t<view class=\"item\" v-for=\"(item,index) in tour.tour_date_list\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"flex-box flex-between plr25\">\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<view class=\"fs24 col-89 mb20\">出发</view>\r\n\t\t\t\t\t\t\t<view class=\"fs34 col-10 mb10\">{{item.appoint_date_text}}</view>\r\n\t\t\t\t\t\t\t<view class=\"fs24 col-5\">{{item.appoint_date_week}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<view class=\"fs24 col-89 mb20\">结束</view>\r\n\t\t\t\t\t\t\t<view class=\"fs34 col-10 mb10\">{{item.appoint_end_date_text}}</view>\r\n\t\t\t\t\t\t\t<view class=\"fs24 col-5\">{{item.appoint_end_date_week}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"col-price plr30 mt20\">\r\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs40\">{{item.salesprice}}</text>\r\n\t\t\t\t\t\t<text class=\"fs30\">起</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"btn\" @click=\"bindEnrollDate(index)\">去报名</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"g_tab mtb15\">\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 1}\" @click=\"tabClick(1)\">路线亮点</view>\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 2}\" @click=\"tabClick(2)\">行程简介</view>\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 3}\" @click=\"tabClick(3)\">费用说明</view>\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 4}\" @click=\"tabClick(4)\">行程必看</view>\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 5}\" @click=\"tabClick(5)\">用户评价({{total}})</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_detail_box\">\r\n\t\t\t\t<view v-if=\"tabIdx == 1\">\r\n\t\t\t\t\t<!-- <image class=\"img_detail\" src=\"../../static/logo.png\" mode=\"widthFix\"></image> -->\r\n\t\t\t\t\t<rich-text :nodes=\"tour.highlights_content\"></rich-text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view v-if=\"tabIdx == 2\">\r\n\t\t\t\t\t<view class=\"step_wrap\">\r\n\t\t\t\t\t\t<rich-text :nodes=\"tour.introduction_content\"></rich-text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view v-if=\"tabIdx == 3\">\r\n\t\t\t\t\t<view class=\"detail fs30 col-3\">\r\n\t\t\t\t\t\t<rich-text :nodes=\"tour.fee_explain\"></rich-text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view v-if=\"tabIdx == 4\">\r\n\t\t\t\t\t<view class=\"detail fs30 col-3\">\r\n\t\t\t\t\t\t<rich-text :nodes=\"tour.see_content\"></rich-text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view v-if=\"tabIdx == 5\">\r\n\t\t\t\t\t<view class=\"g_comment\" v-for=\"(comment,index) in commentList\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"flex-box\">\r\n\t\t\t\t\t\t\t<image :src=\"comment.user.avatar\" mode=\"aspectFill\" class=\"head\"></image>\r\n\t\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t\t<view class=\"fs30 col-5 mb20\">{{comment.user.nickname}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"fs24 col-89\">{{comment.createtime_text}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-rate :disabled=\"true\" :readonly=\"true\" :value=\"4\" :count=\"4\" :size=\"18\" :gutter=\"0\" active-icon=\"/static/icon/icon_star.png\"></u-rate>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text1 pt25\">{{comment.content}}</view>\r\n\t\t\t\t\t\t<view class=\"flex-box flex-wrap\"  v-if=\"comment.images_text.length>0\">\r\n\t\t\t\t\t\t\t<image @click=\"bindPrev(index,index2)\" v-for=\"(img,index2) in comment.images_text\" :src=\"img\" mode=\"aspectFill\" class=\"img1\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"g-btn3-wrap\">\n\t\t\t\t\t\t<view class=\"g-btn3\" @click=\"fetch\">{{commentListMore.text}}</view>\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t</view>\r\n\t\t\t<uni-popup ref=\"couponPopup\" type=\"bottom\">\r\n\t\t\t\t<view class=\"g_coupon_pop\">\r\n\t\t\t\t\t<view class=\"fs30 col-10 tc mb30\">优惠券</view>\r\n\t\t\t\t\t<image src=\"/static/icon/icon_close.png\" mode=\"aspectFit\" class=\"icon_close\" @click=\"couponPopClose\"></image>\r\n\t\t\t\r\n\t\t\t\t\t<view class=\"pop_coupon_wrap\">\r\n\t\t\t\t\t\t<view class=\"pop_coupon\" v-for=\"(coupon,index) in tour.coupons\" :key=\"index\">\r\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_coupon_bg1.png\" mode=\"aspectFill\" class=\"bg\"></image>\r\n\t\t\t\t\t\t\t<view class=\"inner flex-box\">\r\n\t\t\t\t\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"fwb mb20\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"fs24\">¥</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"fs50\">{{coupon.money}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"man\">满{{coupon.at_least}}可用</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"right flex-1 flex-box\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"fs30 mb20\">{{coupon.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"fs24\">{{coupon.use_end_time_text}}到期</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"use\" v-if=\"coupon.is_receive_count == 1\">已领取</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"use\" @click.stop=\"bindReceive(index)\" v-else>领取</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t</uni-popup>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\n\t\t\t\tbarTitle: '线路详情',\r\n\t\t\t\tstatusBarHeight: 20,\r\n\t\t\t\tswiperCurrent: 0,\r\n\t\t\t\ttabIdx: 1,\n\t\t\t\ttourId:0,\n\t\t\t\ttour:{\n\t\t\t\t\twork_time: '',\n\t\t\t\t\taddress: '',\n\t\t\t\t\tname: '',\n\t\t\t\t\tworktime: '',\n\t\t\t\t\tview_count: 0,\n\t\t\t\t\timages_text: [],\n\t\t\t\t\ttags: [],\n\t\t\t\t\tgather_city: {\n\t\t\t\t\t\tname: ''\n\t\t\t\t\t},\n\t\t\t\t\tcoupons: []\r\n\t\t\t\t},\n\t\t\t\t\n\t\t\t\ttotal:0,\n\t\t\t\tcommentList:[],\n\t\t\t\tcommentListMore: {page:1},\n\t\t\t\tsetCol:false\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.statusBarHeight = getApp().globalData.statusBarHeight;\r\n\t\t\tthis.tourId = options.id || 0;\r\n\t\t\tthis.fetchDetail();\n\t\t\tthis.fetch();\r\n\t\t},\n\t\tonReachBottom() {\n\t\t\tif(this.tabIdx == 5){\n\t\t\t\tthis.fetch();\n\t\t\t}\n\t\t},\n\t\tonShareAppMessage(e) {\n\t\t\tlet userinfo = this.$core.getUserinfo();\n\t\t\tlet path = '/pages/travel_detail/travel_detail?id='+this.tourId;\n\t\t\tif(userinfo){\n\t\t\t\tpath += '&pid='+userinfo.pid\n\t\t\t}\n\t\t\treturn {\n\t\t\t\ttitle: this.tour.name,\n\t\t\t\tpath: path,\n\t\t\t\t//imageUrl: this.tour.thumb_image_text\n\t\t\t}\n\t\t},\n\t\tonShareTimeline() {\n\t\t\tlet userinfo = this.$core.getUserinfo();\n\t\t\tlet query = \"id=\" + this.tourId\n\t\t\tif(userinfo){\n\t\t\t\tquery += '&pid='+userinfo.pid\n\t\t\t}\n\t\t\treturn {\n\t\t\t\ttitle: this.tour.name,\n\t\t\t\tquery: query\n\t\t\t}\n\t\t},\r\n\t\tonPageScroll(e) {\r\n\t\t\tif(e.scrollTop > 350){\r\n\t\t\t\tthis.setCol = true\r\n\t\t\t}else{\r\n\t\t\t\tthis.setCol = false\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tswiperChange(e) {\r\n\t\t\t\tthis.swiperCurrent = e.detail.current\r\n\t\t\t},\r\n\t\t\ttabClick(i) {\r\n\t\t\t\tthis.tabIdx = i;\r\n\t\t\t},\r\n\t\t\tnavBack() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\n\t\t\tfetchDetail(){\n\t\t\t\tthis.$core.post({url: 'xilutour.tour/detail',data: {tour_id: this.tourId},loading: false,success: ret => {\n\t\t\t\t\t\tret.data.highlights_content = this.$core.richTextnew(ret.data.highlights_content);\n\t\t\t\t\t\tret.data.introduction_content = this.$core.richTextnew(ret.data.introduction_content);\n\t\t\t\t\t\tret.data.fee_explain = this.$core.richTextnew(ret.data.fee_explain);\n\t\t\t\t\t\tret.data.see_content = this.$core.richTextnew(ret.data.see_content);\n\t\t\t\t\t\tthis.tour = ret.data;\n\t\t\t\t\t\tthis.barTitle = ret.data.name;\n\t\t\t\t\t},fail: err => {\n\t\t\t\t\t\tconsole.log(err);\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle:'提示',\n\t\t\t\t\t\t\tcontent: err.msg,\n\t\t\t\t\t\t\tshowCancel:false,\n\t\t\t\t\t\t\tcomplete() {\n\t\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tfetch(){\n\t\t\t\tlet query = {tour_id: this.tourId};\n\t\t\t\tquery.pagesize = 10;\n\t\t\t\tthis.$util.fetch(this, 'xilutour.tour_comment/lists', query, 'commentListMore', 'commentList', 'data', data=>{\n\t\t\t\t  this.total = data.total;\n\t\t\t\t})\n\t\t\t},\n\t\t\t//更多优惠券\n\t\t\tmoreCoupon(){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/coupon/coupon'\n\t\t\t\t})\n\t\t\t},\n\t\t\t//收藏\n\t\t\ttoggleCollection() {\n\t\t\t\tif(!this.$core.getUserinfo(true)){\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t    this.$core.post({\n\t\t\t        url: 'xilutour.tour/toggle_collection',\n\t\t\t        data: {\n\t\t\t            tour_id: this.tour.id\n\t\t\t        },\n\t\t\t        success: ret => {\n\t\t\t            this.tour.is_collection_count = ret.data.is_collection_count;\n\t\t\t        },\n\t\t\t        fail: err => {}\n\t\t\t    });\n\t\t\t},\n\t\t\t//领取优惠券\n\t\t\tbindReceive(index){\n\t\t\t\tlet coupons = this.tour.coupons;\n\t\t\t\tlet coupon = coupons[index];\n\t\t\t\tif(!this.$core.getUserinfo(true)){\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.$core.post({\n\t\t\t\t    url: 'xilutour.coupon/receive',\n\t\t\t\t    data: {\n\t\t\t\t        coupon_id: coupon.id\n\t\t\t\t    },\n\t\t\t\t    success: ret => {\n\t\t\t\t        uni.showToast({\n\t\t\t\t        \ttitle:'领取成功',\n\t\t\t\t        })\n\t\t\t\t\t\tcoupons[index].is_receive_count = 1;\n\t\t\t\t\t\tthis.tour.coupons = coupons;\n\t\t\t\t    },\n\t\t\t\t    fail: err => {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\tcontent: err.msg,\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t//报名\n\t\t\tbindEnroll(){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/select_specification/select_specification?tour_id='+this.tour.id\n\t\t\t\t})\n\t\t\t},\r\n\t\t\t//\n\t\t\tbindEnrollDate(index){\n\t\t\t\tlet dateList = this.tour.tour_date_list;\n\t\t\t\tlet tourDateId = dateList[index].id;\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/select_specification/select_specification?tour_id='+this.tour.id+'&tour_date_id='+ tourDateId\n\t\t\t\t})\n\t\t\t},\r\n\t\t\t// 打开优惠券弹窗\r\n\t\t\tcouponPopOpen() {\r\n\t\t\t\tthis.$refs.couponPopup.open();\r\n\t\t\t},\r\n\t\t\t// 关闭优惠券弹窗\r\n\t\t\tcouponPopClose() {\r\n\t\t\t\tthis.$refs.couponPopup.close();\r\n\t\t\t},\n\t\t\tbindPrev(index,index2){\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: this.commentList[index].images_text,\n\t\t\t\t\tcurrent: index2\n\t\t\t\t})\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t\r\n\t.container{\r\n\t\tbackground: #F7F9FB;\r\n\t}\r\n\t.g_order_foot1 {\r\n\t\tmargin: 0 auto 40rpx;\r\n\t}\r\n\r\n\t.xilu {\r\n\t\t&_detail_box {\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\r\n\t\t\t.img_detail {\r\n\t\t\t\tmargin: 0 0 20rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\r\n\t\t\t.step_wrap {\r\n\t\t\t\t.step {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tmargin: 0 0 30rpx 50rpx;\r\n\t\t\t\t\tpadding: 30rpx 30rpx 40rpx;\r\n\t\t\t\t\twidth: 640rpx;\r\n\t\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\t\tbox-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);\r\n\t\t\t\t\tborder-radius: 30rpx;\r\n\r\n\t\t\t\t\t.img_step {\r\n\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\tmax-width: 100%;\r\n\t\t\t\t\t\tmargin: 30rpx 0 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.step::before {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tleft: -40rpx;\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\twidth: 2rpx;\r\n\t\t\t\t\theight: calc(100% + 30rpx);\r\n\t\t\t\t\tbackground-color: rgba(255, 171, 41, 0.2);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.step::after {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 38rpx;\r\n\t\t\t\t\tleft: -50rpx;\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\twidth: 22rpx;\r\n\t\t\t\t\theight: 22rpx;\r\n\t\t\t\t\tbackground: #FFAB29;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.detail {\r\n\t\t\t\tpadding: 30rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tbox-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\tline-height: 44rpx;\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t&_regis {\r\n\t\t\tpadding: 0 40rpx;\r\n\t\t\toverflow-x: scroll;\r\n\t\t\toverflow-y: hidden;\r\n\t\t\twhite-space: nowrap;\r\n\r\n\t\t\t.item {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin: 0 30rpx 0 0;\r\n\t\t\t\tpadding: 20rpx 0 0;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 270rpx;\r\n\t\t\t\theight: 312rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tborder-radius: 25rpx;\r\n\t\t\t\tborder: 1px solid #EEEEEE;\r\n\r\n\t\t\t\t.line {\r\n\t\t\t\t\twidth: 12rpx;\r\n\t\t\t\t\theight: 2rpx;\r\n\t\t\t\t\tbackground-color: var(--normal);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btn {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\twidth: 270rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tbackground-color: var(--normal);\r\n\t\t\t\t\tborder-radius: 2rpx 2rpx 25rpx 25rpx;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\tline-height: 60rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_coupon_box {\r\n\t\t\tmargin: 0 30rpx 50rpx;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\twidth: 690rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t.coupon_list {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\toverflow-x: scroll;\r\n\t\t\t\toverflow-y: hidden;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\theight: 56rpx;\r\n\t\t\t\toverflow-y: hidden;\r\n\r\n\t\t\t\t.coupon {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\tdisplay: inline-block;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\twidth: 180rpx;\r\n\t\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.inner {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #D91B00;\r\n\t\t\t\t\t\tline-height: 56rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t\t&_info_wrap {\r\n\t\t\tposition: relative;\r\n\t\t\tmargin-top: -50rpx;\r\n\t\t\tpadding: 40rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 50rpx 50rpx 0 0;\r\n\r\n\t\t\t.desc {\r\n\t\t\t\tline-height: 42rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.label {\r\n\t\t\t\tmargin: 0 20rpx 20rpx 0;\r\n\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\theight: 48rpx;\r\n\t\t\t\tbackground: rgba(255, 171, 41, 0.1);\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #EB9003;\r\n\t\t\t\tline-height: 48rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_swiper {\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t.swiper {\r\n\t\t\t\twidth: 750rpx;\r\n\t\t\t\theight: 860rpx;\r\n\r\n\t\t\t\t.nav {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\twidth: 750rpx;\r\n\t\t\t\t\theight: 860rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.img {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\twidth: 750rpx;\r\n\t\t\t\t\theight: 860rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.swiper_dots {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 80rpx;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright: 0;\r\n\r\n\t\t\t\t.dots {\r\n\t\t\t\t\tmargin: 0 4rpx;\r\n\t\t\t\t\twidth: 14rpx;\r\n\t\t\t\t\theight: 4rpx;\r\n\t\t\t\t\tbackground: #D8D8D8;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.dots.active {\r\n\t\t\t\t\tbackground: var(--normal);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.page-foot~.container {\r\n\t\tpadding-bottom: 180rpx;\r\n\t}\r\n\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_detail.vue?vue&type=style&index=0&id=72bcdb74&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_detail.vue?vue&type=style&index=0&id=72bcdb74&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494342487\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}