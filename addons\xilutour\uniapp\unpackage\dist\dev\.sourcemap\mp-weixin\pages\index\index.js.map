{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/index/index.vue?3d9f", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/index/index.vue?b96d", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/index/index.vue?2ab0", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/index/index.vue?e679", "uni-app:///pages/index/index.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/index/index.vue?6816", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/index/index.vue?86af"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tourList", "data", "statusBarHeight", "messageCount", "bannerList", "adList", "swiper<PERSON><PERSON>rent", "navigationList", "sceneryList", "tourListMore", "page", "currentCity", "onLoad", "uni", "onShow", "onReachBottom", "onPullDownRefresh", "onShareAppMessage", "onShareTimeline", "onUnload", "methods", "bindCityChange", "url", "search", "swiper<PERSON><PERSON>e", "bindSearch", "refreshPage", "group", "loading", "success", "fetchTourList", "pagesize", "bannerjump", "navigation", "bindMessage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAA8zB,CAAgB,8xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2Fl1B;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAR;MACAS;QAAAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACAC;MACAH;MACAA;IACA;EACA;EACAI;IAAA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC,iDAEA;EACAC,6CAEA;EACAC;IACAN;EACA;EACAO;IACA;IACAC;MACAR;QACAS;MACA;IACA;IACA;IACAC;MACAV;QACAS;MACA;IACA;IACAE;MACA;IACA;IACAC;MACAZ;QACAS;MACA;IACA;IACAI;MAAA;MACA;MACA;QAAAJ;QAAArB;UAAA0B;QAAA;QAAAC;QAAAC;UACA;QACA;MAAA;;MAEA;MACA;QAAAP;QAAArB;UAAA0B;QAAA;QAAAC;QAAAC;UACA;QACA;MAAA;;MAEA;MACA;QAAAP;QAAArB;QAAA2B;QAAAC;UACA;QACA;MAAA;;MAEA;MACA;QAAAP;QAAArB;QAAA2B;QAAAC;UACA;QACA;MAAA;MACA;MACA;MACA;QAAAnB;MAAA;MACA;MACAG;IACA;IACAiB;MACA;QAAAC;MAAA,wDAEA;IACA;IACA;IACAC;MACA;QACA;MACA;MACAnB;QACAS;MACA;IACA;IACA;IACAW;MACA;MACA;MACA;QACAX;MACA;QACAA;MACA;QACAA;MACA;MACA;QACA;UACA;UACAA;QACA;UACA;UACAA;QACA;MACA;MACA;QACAT;UACAS;QACA;MACA;IACA;IACAY;MACArB;QACAS;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC7OA;AAAA;AAAA;AAAA;AAAihD,CAAgB,k5CAAG,EAAC,C;;;;;;;;;;;ACAriD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    tourList: function () {\n      return import(\n        /* webpackChunkName: \"components/tour-list/tour-list\" */ \"@/components/tour-list/tour-list.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.adList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"container\" style=\"padding-bottom: 30rpx;\">\r\n\r\n\t\t\t<image class=\"m-backdrop\" src=\"/static/icon/icon_bg1.png\" mode=\"widthFix\"></image>\r\n\t\t\t<view class=\"m-header\">\r\n\t\t\t\t<image class=\"m-backdrop\" src=\"/static/icon/icon_bg1.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view class=\"g-custom-nav flex-box plr30\"\r\n\t\t\t\t\t:style=\"{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }\">\r\n\t\t\t\t\t<view class=\"message\" :class=\"{active:messageCount>0}\" @click=\"bindMessage()\">\r\n\t\t\t\t\t\t<image src=\"/static/icon/icon_message.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<view>消息</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"search_box flex-box\">\r\n\t\t\t\t\t\t<view class=\"addr m-ellipsis\" @click=\"bindCityChange()\">{{currentCity?currentCity.name:''}}</view>\r\n\t\t\t\t\t\t<image class=\"icon_arrow\" src=\"/static/icon/icon_arrow.png\"></image>\r\n\t\t\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t\t\t<image class=\"icon_search\" src=\"/static/icon/icon_search.png\"></image>\r\n\t\t\t\t\t\t<input @click=\"bindSearch()\" disabled=\"true\" class=\"input flex-1\" type=\"text\" placeholder=\"出发城市/目的地\" placeholder-class=\"cola\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"pr\" :style=\"{ paddingTop: 'calc(90rpx + ' + statusBarHeight + 'px)' }\">\r\n\t\t\t\t<view class=\"xilu_swiper\">\r\n\t\t\t\t\t<swiper class=\"swiper\" :current=\"swiperCurrent\" circular previous-margin=\"38px\" next-margin=\"38px\" @change=\"swiperChange\">\r\n\t\t\t\t\t\t<swiper-item v-for=\"(item,index) in bannerList\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"nav\" :class=\"{'scale': swiperCurrent !==index}\" @click=\"bannerjump(item.minapp_url)\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item.thumb_image_text\" mode=\"aspectFill\" class=\"img\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t</swiper>\r\n\t\t\t\t\t<view class=\"swiper_dots flex-box flex-center\">\r\n\t\t\t\t\t\t<view class=\"dots\" v-for=\"(item,index) in bannerList\" :key=\"index\" :class=\"{'active': swiperCurrent == index}\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"plr40\">\r\n\t\t\t\t\t<view class=\"xilu_menu flex-box flex-between\">\r\n\t\t\t\t\t\t<view class=\"item\" @click=\"navigation(index)\" v-for=\"(item,index) in navigationList\" :key=\"index\">\r\n\t\t\t\t\t\t\t<image :src=\"item.icon_text\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t<view class=\"fs30 col-3\">{{item.name}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<navigator class=\"xilu_banner\" :url=\"adList[0].minapp_url\" hover-class=\"none\" v-if=\"adList.length>0\">\r\n\t\t\t\t\t\t<image :src=\"adList[0].thumb_image_text\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t</navigator>\r\n\r\n\t\t\t\t\t<view class=\"xilu_title flex-box\">\r\n\t\t\t\t\t\t<view class=\"flex-1 title\">景点推荐</view>\r\n\t\t\t\t\t\t<navigator class=\"more\" url=\"/pages/more_landscape/more_landscape\" hover-class=\"none\">更多+</navigator>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"xilu_landscape\">\r\n\t\t\t\t\t\t<navigator class=\"item\" :url=\"'/pages/landscape_detail/landscape_detail?id='+item.id\" hover-class=\"none\" v-for=\"(item,index) in sceneryList\">\r\n\t\t\t\t\t\t\t<image :src=\"item.thumb_image_text\" mode=\"aspectFill\">\r\n\t\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t\t<view class=\"name m-ellipsis mb20\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"col-price\" v-if=\"item.project\">\r\n\t\t\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t\t\t<text class=\"fs40\">{{item.project.salesprice}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"fs30\">起</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"xilu_title flex-box\">\r\n\t\t\t\t\t\t<view class=\"flex-1 title\">线路推荐</view>\r\n\t\t\t\t\t\t<navigator class=\"more\" url=\"/pages/popular_travel/popular_travel\" hover-class=\"none\">更多+</navigator>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"g_travel_list\">\n\t\t\t\t\t\t<tour-list :tourList=\"tourList\"></tour-list>\n\t\t\t\t\t\t<view class=\"nothing\" v-if=\"tourListMore.nothing\">\n\t\t\t\t\t\t\t<image src=\"/static/icon/icon_nothing.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t<text>暂无内容</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"g-btn3-wrap\" v-else>\n\t\t\t\t\t\t\t<view class=\"g-btn3\" @click=\"fetchTourList\">{{tourListMore.text}}</view>\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\n\tconst app = getApp();\n\timport tourList from '@/components/tour-list/tour-list.vue';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttourList\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatusBarHeight: 20,\n\t\t\t\tmessageCount: 0,\n\t\t\t\tbannerList:[],\n\t\t\t\tadList:[],\r\n\t\t\t\tswiperCurrent: 0,\n\t\t\t\tnavigationList:[],\n\t\t\t\tsceneryList:[],\n\t\t\t\ttourList:[],\n\t\t\t\ttourListMore:{page:1},\n\t\t\t\tcurrentCity: null,\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.statusBarHeight = getApp().globalData.statusBarHeight;\n\t\t\tthis.refreshPage();\n\t\t\tlet page = this;\n\t\t\tthis.currentCity = this.$core.getCurrentCity();\n\t\t\tuni.$on(app.globalData.Event.CurrentCityChange, function(currentCity) {\n\t\t\t\tpage.currentCity = currentCity;\n\t\t\t\tpage.refreshPage();\n\t\t\t})\r\n\t\t},\n\t\tonShow() {\n\t\t\tif(this.$core.getUserinfo()){\n\t\t\t\tthis.$util.getMessageCount(false).then(count=>{\n\t\t\t\t\tthis.messageCount = count\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\tonReachBottom() {\n\t\t\tthis.fetchTourList()\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tthis.refreshPage();\n\t\t},\n\t\tonShareAppMessage() {\n\t\t\t\n\t\t},\n\t\tonShareTimeline() {\n\t\t\t\n\t\t},\n\t\tonUnload() {\n\t\t\tuni.$off(app.globalData.Event.CurrentCityChange,this);\n\t\t},\r\n\t\tmethods: {\n\t\t\t//更换城市\n\t\t\tbindCityChange(){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t    url: '/pages/change_city/change_city'\n\t\t\t\t})\n\t\t\t},\n\t\t\t//搜索\n\t\t\tsearch() {\n\t\t\t    uni.navigateTo({\n\t\t\t        url: '/pages/search_result/search_result'\n\t\t\t    })\n\t\t\t},\r\n\t\t\tswiperChange(e) {\r\n\t\t\t\tthis.swiperCurrent = e.detail.current\r\n\t\t\t},\n\t\t\tbindSearch(){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/search/search'\n\t\t\t\t})\n\t\t\t},\n\t\t\trefreshPage(){\n\t\t\t\t//轮播\n\t\t\t\tthis.$core.get({url:'xilutour.banner/index',data:{group:'index'},loading:false,success:(ret)=>{\n\t\t\t\t\tthis.bannerList = ret.data;\n\t\t\t\t }});\n\t\t\t\t \n\t\t\t\t //中间图\n\t\t\t\t this.$core.get({url:'xilutour.banner/index',data:{group:'index2'},loading:false,success:(ret)=>{\n\t\t\t\t \tthis.adList = ret.data;\n\t\t\t\t  }});\n\t\t\t\t \n\t\t\t\t//中间金刚区\n\t\t\t\tthis.$core.get({url: 'xilutour.common/navication',data:{},loading:false,success:(ret)=>{\n\t\t\t\t\tthis.navigationList = ret.data;\n\t\t\t\t}})\n\t\t\t\t\n\t\t\t\t//热门景点\n\t\t\t\tthis.$core.get({url: 'xilutour.scenery/hot_list',data:{},loading:false,success:(ret)=>{\n\t\t\t\t\tthis.sceneryList = ret.data;\n\t\t\t\t}})\n\t\t\t\t//线路推荐\n\t\t\t\tthis.tourList = [];\n\t\t\t\tthis.tourListMore = {page: 1};\n\t\t\t\tthis.fetchTourList();\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t},\n\t\t\tfetchTourList(){\n\t\t\t\tthis.$util.fetch(this, 'xilutour.tour/recommend_list', {pagesize:10}, 'tourListMore', 'tourList', 'data', data=>{\n\t\t\t\t  \n\t\t\t\t})\n\t\t\t},\n\t\t\t//轮播图\n\t\t\tbannerjump(url){\n\t\t\t\tif(url.trim() == ''){\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: url\n\t\t\t\t})\n\t\t\t},\n\t\t\t//金刚区\n\t\t\tnavigation(index){\n\t\t\t\tlet item = this.navigationList[index];\n\t\t\t\tlet url = ''\n\t\t\t\tif(item.module_type == 3){\n\t\t\t\t\turl = '/pages/article_list/article_list'\n\t\t\t\t}else if(item.module_type==2){\n\t\t\t\t\turl = '/pages/more_landscape/more_landscape';\n\t\t\t\t}else{\n\t\t\t\t\turl = '/pages/popular_travel/popular_travel';\n\t\t\t\t}\n\t\t\t\tif(item.module_type != 3){\n\t\t\t\t\tif(item.type==1){\n\t\t\t\t\t\t//标签\n\t\t\t\t\t\turl += '?tag_id='+item.type_value;\n\t\t\t\t\t}else if(item.type == 2){\n\t\t\t\t\t\t//分类\n\t\t\t\t\t\turl += '?category_id='+item.type_value\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(url){\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: url\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tbindMessage(){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/my_message/my_message'\n\t\t\t\t})\n\t\t\t}\n\t\t\t\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.xilu {\r\n\t\t&_swiper {\r\n\t\t\tposition: relative;\r\n\t\t\tpadding: 30rpx 0 50rpx;\r\n\r\n\t\t\t.swiper {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 750rpx;\r\n\t\t\t\theight: 320rpx;\r\n\r\n\t\t\t\t.nav {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\twidth: 600rpx;\r\n\t\t\t\t\theight: 320rpx;\r\n\t\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.nav.scale {\r\n\t\t\t\t\ttransform: scale(0.9);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.img {\r\n\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\twidth: 600rpx;\r\n\t\t\t\t\theight: 320rpx;\r\n\t\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.swiper_dots {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 30rpx;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright: 0;\r\n\r\n\t\t\t\t.dots {\r\n\t\t\t\t\tmargin: 0 4rpx;\r\n\t\t\t\t\twidth: 14rpx;\r\n\t\t\t\t\theight: 4rpx;\r\n\t\t\t\t\tbackground: #D8D8D8;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.dots.active {\r\n\t\t\t\t\tbackground: #333333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_menu {\r\n\t\t\tmargin: 0 0 25rpx;\r\n\r\n\t\t\timage {\r\n\t\t\t\tmargin: 0 auto 30rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 100rpx;\r\n\t\t\t\theight: 100rpx;\r\n\t\t\t\tborder-radius: 32rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_banner {\r\n\t\t\tmargin: 0 0 25rpx;\r\n\r\n\t\t\timage {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 670rpx;\r\n\t\t\t\theight: 198rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_title {\r\n\t\t\tmargin: 0 0 40rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #101010;\r\n\t\t\t\tline-height: 42rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.more {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #898989;\r\n\t\t\t\tline-height: 32rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_landscape {\r\n\t\t\twhite-space: nowrap;\r\n\t\t\toverflow-x: scroll;\r\n\t\t\toverflow-y: hidden;\r\n\t\t\tfont-size: 0;\r\n\t\t\tmargin: 0 0 60rpx;\r\n\r\n\t\t\t.item {\r\n\t\t\t\tmargin: 0 20rpx 0 0;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 290rpx;\r\n\t\t\t\tvertical-align: top;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\tmargin: 0 0 20rpx;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\twidth: 290rpx;\r\n\t\t\t\t\theight: 330rpx;\r\n\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.name {\r\n\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tcolor: #101010;\r\n\t\t\t\t\tline-height: 38rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341197\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}