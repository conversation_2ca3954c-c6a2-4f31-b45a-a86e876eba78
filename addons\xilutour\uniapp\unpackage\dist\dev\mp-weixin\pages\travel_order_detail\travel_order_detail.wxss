.g_order_info.data-v-554a44d6 {
  padding-bottom: 5rpx;
}
.g_order_foot1.data-v-554a44d6 {
  padding-left: 30rpx;
}
.xilu_status.data-v-554a44d6 {
  padding: 30rpx;
  margin: 0 0 40rpx;
  background: rgba(5, 185, 174, 0.1);
  border-radius: 22rpx;
  font-size: 28rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #555555;
  line-height: 30rpx;
}
.xilu_status .title.data-v-554a44d6 {
  margin: 0 0 20rpx;
  font-size: 34rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: var(--normal);
  line-height: 36rpx;
}
.xilu_goods .btn_apply.data-v-554a44d6 {
  width: 158rpx;
  height: 70rpx;
  border-radius: 25rpx;
  border: 1rpx solid var(--normal);
  font-size: 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: var(--normal);
  line-height: 70rpx;
  text-align: center;
}
.xilu_goods .img.data-v-554a44d6 {
  margin-right: 30rpx;
  display: block;
  width: 180rpx;
  height: 180rpx;
  border-radius: 15rpx;
}
.xilu_travel_time.data-v-554a44d6 {
  padding: 0 40rpx 0 30rpx;
  margin: 0 0 30rpx;
  height: 120rpx;
  background: #F7F9FB;
  border-radius: 20rpx;
}
.xilu_travel_time .line.data-v-554a44d6 {
  margin: 0 60rpx;
  width: 25rpx;
  height: 2rpx;
  background-color: var(--normal);
}
.xilu_info_box.data-v-554a44d6 {
  margin: 0 0 30rpx;
  padding: 30rpx 30rpx 40rpx;
  background: #F7F9FB;
  border-radius: 25rpx;
}
.xilu_info_box .identity1.data-v-554a44d6 {
  width: 60rpx;
  height: 36rpx;
  background: #FFAB29;
  border-radius: 5rpx;
  font-size: 24rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 36rpx;
  text-align: center;
}
.xilu_info_box .identity2.data-v-554a44d6 {
  width: 60rpx;
  height: 36rpx;
  background: var(--normal);
  border-radius: 5rpx;
  font-size: 24rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 36rpx;
  text-align: center;
}
.xilu .page-foot ~ .container.data-v-554a44d6 {
  padding-bottom: 170rpx;
}
.xilu .container.data-v-554a44d6 {
  padding-top: 30rpx;
  padding-left: 40rpx;
  padding-right: 40rpx;
}

