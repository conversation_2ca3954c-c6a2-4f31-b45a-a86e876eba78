{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/more_landscape/more_landscape.vue?f400", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/more_landscape/more_landscape.vue?f829", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/more_landscape/more_landscape.vue?e68b", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/more_landscape/more_landscape.vue?405b", "uni-app:///pages/more_landscape/more_landscape.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "sceneryList", "data", "statusBarHeight", "categoryList", "bannerList", "currentCity", "sceneryListMore", "page", "query", "q", "sort", "order", "category_id", "searchTab", "isShowSelect", "onLoad", "uni", "onShareAppMessage", "onShareTimeline", "onReachBottom", "onUnload", "methods", "navBack", "tabClick", "bindCityChange", "url", "search", "bindChangeSearchTab", "events", "searchSuccess", "success", "res", "bindChangeSort", "refreshPage", "group", "loading", "refresh", "fetch", "closeSelect"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;;;AAG7D;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAu0B,CAAgB,uyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACqE31B;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAL;MACAM;QAAAC;MAAA;MACAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;IACA;IACAR;IACAS;MACAT;MACAA;IACA;EACA;EACAU,iDAEA;EACAC,6CAEA;EACAC;IACA;EACA;EACAC;IACAJ;EACA;EACAK;IACAC;MACAN;IACA;IACAO;MACA;MACA;IACA;IACA;IACAC;MACAR;QACAS;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;QACAX;UACAS;UACAG;YACAC;cACA;cACA;YACA;UACA;UACAC;YACAC;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QAAAR;QAAAxB;UAAAiC;QAAA;QAAAC;QAAAL;UACA;QACA;MAAA;MACA;MACA;QAAAL;QAAAxB;QAAAkC;QAAAL;UACA;QACA;MAAA;MACA;MACA;MAEAd;IACA;IACAoB;MAAA;MACA;MACA;QAAA7B;MAAA;MACA;MACA;IACA;IACA8B;MACA;MACA7B;MACA,mHAEA;IACA;IACA8B;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/more_landscape/more_landscape.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/more_landscape/more_landscape.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./more_landscape.vue?vue&type=template&id=6b0d96b4&scoped=true&\"\nvar renderjs\nimport script from \"./more_landscape.vue?vue&type=script&lang=js&\"\nexport * from \"./more_landscape.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6b0d96b4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/more_landscape/more_landscape.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./more_landscape.vue?vue&type=template&id=6b0d96b4&scoped=true&\"", "var components\ntry {\n  components = {\n    sceneryList: function () {\n      return import(\n        /* webpackChunkName: \"components/scenery-list/scenery-list\" */ \"@/components/scenery-list/scenery-list.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./more_landscape.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./more_landscape.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"container\">\r\n\t\t\t<image class=\"m-backdrop\" src=\"/static/icon/icon_bg1.png\" mode=\"widthFix\"></image>\r\n\t\t\t<view class=\"m-header\">\r\n\t\t\t\t<image class=\"m-backdrop\" src=\"/static/icon/icon_bg1.png\" mode=\"widthFix\"></image>\r\n\t\t\t\t<view class=\"g-custom-nav flex-box plr30\"\r\n\t\t\t\t\t:style=\"{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }\">\r\n\t\t\t\t\t<image @click=\"navBack\" src=\"/static/icon/icon_back.png\" mode=\"aspectFit\" class=\"icon_back\"></image>\r\n\t\t\t\t\t<view class=\"search_box flex-box\">\r\n\t\t\t\t\t\t<view class=\"addr m-ellipsis\" @click=\"bindCityChange\">{{currentCity?currentCity.name:''}}</view>\r\n\t\t\t\t\t\t<image class=\"icon_arrow\" src=\"/static/icon/icon_arrow.png\"></image>\r\n\t\t\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t\t\t<image class=\"icon_search\" src=\"/static/icon/icon_search.png\"></image>\r\n\t\t\t\t\t\t<input class=\"input flex-1\" type=\"text\" v-model=\"query.q\" placeholder=\"景点名称\" confirm-type=\"search\" @confirm=\"search\" placeholder-class=\"cola\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"g_tab\">\r\n\t\t\t\t\t<view class=\"item\" :class=\"{'active': query.category_id == -1}\" @click=\"tabClick(-1)\">推荐</view>\r\n\t\t\t\t\t<view class=\"item\" :class=\"{'active': query.category_id == item.id}\" @click=\"tabClick(item.id)\"  v-for=\"(item,index) in categoryList\">{{item.name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"g_select flex-box\">\r\n\t\t\t\t\t<view class=\"flex-box flex-center flex-1\" @click=\"bindChangeSearchTab(0)\" :class=\"{active: searchTab==0}\">\r\n\t\t\t\t\t\t<view>综合</view>\r\n\t\t\t\t\t\t<image :src=\"'/static/icon/icon_arrow1' + (searchTab==0 ? 'on' : '') + '.png'\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box flex-center flex-1\" @click=\"bindChangeSearchTab(1)\" :class=\"{active: searchTab==1}\">\r\n\t\t\t\t\t\t<view>价格</view>\r\n\t\t\t\t\t\t<image class=\"icon_price\" :class=\"{'rotate': searchTab==1 && query.order=='desc'}\" src=\"/static/icon/icon_arrow3.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box flex-center flex-1\" @click=\"bindChangeSearchTab(2)\" :class=\"{active: searchTab==2}\">\r\n\t\t\t\t\t\t<view>热门</view>\r\n\t\t\t\t\t\t<image :src=\"'/static/icon/icon_arrow1' + (searchTab==2 ? 'on' : '') + '.png'\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box flex-center flex-1\" @click=\"bindChangeSearchTab(3)\" :class=\"{active: searchTab==3}\">\r\n\t\t\t\t\t\t<view>筛选</view>\r\n\t\t\t\t\t\t<image src=\"/static/icon/icon_arrow1.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"g_select_wrap\" v-show=\"isShowSelect\">\r\n\t\t\t\t\t<block v-if=\"searchTab == 0\">\r\n\t\t\t\t\t\t<view class=\"item\" :class=\"{active:query.sort=='weigh'}\" @click=\"bindChangeSort('weigh')\">综合</view>\r\n\t\t\t\t\t\t<view class=\"item\" :class=\"{active:query.sort=='points'}\" @click=\"bindChangeSort('points')\">推荐</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-if=\"searchTab == 2\">\r\n\t\t\t\t\t\t<view class=\"item\" :class=\"{active:query.sort=='updatetime'}\" @click=\"bindChangeSort('updatetime')\">最新</view>\r\n\t\t\t\t\t\t<view class=\"item\" :class=\"{active:query.sort=='hot_status'}\" @click=\"bindChangeSort('hot_status')\">热门</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"g_mask\" v-show=\"isShowSelect\" @click=\"closeSelect\"></view>\r\n\r\n\t\t\t<view class=\"pr plr40 pb40\" :style=\"{ paddingTop: 'calc(316rpx + ' + statusBarHeight + 'px)' }\">\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"g_landscape_list\">\r\n\t\t\t\t\t<scenery-list :sceneryList=\"sceneryList\"></scenery-list>\n\t\t\t\t\t<view class=\"g-btn3-wrap\">\r\n\t\t\t\t\t\t<view class=\"g-btn3\" @click=\"fetch\">{{sceneryListMore.text}}</view>\r\n\t\t\t\t\t</view>\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\n\timport sceneryList from '@/components/scenery-list/scenery-list.vue';\n\tconst app = getApp();\r\n\texport default {\n\t\tcomponents: {\n\t\t\tsceneryList\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatusBarHeight: 20,\r\n\t\t\t\tcategoryList:[],\n\t\t\t\tbannerList:[],\n\t\t\t\tcurrentCity:null,\n\t\t\t\tsceneryList:[],\n\t\t\t\tsceneryListMore:{page:1},\n\t\t\t\tquery:{q:'',sort: 'weigh',order: 'desc',category_id: -1},\n\t\t\t\tsearchTab: 0,\r\n\t\t\t\tisShowSelect: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.statusBarHeight = getApp().globalData.statusBarHeight;\n\t\t\tif(options.category_id){\n\t\t\t\tthis.query.category_id = options.category_id;\n\t\t\t}\n\t\t\tif(options.tag_id){\n\t\t\t\tthis.query.tag_ids = options.tag_id;\n\t\t\t}\n\t\t\tlet page = this;\n\t\t\tthis.currentCity = this.$core.getCurrentCity();\n\t\t\tpage.refreshPage();\n\t\t\tuni.$on(app.globalData.Event.CurrentCityChange, function(currentCity) {\n\t\t\t\tpage.currentCity = currentCity;\n\t\t\t\tpage.refresh();\n\t\t\t})\r\n\t\t},\n\t\tonShareAppMessage() {\n\t\t\t\n\t\t},\n\t\tonShareTimeline() {\n\t\t\t\n\t\t},\n\t\tonReachBottom() {\n\t\t\tthis.fetch();\n\t\t},\n\t\tonUnload() {\n\t\t\tuni.$off(app.globalData.Event.CurrentCityChange,this);\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tnavBack(){\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\n\t\t\ttabClick(id) {\n\t\t\t\tthis.query.category_id = id;\n\t\t\t\tthis.refresh();\n\t\t\t},\n\t\t\t//更换城市\n\t\t\tbindCityChange(){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t    url: '/pages/change_city/change_city'\n\t\t\t\t})\n\t\t\t},\n\t\t\tsearch(){\n\t\t\t\tthis.refresh()\n\t\t\t},\n\t\t\t//排序,0=综合（弹窗），1=价格，2=热门（弹窗)\n\t\t\tbindChangeSearchTab(searchTabIndex){\n\t\t\t\tif(searchTabIndex == 0 || searchTabIndex == 2){\n\t\t\t\t\tthis.isShowSelect = this.isShowSelect&&this.searchTab == searchTabIndex?false:true;\n\t\t\t\t}else if(searchTabIndex == 1){\n\t\t\t\t\tthis.query.sort = 'salesprice';\n\t\t\t\t\tthis.query.order = this.query.order == 'asc'?'desc':'asc';\n\t\t\t\t\tthis.isShowSelect = false;\n\t\t\t\t\tthis.refresh();\n\t\t\t\t}else if(searchTabIndex == 3){\n\t\t\t\t\tlet query = this.query;\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl:'/pages/filtrate/filtrate?type=scenery',\n\t\t\t\t\t\tevents:{\n\t\t\t\t\t\t\tsearchSuccess: data=>{\n\t\t\t\t\t\t\t\tthis.query = data;\n\t\t\t\t\t\t\t\tthis.refresh();\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\t\tres.eventChannel.emit(\"searchTransform\",query)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tthis.searchTab = searchTabIndex;\n\t\t\t},\n\t\t\t//二级排序\n\t\t\tbindChangeSort(sort){\n\t\t\t\tthis.query.sort = sort;\n\t\t\t\tthis.query.order = 'desc';\n\t\t\t\tthis.isShowSelect = false;\n\t\t\t\tthis.refresh();\n\t\t\t},\n\t\t\trefreshPage(){\n\t\t\t\t//轮播\n\t\t\t\tthis.$core.get({url:'xilutour.banner/index',data:{group:'scenery_index'},loading:false,success:(ret)=>{\n\t\t\t\t\tthis.bannerList = ret.data;\n\t\t\t\t }});\n\t\t\t\t //分类\n\t\t\t\t this.$core.get({url:'xilutour.common/scenery_category',data:{},loading:false,success:(ret)=>{\n\t\t\t\t \tthis.categoryList = ret.data;\n\t\t\t\t  }});\n\t\t\t\t  //列表\n\t\t\t\t  this.refresh();\n\t\t\t\t  \n\t\t\t\t  uni.stopPullDownRefresh();\n\t\t\t},\n\t\t\trefresh(cacheQuery=false){\n\t\t\t\tthis.sceneryList = [];\n\t\t\t\tthis.sceneryListMore = {page:1};\n\t\t\t\tif(cacheQuery) this.query = {};\n\t\t\t\tthis.fetch();\n\t\t\t},\n\t\t\tfetch(){\n\t\t\t\tlet query = this.query;\n\t\t\t\tquery.pagesize = 10;\n\t\t\t\tthis.$util.fetch(this, 'xilutour.scenery/lists', query, 'sceneryListMore', 'sceneryList', 'data', data=>{\n\t\t\t\t  \n\t\t\t\t})\n\t\t\t},\r\n\t\t\tcloseSelect() {\r\n\t\t\t\tthis.isShowSelect = false\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\r\n</style>"], "sourceRoot": ""}