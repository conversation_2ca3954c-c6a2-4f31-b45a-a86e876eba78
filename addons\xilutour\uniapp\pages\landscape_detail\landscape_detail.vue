<template>
	<view class="xilu">
		<view class="page-foot">
			<view class="g_order_foot1 flex-box">
				<navigator class="nav" url="/pages/index/index" open-type="switchTab" hover-class="none">
					<image src="/static/icon/icon_btn1.png" mode="aspectFit" class="icon"></image>
					<view>首页</view>
				</navigator>
				<button class="nav" hover-class="none" open-type="contact">
					<image src="/static/icon/icon_btn2.png" mode="aspectFit" class="icon"></image>
					<view>客服</view>
				</button>
				<view class="nav" @click="toggleCollection()">
					<image :src="'/static/icon/icon_btn3'+(scenery.is_collection_count == 1 ? 'on' : '')+'.png'" mode="aspectFit"
						class="icon"></image>
					<view>{{scenery.is_collection_count == 1?'已收藏':'收藏'}}</view>
				</view>
				<view @click="bindBuy()" class="btn1">立即购买</view>
			</view>
		</view>

		<view class="container">
			<view class="m-header" :style="{ background: setCol ? 'var(--normal)' : 'unset'}">
				<view class="g-custom-nav flex-box plr30"
					:style="{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }">
					<image @click="navBack" src="/static/icon/icon_back1.png" mode="aspectFit" class="icon_back"></image>
					<view class="flex-1 pr35 mr30 fs26 col-f tc">{{barTitle}}</view>
				</view>
			</view>

			<view class="xilu_swiper">
				<swiper class="swiper" :current="swiperCurrent" circular @change="swiperChange">
					<swiper-item v-for="(img,index) in scenery.images_text" :key="index">
						<view class="nav" :class="{'scale': swiperCurrent !==index}">
							<image :src="img" mode="aspectFill" class="img"></image>
						</view>
					</swiper-item>
				</swiper>
				<view class="swiper_dots flex-box flex-center">
					<view class="dots" v-for="(img,index) in scenery.images_text" :key="index"
						:class="{'active': swiperCurrent == index}">
					</view>
				</view>
			</view>

			<view class="xilu_info_wrap">
				<view class="g_travel_list">
					<view class="flex-box mb30">
						<view class="flex-1 col-price" v-if="scenery.min_project">
							<text class="fs30">¥</text>
							<text class="fs40">{{scenery.min_project.salesprice}}</text>
							<text class="fs30">起</text>
						</view>
						<view class="fs28 col-a">{{scenery.view_count}}人浏览</view>
					</view>
					<view class="flex-box flex-wrap pb10" v-if="scenery.tags.length>0">
						<view class="label" v-for="(tag,index) in scenery.tags" :key="index">{{tag.name}}</view>
					</view>
					<view class="mb20">
						<text class="level" v-if="scenery.level">{{scenery.level.name}}</text>
						<text class="fs36 col-10">{{scenery.name}}</text>
					</view>
					<view class="desc">{{scenery.introduce}}</view>
				</view>
			</view>

			<view class="xilu_coupon_box">
				<view class="opening_box">
					<view class="flex-box mb30">
						<view class="col-normal fs30 mr30">开放时间</view>
						<view class="fs30 col-5 mr15">{{scenery.work_time}}</view>
						<image class="icon" v-if="scenery.tel" @click="callphone()" src="/static/icon/icon_phone.png"
							mode="aspectFit"></image>
					</view>
					<view class="flex-box flex-align-start">
						<view class="flex-1 mr10 fs30 col-5">
							{{scenery.city?scenery.city.name:''}}{{scenery.district?scenery.district.name:''}}{{scenery.address}}
						</view>
						<image v-if="scenery.lat" @click="bindOpenLocation()" class="icon" src="/static/icon/icon_go.png"
							mode="aspectFit"></image>
					</view>
				</view>
				<view v-if="scenery.coupons.length>0" class="flex-box ptb25 m-hairline--top" @click="couponPopOpen">
					<view class="col-price fs30 mr20">优惠券</view>
					<view class="coupon_list">
						<view class="coupon" v-for="(item,index) in scenery.coupons">
							<image src="/static/icon/icon_coupon_bg3.png" mode="aspectFill"></image>
							<view class="inner">{{item.name}}</view>
						</view>
					</view>
					<image src="/static/icon/icon_arrow2.png" mode="aspectFit" class="g-icon30"></image>
				</view>
			</view>

			<scroll-view class="g_tab" :scroll-x="true" :scroll-y="false" :scroll-left="scrollLeft" @scroll="scrollFunc">
				<view class="item" :class="{'active': tabIdx == 1}" @click="tabClick(1)">景区项目</view>
				<view class="item" :class="{'active': tabIdx == 2}" @click="tabClick(2)">景区详情</view>
				<view class="item" :class="{'active': tabIdx == 3}" @click="tabClick(3)">费用说明</view>
				<view class="item" :class="{'active': tabIdx == 4}" @click="tabClick(4)">行程必看</view>
				<view class="item" :class="{'active': tabIdx == 5}" @click="tabClick(5)">用户评价({{total}})</view>
			</scroll-view>

			<view class="xilu_detail_box">
				<view v-if="tabIdx == 1">
					<view class="project flex-box" v-if="scenery.projects.length>0" v-for="(project,index) in scenery.projects"
						:key="index">
						<image :src="project.thumb_image_text" mode="aspectFill"></image>
						<view class="flex-1">
							<view class="fs34 col-10 mb20">{{project.name}}</view>
							<view class="label_wrap pb10" v-if="project.tags.length>0">
								<view class="label" v-for="tag in project.tags_text">{{tag}}</view>
							</view>
							<view class="flex-box flex-align-end">
								<view class="flex-1 col-price">
									<text class="fs30">低至¥</text>
									<text class="fs40">{{project.salesprice}}</text>
								</view>
								<view class="btn" @click="bindAppoint(project.id)">预定</view>
							</view>
						</view>
					</view>
				</view>

				<view v-if="tabIdx == 2">
					<rich-text :nodes="scenery.content"></rich-text>
				</view>

				<view v-if="tabIdx == 3">
					<view class="detail fs30 col-3">
						<view class="fs36 col-10 mb30">费用包含</view>
						<view>
							<rich-text :nodes="scenery.statement"></rich-text>
						</view>
					</view>
				</view>

				<view v-if="tabIdx == 4">
					<view class="detail fs30 col-3">
						<rich-text :nodes="scenery.travel_know"></rich-text>
					</view>
				</view>

				<view v-if="tabIdx == 5">
					<view class="g_comment" v-for="(comment,index) in commentList" :key="index">
						<view class="flex-box">
							<image :src="comment.user.avatar" mode="aspectFill" class="head"></image>
							<view class="flex-1">
								<view class="fs30 col-5 mb20">{{comment.user.nickname}}</view>
								<view class="fs24 col-89">{{comment.createtime_text}}</view>
							</view>
							<u-rate :disabled="true" :readonly="true" :value="4" :count="4" :size="18" :gutter="0"
								active-icon="/static/icon/icon_star.png"></u-rate>
						</view>
						<view class="text1 pt25">{{comment.content}}</view>
						<view class="flex-box flex-wrap" v-if="comment.images_text.length>0">
							<image @click="bindPrev(index,index2)" v-for="(img,index2) in comment.images_text" :src="img"
								mode="aspectFill" class="img1"></image>
						</view>
					</view>

					<view class="g-btn3-wrap">
						<view class="g-btn3" @click="fetch">{{commentListMore.text}}</view>
					</view>

				</view>

			</view>


			<uni-popup ref="couponPopup" type="bottom">
				<view class="g_coupon_pop">
					<view class="fs30 col-10 tc mb30">优惠券</view>
					<image src="/static/icon/icon_close.png" mode="aspectFit" class="icon_close" @click="couponPopClose"></image>

					<view class="pop_coupon_wrap">
						<view class="pop_coupon" v-for="(coupon,index) in scenery.coupons" :key="index">
							<image src="/static/icon/icon_coupon_bg1.png" mode="aspectFill" class="bg"></image>
							<view class="inner flex-box">
								<view class="left">
									<view class="fwb mb20">
										<text class="fs24">¥</text>
										<text class="fs50">{{coupon.money}}</text>
									</view>
									<view class="man">满{{coupon.at_least}}可用</view>
								</view>
								<view class="right flex-1 flex-box">
									<view class="flex-1">
										<view class="fs30 mb20">{{coupon.name}}</view>
										<view class="fs24">{{coupon.use_end_time_text}}到期</view>
									</view>
									<view class="use" v-if="coupon.is_receive_count == 1">已领取</view>
									<view class="use" @click="bindReceive(index)" v-else>领取</view>
								</view>
							</view>
						</view>

					</view>

				</view>
			</uni-popup>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				barTitle: '风景详情',
				statusBarHeight: 20,
				swiperCurrent: 0,
				tabIdx: 1,
				sceneryId: 0,
				scenery: {
					work_time: '',
					address: '',
					name: '',
					view_count: 0,
					worktime: '',
					tags: [],
					projects: [],
					coupons: []
				},
				total: 0,
				commentList: [],
				commentListMore: {
					page: 1
				},
				setCol: false,
				scrollLeft:0
			};
		},
		onLoad(options) {
			this.statusBarHeight = getApp().globalData.statusBarHeight;
			this.sceneryId = options.id;
			this.fetchDetail();
			this.fetch();
		},
		onReachBottom() {
			if (this.tabIdx == 5) {
				this.fetch();
			}
		},
		onShareAppMessage(e) {
			let userinfo = this.$core.getUserinfo();
			let path = '/pages/landscape_detail/landscape_detail?id=' + this.sceneryId;
			if (userinfo) {
				path += '&pid=' + userinfo.pid
			}
			return {
				title: this.scenery.name,
				path: path,
				//imageUrl: this.scenery.thumb_image_text
			}
		},
		onShareTimeline() {
			let userinfo = this.$core.getUserinfo();
			let query = "id=" + this.sceneryId
			if (userinfo) {
				query += '&pid=' + userinfo.pid
			}
			return {
				title: this.scenery.name,
				query: query
			}
		},
		onPageScroll(e) {
			if (e.scrollTop > 350) {
				this.setCol = true
			} else {
				this.setCol = false
			}
		},
		methods: {
			swiperChange(e) {
				this.swiperCurrent = e.detail.current
			},
			tabClick(i) {
				this.tabIdx = i;
			},
			navBack() {
				uni.navigateBack()
			},
			fetchDetail() {
				this.$core.post({
					url: 'xilutour.scenery/detail',
					data: {
						scenery_id: this.sceneryId
					},
					loading: false,
					success: ret => {
						ret.data.content = this.$core.richTextnew(ret.data.content);
						ret.data.statement = this.$core.richTextnew(ret.data.statement);
						ret.data.travel_know = this.$core.richTextnew(ret.data.travel_know);
						this.scenery = ret.data;
						this.barTitle = ret.data.name;
					},
					fail: err => {
						console.log(err);
						uni.showModal({
							title:'提示',
							content: err.msg,
							showCancel:false,
							complete() {
								uni.navigateBack()
							}
						})
						return false;
					}
				});
			},
			fetch() {
				let query = {
					scenery_id: this.sceneryId
				};
				query.pagesize = 10;
				this.$util.fetch(this, 'xilutour.scenery_comment/lists', query, 'commentListMore', 'commentList', 'data',
					data => {
						this.total = data.total;
					})
			},
			//拨打电话
			callphone() {
				let tel = this.scenery.tel;
				uni.makePhoneCall({
					phoneNumber: tel
				})
			},
			//导航
			bindOpenLocation() {
				let scenery = this.scenery;
				let address = (scenery.city ? scenery.city.name : '') + (scenery.district ? scenery.district.name : '') + (
					scenery.address)
				uni.openLocation({
					latitude: Number(scenery.lat),
					longitude: Number(scenery.lng),
					name: scenery.name,
					address: address
				})
			},
			//收藏
			toggleCollection() {
				if (!this.$core.getUserinfo(true)) {
					return;
				}
				this.$core.post({
					url: 'xilutour.scenery/toggle_collection',
					data: {
						scenery_id: this.scenery.id
					},
					success: ret => {
						this.scenery.is_collection_count = ret.data.is_collection_count;
					},
					fail: err => {}
				});
			},
			//领取优惠券
			bindReceive(index) {
				let coupons = this.scenery.coupons;
				let coupon = coupons[index];
				if (!this.$core.getUserinfo(true)) {
					return;
				}
				this.$core.post({
					url: 'xilutour.coupon/receive',
					data: {
						coupon_id: coupon.id
					},
					success: ret => {
						uni.showToast({
							title: '领取成功',
						})
						coupons[index].is_receive_count = 1;
						this.scenery.coupons = coupons;
					},
					fail: err => {
						uni.showModal({
							title: '提示',
							content: err.msg,
						})
						return false;
					}
				});
			},
			bindBuy() {
				this.tabIdx = 1;
				this.scrollLeft = 0
				let view = uni.createSelectorQuery().in(this).select(".g_tab");
				setTimeout(() => {
					uni
						.createSelectorQuery()
						.selectViewport()
						.scrollOffset((res) => {
							console.log("竖直滚动位置" + res.scrollTop);
							view
								.boundingClientRect((data) => {
									console.log("节点离页面顶部的距离为" + data.top);

									uni.pageScrollTo({
										scrollTop: res.scrollTop + data.top - this.statusBarHeight - 45,
										duration: 500
									})

								})
								.exec();
						})
						.exec();
				}, 500)
			},
			scrollFunc(e){
				this.scrollLeft = e.detail.scrollLeft;
				console.log(e);
			},
			//预定
			bindAppoint(projectId) {
				if (!this.$core.getUserinfo(true)) {
					return;
				}
				uni.navigateTo({
					url: '/pages/landscape_pay/landscape_pay?scenery_id=' + this.scenery.id + '&project_id=' + projectId
				})
			},
			// 打开优惠券弹窗
			couponPopOpen() {
				this.$refs.couponPopup.open();
			},
			// 关闭优惠券弹窗
			couponPopClose() {
				this.$refs.couponPopup.close();
			},

			bindPrev(index, index2) {
				uni.previewImage({
					urls: this.commentList[index].images_text,
					current: index2
				})
			},
		}
	}
</script>

<style lang="less" scoped>
	.g_order_foot1 {
		margin: 0 auto 40rpx;
	}

	.xilu {
		&_detail_box {
			padding: 30rpx;
			background: #F7F9FB;

			.img_detail {
				margin: 0 0 20rpx;
				display: block;
				width: 100%;
			}

			.step_wrap {
				.step {
					position: relative;
					margin: 0 0 30rpx 50rpx;
					padding: 30rpx 30rpx 40rpx;
					width: 640rpx;
					background: #FFFFFF;
					box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
					border-radius: 30rpx;

					.img_step {
						display: block;
						max-width: 100%;
						margin: 30rpx 0 0;
					}
				}

				.step::before {
					position: absolute;
					top: 0;
					left: -40rpx;
					content: '';
					width: 2rpx;
					height: calc(100% + 30rpx);
					background-color: rgba(255, 171, 41, 0.2);
				}

				.step::after {
					position: absolute;
					top: 38rpx;
					left: -50rpx;
					content: '';
					width: 22rpx;
					height: 22rpx;
					background: #FFAB29;
					border-radius: 50%;
				}
			}

			.detail {
				padding: 30rpx;
				background: #FFFFFF;
				box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
				border-radius: 30rpx;
				line-height: 44rpx;
			}

			.project {
				padding: 30rpx;
				margin: 0 0 30rpx;
				background: #FFFFFF;
				box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
				border-radius: 20rpx;

				image {
					margin-right: 30rpx;
					display: block;
					width: 200rpx;
					height: 200rpx;
					border-radius: 25rpx;
				}

				.label_wrap {
					overflow-x: scroll;
					overflow-y: hidden;
					white-space: nowrap;
					font-size: 0;

					.label {
						margin: 0 20rpx 0 0;
						padding: 0 10rpx;
						display: inline-block;
						height: 48rpx;
						background: rgba(255, 171, 41, 0.1);
						border-radius: 8rpx;
						font-size: 28rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #EB9003;
						line-height: 48rpx;
					}
				}

				.btn {
					width: 100rpx;
					height: 60rpx;
					background: var(--normal);
					border-radius: 20rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 60rpx;
					text-align: center;
				}
			}

		}

		&_regis {
			padding: 0 40rpx;
			overflow-x: scroll;
			overflow-y: hidden;
			white-space: nowrap;

			.item {
				position: relative;
				margin: 0 30rpx 0 0;
				padding: 20rpx 0 0;
				display: inline-block;
				width: 270rpx;
				height: 312rpx;
				background: #FFFFFF;
				border-radius: 25rpx;
				border: 1px solid #EEEEEE;

				.line {
					width: 12rpx;
					height: 2rpx;
					background-color: var(--normal);
				}

				.btn {
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					width: 270rpx;
					height: 60rpx;
					background-color: var(--normal);
					border-radius: 2rpx 2rpx 25rpx 25rpx;
					font-size: 30rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 60rpx;
					text-align: center;
				}
			}
		}

		&_coupon_box {
			margin: 0 30rpx 15rpx;
			padding: 0 30rpx;
			width: 690rpx;
			background: #F7F9FB;
			border-radius: 20rpx;

			.coupon_list {
				flex: 1;
				overflow-x: scroll;
				white-space: nowrap;
				overflow-y: hidden;
				height: 56rpx;

				.coupon {
					position: relative;
					margin-right: 20rpx;
					display: inline-block;

					image {
						display: block;
						width: 180rpx;
						height: 56rpx;
					}

					.inner {
						position: absolute;
						top: 0;
						right: 0;
						left: 0;
						bottom: 0;
						font-size: 26rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						color: #D91B00;
						line-height: 56rpx;
						text-align: center;
					}
				}

			}

			.opening_box {
				padding: 30rpx 0;
				background: #F7F9FB;

				.icon {
					display: block;
					width: 34rpx;
					height: 34rpx;
				}
			}
		}


		&_info_wrap {
			position: relative;
			margin-top: -50rpx;
			padding: 40rpx;
			background: #FFFFFF;
			border-radius: 50rpx 50rpx 0 0;

			.desc {
				line-height: 42rpx;
			}

			.label {
				margin: 0 20rpx 20rpx 0;
				padding: 0 10rpx;
				height: 48rpx;
				background: rgba(255, 171, 41, 0.1);
				border-radius: 8rpx;
				font-size: 28rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #EB9003;
				line-height: 48rpx;
			}
		}

		&_swiper {
			position: relative;

			.swiper {
				width: 750rpx;
				height: 860rpx;

				.nav {
					position: relative;
					width: 750rpx;
					height: 860rpx;
				}

				.img {
					display: block;
					width: 750rpx;
					height: 860rpx;
				}
			}

			.swiper_dots {
				position: absolute;
				bottom: 80rpx;
				left: 0;
				right: 0;

				.dots {
					margin: 0 4rpx;
					width: 14rpx;
					height: 4rpx;
					background: #D8D8D8;
				}

				.dots.active {
					background: var(--normal);
				}
			}
		}

	}
</style>