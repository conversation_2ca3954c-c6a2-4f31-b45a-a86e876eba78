<template>
	<view class="xilu">
		<view class="container">
				<image class="m-backdrop" src="../../static/icon/icon_bg2.png" mode="widthFix"></image>
			<view class="m-header">
					<image class="m-backdrop" src="../../static/icon/icon_bg2.png" mode="widthFix"></image>
				<view class="g-custom-nav flex-box flex-center plr30"
					:style="{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }">
					<view class="fs26">个人中心</view>
				</view>
			</view>

			<view class="page-body pr" :style="{ paddingTop: 'calc(90rpx + ' + statusBarHeight + 'px)' }">
				<view class="flex-box xilu_user">
					<view class="flex-1">
						<view class="fs40 mb40 col-10" @click="jump('/pages/basic_information/basic_information',1)">{{userinfo.nickname || '请登录'}}</view>
						<view class="flex-box tc">
							<view class="pr40 mr30" @click="jump('/pages/coupon/coupon',1)">
								<view class="num">{{userinfo.coupon_count || 0}}</view>
								<view class="fs28 col-89">优惠券</view>
							</view>
							<view class="pr40 mr30" @click="jump('/pages/my_collection/my_collection',1)">
								<view class="num">{{userinfo.collection_count || 0}}</view>
								<view class="fs28 col-89">收藏</view>
							</view>
							<view class="pr40 mr30" @click="jump('/pages/my_message/my_message')">
								<view class="num" :class="{dot:messageCount>0}">{{messageCount}}</view>
								<view class="fs28 col-89">消息</view>
							</view>
						</view>
					</view>
					<view class="head" @click="jump('/pages/basic_information/basic_information',1)">
						<image :src="userinfo.avatar" mode="aspectFill" class="img_head"></image>
						<image src="../../static/icon/icon_edit.png" mode="aspectFit" class="icon_edit"></image>
					</view>
				</view>

				<view class="flex-box flex-between">
					<view class="xilu_menu" @click="jump('/pages/landscape_order/landscape_order',1)" style="background-color: #08CDC1;">
						<view class="title">景点订单</view>
						<view class="flex-box">
							<view class="flex-1">
								<text class="fs40 col-f mr10">{{userinfo.scenery_order_count}}</text>
								<text class="fs24" style="color: #B8FFFB;">个订单</text>
							</view>
							<view class="view">查看</view>
						</view>
					</view>

					<view class="xilu_menu" @click="jump('/pages/travel_order/travel_order',1)" style="background-color: #FFAB29;">
						<view class="title">旅游订单</view>
						<view class="flex-box">
							<view class="flex-1">
								<text class="fs40 col-f mr10">{{userinfo.tour_order_count}}</text>
								<text class="fs24" style="color: #FFEDD0;">个订单</text>
							</view>
							<view class="view">查看</view>
						</view>
					</view>
				</view>

				<view class="xilu_nav_box">
					<view class="title">我的服务</view>
					<view class="flex-box flex-wrap">
						<view class="item" @click="jump('/pages/my_commission/my_commission',1)">
							<image src="/static/icon/icon_nav1.png" mode="aspectFit"></image>
							<view>我的佣金</view>
						</view>
						<view class="item" @click="jump('/pages/traveler_list/traveler_list',1)">
							<image src="/static/icon/icon_nav10.png" mode="aspectFit"></image>
							<view>出行人</view>
						</view>
						<view class="item"  @click="jump('/pages/invite_friends/invite_friends',1)">
							<image src="/static/icon/icon_nav2.png" mode="aspectFit"></image>
							<view>邀请好友</view>
						</view>
						<view class="item" @click="jump('/pages/problem/problem',1)">
							<image src="/static/icon/icon_nav3.png" mode="aspectFit"></image>
							<view>常见问题</view>
						</view>
						<view class="item" @click="jump('/pages/singlepage/singlepage?type=privacy_agreement',0)">
							<image src="/static/icon/icon_nav4.png" mode="aspectFit"></image>
							<view>隐私协议</view>
						</view>
						<button class="item" hover-class="none" open-type="contact">
							<image src="/static/icon/icon_nav5.png" mode="aspectFit"></image>
							<view>我的客服</view>
						</button>
						<view class="item" @click="layout()">
							<image src="/static/icon/icon_nav6.png" mode="aspectFit"></image>
							<view>退出登录</view>
						</view>
					</view>
				</view>

				<view class="xilu_nav_box" v-if="userinfo.verifier_status">
					<view class="title">核销功能</view>
					<view class="flex-box flex-wrap">
						<view class="item" @click="bindScan()">
							<image src="/static/icon/icon_nav7.png" mode="aspectFit"></image>
							<view>扫码核销</view>
						</view>
						<view class="item" @click="jump('/pages/write_off_order/write_off_order',1)">
							<image src="/static/icon/icon_nav8.png" mode="aspectFit"></image>
							<view>核销记录</view>
						</view>
						<view class="item" @click="jump('/pages/store_information/store_information',1)">
							<image src="/static/icon/icon_nav9.png" mode="aspectFit"></image>
							<view>门店信息</view>
						</view>
					</view>
				</view>

			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				statusBarHeight: 20,
				messageCount:0,
				userinfo:{
					coupon_count:0,
					collection_count:0,
					user_message: 0,
					nickname:'',
					avatar:'',
				}
			};
		},
		onLoad() {
			let page = this;
			this.statusBarHeight = getApp().globalData.statusBarHeight;
			if(this.$core.getUserinfo()) {
				this.getUserinfo();
			}
			uni.$on("user_update",function(){
				page.getUserinfo();
			})
			uni.$on(app.globalData.Event.loginOut,function(){
				page.userinfo = {nickname:'',avatar:''};
			})
		},
		onShow() {
			if(this.$core.getUserinfo()){
				this.$util.getMessageCount(false).then(count=>{
					this.messageCount = count
				})
			}
		},
		onPullDownRefresh() {
			this.getUserinfo();
			uni.stopPullDownRefresh();
		},
		onUnload() {
			uni.$off("user_update");
			uni.$off(app.globalData.Event.loginOut)
		},
		methods:{
			getUserinfo(){
				this.$core.post({url: 'xilutour.user/info',data: {},loading: false,success: ret => {
						this.userinfo = ret.data;
					},fail: err => {
						console.log(err);
					}
				});
			},
			jump(url,needLogin){
				if(needLogin){
					if(!this.$core.getUserinfo(true)){
						return true;
					}
				}
				uni.navigateTo({
					url: url
				})
			},
			bindScan(){
				if(!this.$core.getUserinfo(true)){
					return true;
				}
				uni.scanCode({
					success: function (res) {
						console.log('条码类型：', res);
						uni.navigateTo({
							url: '/'+res.path
						})
					}
				});

			},
			layout(){
				this.$core.logout();
				uni.showToast({
					title:"退出登录成功",
					icon: 'none'
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		&_user {
			margin: 0 0 46rpx;

			.num {
				position: relative;
				margin: 0 0 15rpx;
				font-size: 46rpx;
				font-family: Poppins, Poppins;
				font-weight: 800;
				color: #101010;
				line-height: 48rpx;
			}

			.num.dot::after {
				position: absolute;
				right: 0rpx;
				top: 0rpx;
				content: '';
				width: 10rpx;
				height: 10rpx;
				background: #F35F4B;
				border-radius: 50%;
			}

			.head {
				position: relative;
				width: 208rpx;
				height: 208rpx;
				border: 6rpx solid #FFFFFF;
				border-radius: 50%;

				.img_head {
					display: block;
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}

				.icon_edit {
					position: absolute;
					right: 20rpx;
					bottom: 0;
					display: block;
					width: 45rpx;
					height: 45rpx;
				}
			}
		}

		&_menu {
			padding: 30rpx;
			width: 320rpx;
			border-radius: 30rpx;

			.title {
				margin: 0 0 24rpx;
				font-size: 34rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 36rpx;
			}

			.view {
				width: 78rpx;
				height: 46rpx;
				background: #FFFFFF;
				border-radius: 16rpx;
				font-size: 24rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 46rpx;
				text-align: center;
			}
		}

		&_nav_box {
			margin: 30rpx 0 0;
			padding: 30rpx 0 40rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
			border-radius: 30rpx;

			.title {
				padding: 0 26rpx;
				font-size: 36rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #101010;
				line-height: 38rpx;
			}

			.item {
				padding: 0;
				margin: 40rpx 0 0;
				width: 25%;
				font-size: 28rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #555555;
				line-height: 30rpx;
				text-align: center;
				background-color: #fff;

				image {
					margin: 0 auto 20rpx;
					display: block;
					width: 60rpx;
					height: 60rpx;
				}
			}

			.item::after {
				content: none;
			}
		}

		.container {
			background: #F7F9FB;
		}

		.page-body {
			padding: 40rpx 40rpx 30rpx;
		}
	}
</style>