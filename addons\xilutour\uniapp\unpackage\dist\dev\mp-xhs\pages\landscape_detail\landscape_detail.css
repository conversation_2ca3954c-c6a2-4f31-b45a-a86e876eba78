.g_order_foot1.data-v-740743b4 {
  margin: 0 auto 40rpx;
}
.xilu_detail_box.data-v-740743b4 {
  padding: 30rpx;
  background: #F7F9FB;
}
.xilu_detail_box .img_detail.data-v-740743b4 {
  margin: 0 0 20rpx;
  display: block;
  width: 100%;
}
.xilu_detail_box .step_wrap .step.data-v-740743b4 {
  position: relative;
  margin: 0 0 30rpx 50rpx;
  padding: 30rpx 30rpx 40rpx;
  width: 640rpx;
  background: #FFFFFF;
  box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
  border-radius: 30rpx;
}
.xilu_detail_box .step_wrap .step .img_step.data-v-740743b4 {
  display: block;
  max-width: 100%;
  margin: 30rpx 0 0;
}
.xilu_detail_box .step_wrap .step.data-v-740743b4::before {
  position: absolute;
  top: 0;
  left: -40rpx;
  content: '';
  width: 2rpx;
  height: calc(100% + 30rpx);
  background-color: rgba(255, 171, 41, 0.2);
}
.xilu_detail_box .step_wrap .step.data-v-740743b4::after {
  position: absolute;
  top: 38rpx;
  left: -50rpx;
  content: '';
  width: 22rpx;
  height: 22rpx;
  background: #FFAB29;
  border-radius: 50%;
}
.xilu_detail_box .detail.data-v-740743b4 {
  padding: 30rpx;
  background: #FFFFFF;
  box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
  border-radius: 30rpx;
  line-height: 44rpx;
}
.xilu_detail_box .project.data-v-740743b4 {
  padding: 30rpx;
  margin: 0 0 30rpx;
  background: #FFFFFF;
  box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
  border-radius: 20rpx;
}
.xilu_detail_box .project image.data-v-740743b4 {
  margin-right: 30rpx;
  display: block;
  width: 200rpx;
  height: 200rpx;
  border-radius: 25rpx;
}
.xilu_detail_box .project .label_wrap.data-v-740743b4 {
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  font-size: 0;
}
.xilu_detail_box .project .label_wrap .label.data-v-740743b4 {
  margin: 0 20rpx 0 0;
  padding: 0 10rpx;
  display: inline-block;
  height: 48rpx;
  background: rgba(255, 171, 41, 0.1);
  border-radius: 8rpx;
  font-size: 28rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #EB9003;
  line-height: 48rpx;
}
.xilu_detail_box .project .btn.data-v-740743b4 {
  width: 100rpx;
  height: 60rpx;
  background: var(--normal);
  border-radius: 20rpx;
  font-size: 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 60rpx;
  text-align: center;
}
.xilu_regis.data-v-740743b4 {
  padding: 0 40rpx;
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
}
.xilu_regis .item.data-v-740743b4 {
  position: relative;
  margin: 0 30rpx 0 0;
  padding: 20rpx 0 0;
  display: inline-block;
  width: 270rpx;
  height: 312rpx;
  background: #FFFFFF;
  border-radius: 25rpx;
  border: 1px solid #EEEEEE;
}
.xilu_regis .item .line.data-v-740743b4 {
  width: 12rpx;
  height: 2rpx;
  background-color: var(--normal);
}
.xilu_regis .item .btn.data-v-740743b4 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 270rpx;
  height: 60rpx;
  background-color: var(--normal);
  border-radius: 2rpx 2rpx 25rpx 25rpx;
  font-size: 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 60rpx;
  text-align: center;
}
.xilu_coupon_box.data-v-740743b4 {
  margin: 0 30rpx 15rpx;
  padding: 0 30rpx;
  width: 690rpx;
  background: #F7F9FB;
  border-radius: 20rpx;
}
.xilu_coupon_box .coupon_list.data-v-740743b4 {
  flex: 1;
  overflow-x: scroll;
  white-space: nowrap;
  overflow-y: hidden;
  height: 56rpx;
}
.xilu_coupon_box .coupon_list .coupon.data-v-740743b4 {
  position: relative;
  margin-right: 20rpx;
  display: inline-block;
}
.xilu_coupon_box .coupon_list .coupon image.data-v-740743b4 {
  display: block;
  width: 180rpx;
  height: 56rpx;
}
.xilu_coupon_box .coupon_list .coupon .inner.data-v-740743b4 {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  font-size: 26rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #D91B00;
  line-height: 56rpx;
  text-align: center;
}
.xilu_coupon_box .opening_box.data-v-740743b4 {
  padding: 30rpx 0;
  background: #F7F9FB;
}
.xilu_coupon_box .opening_box .icon.data-v-740743b4 {
  display: block;
  width: 34rpx;
  height: 34rpx;
}
.xilu_info_wrap.data-v-740743b4 {
  position: relative;
  margin-top: -50rpx;
  padding: 40rpx;
  background: #FFFFFF;
  border-radius: 50rpx 50rpx 0 0;
}
.xilu_info_wrap .desc.data-v-740743b4 {
  line-height: 42rpx;
}
.xilu_info_wrap .label.data-v-740743b4 {
  margin: 0 20rpx 20rpx 0;
  padding: 0 10rpx;
  height: 48rpx;
  background: rgba(255, 171, 41, 0.1);
  border-radius: 8rpx;
  font-size: 28rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #EB9003;
  line-height: 48rpx;
}
.xilu_swiper.data-v-740743b4 {
  position: relative;
}
.xilu_swiper .swiper.data-v-740743b4 {
  width: 750rpx;
  height: 860rpx;
}
.xilu_swiper .swiper .nav.data-v-740743b4 {
  position: relative;
  width: 750rpx;
  height: 860rpx;
}
.xilu_swiper .swiper .img.data-v-740743b4 {
  display: block;
  width: 750rpx;
  height: 860rpx;
}
.xilu_swiper .swiper_dots.data-v-740743b4 {
  position: absolute;
  bottom: 80rpx;
  left: 0;
  right: 0;
}
.xilu_swiper .swiper_dots .dots.data-v-740743b4 {
  margin: 0 4rpx;
  width: 14rpx;
  height: 4rpx;
  background: #D8D8D8;
}
.xilu_swiper .swiper_dots .dots.active.data-v-740743b4 {
  background: var(--normal);
}

