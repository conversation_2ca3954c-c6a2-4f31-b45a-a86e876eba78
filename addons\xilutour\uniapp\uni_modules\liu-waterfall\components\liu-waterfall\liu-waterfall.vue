<template>
	<view class="waterfall" id="waterfall" :style="'background-color:'+bgColor">
		<!-- :style="{ width: columnWidth + 'px' }" -->
		<view v-for="(item, index) in columnData" :key="index" class="column">
			<view v-for="(childItem, childIndex) in item" :key="childIndex" style="width: 100%" :id="'item' + childItem.id"
				>
				<navigator class="item"
					:style="'background-color:'+cardBgColor+';margin:'+margin+'rpx;border-radius:'+radius+'rpx;'" hover-class="none" :url="'/pages/article_detail/article_detail?id='+childItem.id">
					<image :src="childItem.thumb_image_text" mode="widthFix" lazy-load
						:style="{height:childItem.height,width: '100%'}">
					</image>
					<view class="title-info">
						<view class="item-title">{{ childItem.name }}</view>
						<!-- <view class="item-desc">{{ childItem.desc }}</view> -->
					</view>
				</navigator>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			//数据源
			dataList: {
				type: Array,
				required: true,
				default: []
			},
			//显示列数
			column: {
				type: Number,
				required: true,
				default: 2
			},
			//卡片margin(rpx)
			margin: {
				type: Number,
				default: 10
			},
			//卡片圆角(rpx)
			radius: {
				type: Number,
				default: 8
			},
			//页面背景颜色
			bgColor: {
				type: String,
				default: 'unset'
			},
			//卡片背景颜色
			cardBgColor: {
				type: String,
				default: '#FFFFFF'
			},
		},
		data() {
			return {
				columnData: [],
				columnWidth: 0,
				loading: false,
				loginShow: false, //登录弹框
				h5LoginShow: false,
				islogin: false,
				posting:false
			};
		},
		watch: {
			dataList: {
				immediate: true,
				deep: true,
				handler(newValue, oldValue) {
					this.$nextTick(() => {
						this.setColumnWidth()
						this.setData()
					})
				},
			},
			column: {
				immediate: false,
				deep: true,
				handler(newValue) {
					this.$nextTick(() => {
						this.setColumnWidth()
						this.setData()
					})
				},
			},
		},
		methods: {
			//计算每列的高度
			getElemHeight(index) {
				this.$nextTick(() => {
					var arr = [];
					this.dataList.map((item, index) => {
						uni.getImageInfo({
							src: item.picUrl,
							success: (e) => {
								item.height = (e.height * (this.columnWidth / e.width)) + 'px'
								this.createSelectorQuery().select('#item' + item.id)
									.boundingClientRect(res => {
										arr.push({
											...{
												itemHeight: res.height
											},
											...item
										});
										if (arr.length == this.dataList.length) {
											this.columnData = this.distributeToNArrays(arr,
												this.column);
										}
									}).exec();
							}
						})
					})
				})
			},
			distributeToNArrays(arr, n) {
				let sums = new Array(n).fill(0);
				return arr.reduce(
					(arrays, item) => {
						let minSum = Math.min(...sums);
						let minIndex = sums.indexOf(minSum);
						arrays[minIndex].push(item);
						sums[minIndex] += item.itemHeight;
						return arrays;
					},
					new Array(n).fill().map(() => []),
				)
			},
			setColumnWidth() {
				// let width = uni.getSystemInfoSync().windowWidth
				let width;
				this.createSelectorQuery().select('#waterfall')
					.boundingClientRect(res => {
						width = res.width
						this.columnWidth = Math.floor(width / this.column)
					}).exec();

			},
			setData() {
				const resultArray = this.dataList.reduce(
					(acc, cur, index) => {
						const targetIndex = index % this.column;
						acc[targetIndex].push(cur);
						return acc;
					},
					Array.from(Array(this.column), () => []),
				);
				this.columnData = resultArray;
				this.getElemHeight()
			},
			click(index) {
				this.$emit('click', index)
			}
		},
	};
</script>

<style scoped>
	.waterfall {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.waterfall .column {
		width: 335rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.waterfall .column + .column{
		margin-left: 20rpx;
	}

	.waterfall .item {
		margin-bottom: 20rpx!important;
		overflow: hidden;
	}

	.waterfall .title-info {
		padding: 0rpx 20rpx 20rpx 20rpx;
	}

	.waterfall .item-title {
		font-size: 32rpx;
		color: #333333;
		line-height: 46rpx;
		text-align: justify;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		font-weight: bold;
	}

	.waterfall .item-desc {
		margin-top: 4rpx;
		font-size: 26rpx;
		color: #666666;
		line-height: 34rpx;
		text-align: justify;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
	}
</style>