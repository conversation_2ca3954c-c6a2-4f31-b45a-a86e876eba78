
.waterfall.data-v-64a760e9 {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
}
.waterfall .column.data-v-64a760e9 {
	width: 335rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.waterfall .column + .column.data-v-64a760e9{
	margin-left: 20rpx;
}
.waterfall .item.data-v-64a760e9 {
	margin-bottom: 20rpx!important;
	overflow: hidden;
}
.waterfall .title-info.data-v-64a760e9 {
	padding: 0rpx 20rpx 20rpx 20rpx;
}
.waterfall .item-title.data-v-64a760e9 {
	font-size: 32rpx;
	color: #333333;
	line-height: 46rpx;
	text-align: justify;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	font-weight: bold;
}
.waterfall .item-desc.data-v-64a760e9 {
	margin-top: 4rpx;
	font-size: 26rpx;
	color: #666666;
	line-height: 34rpx;
	text-align: justify;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
}

