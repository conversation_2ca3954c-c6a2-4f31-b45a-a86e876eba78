<?php
/**
 * 小红书新版登录功能测试脚本
 * 
 * 使用方法：
 * php scripts/test_xhs_login_new.php
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 获取项目根目录
$rootPath = dirname(__DIR__);

// 引入框架
define('APP_PATH', $rootPath . '/application/');
define('ROOT_PATH', $rootPath . '/');
define('EXTEND_PATH', $rootPath . '/extend/');
define('VENDOR_PATH', $rootPath . '/vendor/');
define('RUNTIME_PATH', $rootPath . '/runtime/');
define('LOG_PATH', RUNTIME_PATH . 'log/');
define('CACHE_PATH', RUNTIME_PATH . 'cache/');
define('TEMP_PATH', RUNTIME_PATH . 'temp/');
define('CONF_PATH', $rootPath . '/config/');

require_once $rootPath . '/thinkphp/base.php';

echo "=== 小红书新版登录功能测试 ===\n";

try {
    // 初始化应用
    \think\App::initCommon();
    
    echo "1. 检查小红书配置...\n";
    $xhs_config = \app\common\model\xilutour\Config::getMyConfig('xhs');
    
    if (empty($xhs_config['xhs_appid']) || empty($xhs_config['xhs_appsecret'])) {
        echo "❌ 小红书配置不完整，请先在管理后台配置\n";
        exit(1);
    }
    
    echo "   ✅ 配置检查通过\n";
    echo "   AppID: " . $xhs_config['xhs_appid'] . "\n";
    
    echo "\n2. 测试小红书控制器方法...\n";
    
    // 创建小红书控制器实例
    $xhs = new \addons\xilutour\controller\XiaohongshuMini();
    
    // 测试processUserProfile方法
    $testUserInfo = [
        'nickName' => '测试用户',
        'avatarUrl' => 'https://test.com/avatar.jpg',
        'gender' => 1
    ];
    $testOpenid = 'test_openid_12345678';
    
    echo "   测试processUserProfile方法...\n";
    $processedInfo = $xhs->processUserProfile($testUserInfo, $testOpenid);
    
    echo "   输入用户信息: " . json_encode($testUserInfo, JSON_UNESCAPED_UNICODE) . "\n";
    echo "   处理后信息: " . json_encode($processedInfo, JSON_UNESCAPED_UNICODE) . "\n";
    
    if ($processedInfo['openid'] === $testOpenid && 
        $processedInfo['nickname'] === $testUserInfo['nickName'] &&
        $processedInfo['avatar'] === $testUserInfo['avatarUrl']) {
        echo "   ✅ processUserProfile方法测试通过\n";
    } else {
        echo "   ❌ processUserProfile方法测试失败\n";
    }
    
    echo "\n3. 测试用户名生成逻辑...\n";
    
    $testOpenids = [
        'xhs_openid_12345678',
        'another_test_openid_87654321',
        'short_id'
    ];
    
    foreach ($testOpenids as $openid) {
        $username = 'xhs_' . substr($openid, -8);
        echo "   OpenID: {$openid} -> 用户名: {$username}\n";
    }
    
    echo "\n4. 模拟API请求测试...\n";
    
    // 模拟请求数据
    $mockRequestData = [
        'code' => 'mock_code_for_test',
        'userInfo' => [
            'nickName' => '小红书测试用户',
            'avatarUrl' => 'https://example.com/avatar.png',
            'gender' => 0
        ],
        'platform' => 'xhs',
        'puser_id' => 0
    ];
    
    echo "   模拟请求数据:\n";
    echo "   " . json_encode($mockRequestData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    
    echo "\n5. 检查数据库表结构...\n";
    
    // 检查用户表是否有source字段
    try {
        $db = \think\Db::connect();
        $userTableInfo = $db->query("SHOW COLUMNS FROM fa_user LIKE 'source'");
        if ($userTableInfo) {
            echo "   ✅ 用户表source字段存在\n";
        } else {
            echo "   ❌ 用户表source字段不存在，请执行数据库迁移\n";
        }
        
        // 检查第三方登录表
        $thirdTableInfo = $db->query("SHOW TABLES LIKE 'fa_xilutour_third'");
        if ($thirdTableInfo) {
            echo "   ✅ 第三方登录表存在\n";
        } else {
            echo "   ❌ 第三方登录表不存在\n";
        }
        
    } catch (Exception $e) {
        echo "   ⚠️  数据库检查失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n6. 生成测试用例...\n";
    
    $testCases = [
        [
            'name' => '正常用户信息',
            'data' => [
                'code' => 'test_code_001',
                'userInfo' => [
                    'nickName' => '张三',
                    'avatarUrl' => 'https://example.com/zhang.jpg',
                    'gender' => 1
                ],
                'platform' => 'xhs'
            ]
        ],
        [
            'name' => '空昵称处理',
            'data' => [
                'code' => 'test_code_002',
                'userInfo' => [
                    'nickName' => '',
                    'avatarUrl' => 'https://example.com/empty.jpg',
                    'gender' => 0
                ],
                'platform' => 'xhs'
            ]
        ],
        [
            'name' => '缺少头像',
            'data' => [
                'code' => 'test_code_003',
                'userInfo' => [
                    'nickName' => '李四',
                    'gender' => 2
                ],
                'platform' => 'xhs'
            ]
        ]
    ];
    
    foreach ($testCases as $index => $testCase) {
        echo "   测试用例 " . ($index + 1) . ": " . $testCase['name'] . "\n";
        echo "   curl -X POST 'http://your-domain/api/xilutour.user/xhs_login_with_profile' \\\n";
        echo "        -H 'Content-Type: application/json' \\\n";
        echo "        -d '" . json_encode($testCase['data']) . "'\n\n";
    }
    
    echo "7. 功能对比说明...\n";
    echo "   旧版本流程:\n";
    echo "   1. 调用xhslogin获取openid\n";
    echo "   2. 调用xhs_get_mobile获取手机号（小红书不支持）\n";
    echo "   3. 创建用户账号\n";
    echo "\n";
    echo "   新版本流程:\n";
    echo "   1. 前端调用xhs.getUserProfile获取用户信息\n";
    echo "   2. 调用xhs_login_with_profile直接完成登录\n";
    echo "   3. 无需手机号，使用openid生成用户名\n";
    echo "\n";
    
    echo "8. 注意事项...\n";
    echo "   - 新版本不需要手机号，用户体验更好\n";
    echo "   - 用户名使用openid生成，格式: xhs_xxxxxxxx\n";
    echo "   - 保留旧接口以确保兼容性\n";
    echo "   - 建议在小红书开发者工具中测试完整流程\n";
    
} catch (Exception $e) {
    echo "❌ 测试脚本执行失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== 测试完成 ===\n";
echo "\n📋 后续步骤:\n";
echo "1. 在小红书开发者工具中测试登录流程\n";
echo "2. 使用上面的curl命令测试API接口\n";
echo "3. 检查用户表和第三方登录表的数据\n";
echo "4. 验证用户来源字段是否正确设置为'xhs'\n";

echo "\n📖 相关文档:\n";
echo "- 详细说明: docs/xiaohongshu_login_without_phone.md\n";
echo "- API调试: docs/xiaohongshu_api_debug.md\n";
echo "- 缓存测试: docs/xiaohongshu_cache_test.md\n";

echo "\n🎉 小红书新版登录功能测试脚本执行完成！\n";
?>
