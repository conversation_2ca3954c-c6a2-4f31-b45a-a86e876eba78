{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/my_collection/my_collection.vue?2a58", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/my_collection/my_collection.vue?125b", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/my_collection/my_collection.vue?166f", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/my_collection/my_collection.vue?223f", "uni-app:///pages/my_collection/my_collection.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/my_collection/my_collection.vue?5a2b", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/my_collection/my_collection.vue?b9fc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "sceneryList", "data", "tabIdx", "sceneryListMore", "page", "tourList", "tourListMore", "onLoad", "onReachBottom", "methods", "tabClick", "refresh", "fetchScenery", "pagesize", "fetchTour"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCgC11B;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAF;MACAG;QAAAC;MAAA;MAEAC;MACAC;QAAAF;MAAA;IACA;EACA;EACAG;IACA;IACA;EACA;EACAC;IACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;UAAAP;QAAA;QACA;MACA;QACA;QACA;UAAAA;QAAA;QACA;MACA;IAEA;IACAQ;MACA;QAAAC;MAAA,8DAEA;IACA;IACAC;MACA;QAAAD;MAAA,wDAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACtFA;AAAA;AAAA;AAAA;AAAyhD,CAAgB,05CAAG,EAAC,C;;;;;;;;;;;ACA7iD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my_collection/my_collection.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my_collection/my_collection.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my_collection.vue?vue&type=template&id=594b592a&scoped=true&\"\nvar renderjs\nimport script from \"./my_collection.vue?vue&type=script&lang=js&\"\nexport * from \"./my_collection.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my_collection.vue?vue&type=style&index=0&id=594b592a&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"594b592a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my_collection/my_collection.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my_collection.vue?vue&type=template&id=594b592a&scoped=true&\"", "var components\ntry {\n  components = {\n    sceneryList: function () {\n      return import(\n        /* webpackChunkName: \"components/scenery-list/scenery-list\" */ \"@/components/scenery-list/scenery-list.vue\"\n      )\n    },\n    tourList: function () {\n      return import(\n        /* webpackChunkName: \"components/tour-list/tour-list\" */ \"@/components/tour-list/tour-list.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my_collection.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my_collection.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"page-head bg-white\">\r\n\t\t\t<view class=\"g_tab\">\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 1}\" @click=\"tabClick(1)\">景点</view>\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 2}\" @click=\"tabClick(2)\">路线</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"g_landscape_list\" v-if=\"tabIdx == 1\">\r\n\t\t\t\t<scenery-list :sceneryList=\"sceneryList\"></scenery-list>\n\t\t\t\t<view class=\"g-btn3-wrap\">\n\t\t\t\t\t<view class=\"g-btn3\" @click=\"fetchScenery\">{{sceneryListMore.text}}</view>\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"g_travel_list\" v-if=\"tabIdx == 2\">\r\n\t\t\t\t<tour-list :tourList=\"tourList\"></tour-list>\n\t\t\t\t\n\t\t\t\t<view class=\"g-btn3-wrap\">\n\t\t\t\t\t<view class=\"g-btn3\" @click=\"fetch\">{{tourListMore.text}}</view>\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\n\timport sceneryList from '@/components/scenery-list/scenery-list.vue';\r\n\texport default {\n\t\tcomponents: {\n\t\t\tsceneryList\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttabIdx: 1,\n\t\t\t\tsceneryList:[],\n\t\t\t\tsceneryListMore:{page:1},\n\t\t\t\t\n\t\t\t\ttourList:[],\n\t\t\t\ttourListMore:{page:1}\r\n\t\t\t};\r\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.fetchScenery();\n\t\t\tthis.fetchTour();\n\t\t},\n\t\tonReachBottom() {\n\t\t\tif(this.tabIdx == 1){\n\t\t\t\tthis.fetchScenery();\n\t\t\t}else{\n\t\t\t\tthis.fetchTour();\n\t\t\t}\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttabClick(i) {\r\n\t\t\t\tthis.tabIdx = i;\n\t\t\t\t// this.refresh();\r\n\t\t\t},\n\t\t\t\n\t\t\trefresh(){\n\t\t\t\tif(this.tabIdx == 1){\n\t\t\t\t\tthis.sceneryList = [];\n\t\t\t\t\tthis.sceneryListMore = {page:1};\n\t\t\t\t\tthis.fetchScenery();\n\t\t\t\t}else{\n\t\t\t\t\tthis.tourList = [];\n\t\t\t\t\tthis.tourListMore = {page:1};\n\t\t\t\t\tthis.fetchTour();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t},\n\t\t\tfetchScenery(){\n\t\t\t\tthis.$util.fetch(this, 'xilutour.scenery/collection_list', {pagesize:10}, 'sceneryListMore', 'sceneryList', 'data', data=>{\n\t\t\t\t  \n\t\t\t\t})\n\t\t\t},\n\t\t\tfetchTour(){\n\t\t\t\tthis.$util.fetch(this, 'xilutour.tour/collection_list', {pagesize:10}, 'tourListMore', 'tourList', 'data', data=>{\n\t\t\t\t  \n\t\t\t\t})\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\r\n\r\n\t.xilu {\r\n\t\t.container {\r\n\t\t\tpadding: 106rpx 40rpx 40rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my_collection.vue?vue&type=style&index=0&id=594b592a&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my_collection.vue?vue&type=style&index=0&id=594b592a&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341200\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}