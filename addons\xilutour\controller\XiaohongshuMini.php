<?php
namespace addons\xilutour\controller;

use think\Cache;
use think\Controller;
use think\Exception;
use think\exception\ThrowableError;
use fast\Http;
use think\Log;

/**
 * 小红书小程序登录控制器
 */
class XiaohongshuMini extends Controller
{
    protected $appId;
    protected $appSecret;

    public function _initialize()
    {
        parent::_initialize();
        $xhs_config = \app\common\model\xilutour\Config::getMyConfig('xhs');
        $this->appId = $xhs_config['xhs_appid'] ?? '';
        $this->appSecret = $xhs_config['xhs_appsecret'] ?? '';
    }

    public function getAccessToken()
    {
        $xhs_config = \app\common\model\xilutour\Config::getMyConfig('xhs');
        $this->appId = $xhs_config['xhs_appid'] ?? '';
        $this->appSecret = $xhs_config['xhs_appsecret'] ?? '';
        if (empty($this->appId) || empty($this->appSecret)) {
            throw new Exception("请正确配置小红书应用信息");
        }
        $key = 'xhs_access_token_' . $this->appId;
        $redis = Cache::store('redis')->handler();
        if ($accessToken = $redis->get($key)) {
            return $accessToken;
        }

        try {
            $url = "https://miniapp.xiaohongshu.com/api/rmp/token";
            $params = [
                'appid' => $this->appId,
                'secret' => $this->appSecret,
            ];
            $response = Http::post($url, $params);
            $result = json_decode($response, true);
            if (!$result || isset($result['success']) || $result['success'] == false) {
                $error_msg = isset($result['error_description']) ? $result['error_description'] : '获取小红书登录信息失败';
                throw new Exception($error_msg);
            }

            $redis = Cache::store('redis')->handler();
            $redis->set($key, $result['data']['access_token'], $result['data']['expires_in'] - 60);

            return $result['data']['access_token'];
        } catch (\Exception $exception) {
            throw new Exception("小红书登录失败");
        }
    }
    /**
     * 小红书登录
     * @param string $code 小红书登录code
     * @return array
     * @throws Exception
     * @throws ThrowableError
     */
    public function xhsLogin($code)
    {
        if (empty($this->appId) || empty($this->appSecret)) {
            throw new Exception("请正确配置小红书应用信息");
        }

        try {
            // 调用小红书API获取session_key和openid
            $url = "https://miniapp.xiaohongshu.com/api/rmp/session";
            $params = [
                'appid' => $this->appId,
                'access_token' => $this->getAccessToken(),
                'code' => $code,
            ];

            $response = Http::post($url, $params);
            $result = json_decode($response, true);

            if (!$result || isset($result['error'])) {
                $error_msg = isset($result['error_description']) ? $result['error_description'] : '小红书登录失败';
                throw new Exception($error_msg);
            }

            return [
                'openid' => $result['openid'] ?? '',
                'session_key' => $result['session_key'] ?? '',
                'unionid' => $result['unionid'] ?? '',
            ];

        } catch (ThrowableError $e) {
            throw $e;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 获取小红书用户信息
     * @param string $accessToken 访问令牌
     * @param string $openid 用户openid
     * @return array
     * @throws Exception
     */
    public function getUserInfo($accessToken, $openid)
    {
        try {
            $url = "https://open.xiaohongshu.com/api/sns/userinfo";
            $params = [
                'access_token' => $accessToken,
                'openid' => $openid
            ];

            $response = Http::get($url, $params);
            $result = json_decode($response, true);

            if (!$result || isset($result['error'])) {
                $error_msg = isset($result['error_description']) ? $result['error_description'] : '获取用户信息失败';
                throw new Exception($error_msg);
            }

            return [
                'openid' => $result['openid'] ?? '',
                'nickname' => $result['nickname'] ?? '',
                'avatar' => $result['avatar'] ?? '',
                'gender' => $result['gender'] ?? 0,
                'unionid' => $result['unionid'] ?? ''
            ];

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 解密小红书手机号
     * @param string $sessionKey 会话密钥
     * @param string $iv 初始向量
     * @param string $encryptedData 加密数据
     * @return array
     * @throws Exception
     */
    public function decryptPhoneNumber($sessionKey, $iv, $encryptedData)
    {
        try {
            // 小红书手机号解密逻辑
            // 注意：这里需要根据小红书实际的解密方式进行实现
            // 目前使用类似微信的AES解密方式作为示例

            $aesKey = base64_decode($sessionKey);
            $aesIV = base64_decode($iv);
            $aesCipher = base64_decode($encryptedData);

            $result = openssl_decrypt($aesCipher, "AES-128-CBC", $aesKey, 1, $aesIV);

            if (!$result) {
                throw new Exception("手机号解密失败");
            }

            $dataObj = json_decode($result, true);

            if (!$dataObj || !isset($dataObj['phoneNumber'])) {
                throw new Exception("手机号解密数据格式错误");
            }

            return [
                'phoneNumber' => $dataObj['phoneNumber'],
                'purePhoneNumber' => $dataObj['purePhoneNumber'] ?? $dataObj['phoneNumber'],
                'countryCode' => $dataObj['countryCode'] ?? '86'
            ];

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 刷新访问令牌
     * @param string $refreshToken 刷新令牌
     * @return array
     * @throws Exception
     */
    public function refreshToken($refreshToken)
    {
        try {
            $url = "https://open.xiaohongshu.com/api/sns/oauth/refresh_token";
            $params = [
                'appid' => $this->appId,
                'grant_type' => 'refresh_token',
                'refresh_token' => $refreshToken
            ];

            $response = Http::post($url, $params);
            $result = json_decode($response, true);

            if (!$result || isset($result['error'])) {
                $error_msg = isset($result['error_description']) ? $result['error_description'] : '刷新令牌失败';
                throw new Exception($error_msg);
            }

            return [
                'access_token' => $result['access_token'] ?? '',
                'refresh_token' => $result['refresh_token'] ?? '',
                'expires_in' => $result['expires_in'] ?? 7200
            ];

        } catch (Exception $e) {
            throw $e;
        }
    }
}
