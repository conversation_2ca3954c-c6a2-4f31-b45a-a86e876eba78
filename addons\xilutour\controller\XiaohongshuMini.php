<?php
namespace addons\xilutour\controller;

use GuzzleHttp\Client;
use think\Cache;
use think\Controller;
use think\Exception;
use think\exception\ThrowableError;
use fast\Http;
use think\Log;

/**
 * 小红书小程序登录控制器
 */
class XiaohongshuMini extends Controller
{
    // 小红书API基础URL
    const XHS_API_BASE_URL = 'https://miniapp.xiaohongshu.com/';
    const XHS_SANDBOX_URL = 'https://miniapp-sandbox.xiaohongshu.com/';

    protected $appId;
    protected $appSecret;
    protected $isDebug = false; // 是否使用沙箱环境

    public function _initialize()
    {
        parent::_initialize();
        $xhs_config = \app\common\model\xilutour\Config::getMyConfig('xhs');
        $this->appId = $xhs_config['xhs_appid'] ?? '';
        $this->appSecret = $xhs_config['xhs_appsecret'] ?? '';
        $this->isDebug = config('app_debug') || (isset($xhs_config['xhs_debug']) && $xhs_config['xhs_debug']);
    }

    /**
     * 获取API基础URL
     * @return string
     */
    private function getApiBaseUrl()
    {
        return $this->isDebug ? self::XHS_SANDBOX_URL : self::XHS_API_BASE_URL;
    }

    /**
     * 发送HTTP请求到小红书API
     * @param string $endpoint API端点
     * @param array $params 请求参数
     * @param string $method 请求方法
     * @return array
     * @throws Exception
     */
    private function sendRequest($endpoint, $params = [], $method = 'POST')
    {
        $url = $this->getApiBaseUrl() . $endpoint;

        $client = new Client([
            'verify'  => false,
            'timeout' => 3,
            'http_errors' => false,
        ]);
        try {
            if ($method === 'POST') {
                $response = $client->post($url, [
                    'headers' => ['Content-Type' => 'application/json', 'charset' => 'UTF-8'],
                    'json' => $params
                ]);
                Log::info("小红书API响应:" . json_encode([
                    'url' => $url,
                    'params' => json_encode($params),
                    'code' => $response->getStatusCode(),
                    'body' => json_decode($response->getBody(), true, 512, JSON_BIGINT_AS_STRING)
                ], JSON_UNESCAPED_UNICODE));

                $result = $response->getBody();

                // 如果响应不是有效的JSON，记录原始响应
                if (!$result) {
                    throw new Exception("API响应格式错误: " . substr($response, 0, 200));
                }

                return json_decode($result, true);
            } else {
                // GET请求
                $response = $client->get($url . '?' . http_build_query($params));
                Log::info("小红书API(GET)响应:" . json_encode([
                        'url' => $url,
                        'params' => json_encode($params),
                        'code' => $response->getStatusCode(),
                        'body' => json_decode($response->getBody(), true, 512, JSON_BIGINT_AS_STRING)
                    ], JSON_UNESCAPED_UNICODE));
                $result = json_decode($response->getBody(), true);
                return $result ?: [];
            }

        } catch (Exception $e) {
            Log::error("小红书API请求异常: {$url}" . var_export([
                'error' => $e->getMessage(),
                'params' => $params,
                'trace' => $e->getTraceAsString()
            ]));
            throw $e;
        }
    }

    /**
     * 获取access_token（带缓存）
     * @return string
     * @throws Exception
     */
    public function getAccessToken()
    {
        if (empty($this->appId) || empty($this->appSecret)) {
            throw new Exception("请正确配置小红书应用信息");
        }

        // 缓存key
        $cacheKey = 'xhs_access_token' . md5($this->appId);

        try {
            // 先尝试从缓存读取
            $cachedToken = Cache::get($cacheKey);
            if ($cachedToken) {
                Log::info('小红书access_token从缓存获取成功:' . $cachedToken);
                return $cachedToken;
            }

            // 缓存不存在，请求新的access_token
            Log::info('小红书access_token缓存不存在，重新获取');

            // 构建请求参数 - 根据小红书API文档调整
            $params = [
                'appid' => $this->appId,
                'secret' => $this->appSecret
            ];

            // 使用统一的请求方法
            $result = $this->sendRequest('api/rmp/token', $params, 'POST');

            if (!$result || (isset($result['success']) && $result['success'] == false)) {
                $error_msg = isset($result['error_description']) ? $result['error_description'] : '获取小红书access_token失败';
                throw new Exception($error_msg);
            }

            $accessToken = $result['data']['access_token'] ?? '';
            $expiresIn = $result['data']['expires_in'] ?? 7200;

            if (empty($accessToken)) {
                throw new Exception("获取到的access_token为空");
            }

            // 缓存access_token，提前60秒过期以避免边界问题
            $cacheTime = max($expiresIn - 60, 60);
            Cache::set($cacheKey, $accessToken, $cacheTime);

            Log::info('小红书access_token获取并缓存成功，有效期：' . $cacheTime . '秒');
            return $accessToken;

        } catch (Exception $e) {
            Log::error('获取小红书access_token失败：' . $e->getMessage());
            throw $e;
        } catch (\Exception $e) {
            Log::error('获取小红书access_token异常：' . $e->getMessage());
            throw new Exception("获取小红书access_token失败");
        }
    }

    /**
     * 清除access_token缓存
     * @return bool
     */
    public function clearAccessTokenCache()
    {
        $cacheKey = 'xhs_access_token_' . md5($this->appId);
        return Cache::rm($cacheKey);
    }

    /**
     * 获取缓存统计信息
     * @return array
     */
    public function getCacheInfo()
    {
        $cacheKey = 'xhs_access_token_' . md5($this->appId);
        $cachedToken = Cache::get($cacheKey);

        return [
            'cache_key' => $cacheKey,
            'has_cache' => !empty($cachedToken),
            'token_preview' => $cachedToken ? substr($cachedToken, 0, 10) . '...' : '',
            'app_id' => $this->appId
        ];
    }

    /**
     * 测试小红书API连接和配置
     * @return array
     */
    public function testConnection()
    {
        $result = [
            'config_check' => false,
            'network_check' => false,
            'api_check' => false,
            'messages' => []
        ];

        try {
            // 1. 检查配置
            if (empty($this->appId) || empty($this->appSecret)) {
                $result['messages'][] = '❌ 小红书配置不完整';
                return $result;
            }
            $result['config_check'] = true;
            $result['messages'][] = '✅ 配置检查通过';

            // 2. 检查网络连接
            $testUrl = $this->getApiBaseUrl();
            try {
                $response = $this->sendRequest($testUrl, [], 'GET');
                $result['network_check'] = true;
                $result['messages'][] = '✅ 网络连接正常';
            } catch (Exception $e) {
                $result['messages'][] = '❌ 网络连接失败: ' . $e->getMessage();
                return $result;
            }

            // 3. 测试API调用
            try {
                $this->clearAccessTokenCache(); // 清除缓存确保重新请求
                $token = $this->getAccessToken();
                $result['api_check'] = true;
                $result['messages'][] = '✅ API调用成功';
                $result['token_preview'] = substr($token, 0, 10) . '...';
            } catch (Exception $e) {
                $result['messages'][] = '❌ API调用失败: ' . $e->getMessage();
            }

        } catch (Exception $e) {
            $result['messages'][] = '❌ 测试异常: ' . $e->getMessage();
        }

        return $result;
    }
    /**
     * 小红书登录
     * @param string $code 小红书登录code
     * @return array
     * @throws Exception
     * @throws ThrowableError
     */
    public function xhsLogin($code)
    {
        if (empty($this->appId) || empty($this->appSecret)) {
            throw new Exception("请正确配置小红书应用信息");
        }

        try {
            // 获取access_token（带缓存）
            $accessToken = $this->getAccessToken();

            // 调用小红书API获取session_key和openid
            $params = [
                'appid' => $this->appId,
                'access_token' => $accessToken,
                'code' => $code,
            ];

            // 使用统一的请求方法
            $result = $this->sendRequest('api/rmp/session', $params, 'GET');

            if (!$result || (isset($result['success']) && $result['success'] == false)) {
                $error_msg = isset($result['error_description']) ? $result['error_description'] : '小红书登录失败';
                throw new Exception($error_msg);
            }
            return [
                'openid' => $result['data']['openid'] ?? '',
                'session_key' => $result['data']['session_key'] ?? '',
                'unionid' => $result['data']['unionid'] ?? ''
            ];

        } catch (Exception $e) {
            Log::error('小红书登录Exception：' . $e->getMessage() . $e->getFile() . $e->getLine());
            throw $e;
        }
    }

    /**
     * 获取小红书用户信息
     * @param string $openid 用户openid
     * @param string $accessToken 访问令牌（可选，不传则自动获取）
     * @return array
     * @throws Exception
     */
    public function getUserInfo($openid, $accessToken = '')
    {
        try {
            // 如果没有传入accessToken，则自动获取
            if (empty($accessToken)) {
                $accessToken = $this->getAccessToken();
            }

            $url = self::XHS_SANDBOX_URL . "api/sns/userinfo";
            $params = [
                'access_token' => $accessToken,
                'openid' => $openid
            ];

            Log::info('获取小红书用户信息请求参数：', $params);
            $response = Http::get($url, $params);
            $result = json_decode($response, true);
            Log::info('获取小红书用户信息响应：', $result ?: []);

            if (!$result || (isset($result['success']) && $result['success'] == false)) {
                // 如果是access_token过期，清除缓存重试一次
                if (isset($result['error_code']) && in_array($result['error_code'], ['40001', '40014', '42001'])) {
                    Log::info('获取用户信息时access_token可能过期，清除缓存重试');
                    $this->clearAccessTokenCache();
                    $accessToken = $this->getAccessToken();
                    $params['access_token'] = $accessToken;

                    $response = Http::get($url, $params);
                    $result = json_decode($response, true);
                    Log::info('获取小红书用户信息重试响应：', $result ?: []);
                }

                if (!$result || (isset($result['success']) && $result['success'] == false)) {
                    $error_msg = isset($result['error_description']) ? $result['error_description'] : '获取用户信息失败';
                    throw new Exception($error_msg);
                }
            }

            return [
                'openid' => $result['data']['openid'] ?? $openid,
                'nickname' => $result['data']['nickname'] ?? '',
                'avatar' => $result['data']['avatar'] ?? '',
                'gender' => $result['data']['gender'] ?? 0,
                'unionid' => $result['data']['unionid'] ?? ''
            ];

        } catch (Exception $e) {
            Log::error('获取小红书用户信息失败：' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 解密小红书手机号
     * @param string $sessionKey 会话密钥
     * @param string $iv 初始向量
     * @param string $encryptedData 加密数据
     * @return array
     * @throws Exception
     */
    public function decryptPhoneNumber($sessionKey, $iv, $encryptedData)
    {
        try {
            // 小红书手机号解密逻辑
            // 注意：这里需要根据小红书实际的解密方式进行实现
            // 目前使用类似微信的AES解密方式作为示例

            $aesKey = base64_decode($sessionKey);
            $aesIV = base64_decode($iv);
            $aesCipher = base64_decode($encryptedData);

            $result = openssl_decrypt($aesCipher, "AES-128-CBC", $aesKey, 1, $aesIV);

            if (!$result) {
                throw new Exception("手机号解密失败");
            }

            $dataObj = json_decode($result, true);

            if (!$dataObj || !isset($dataObj['phoneNumber'])) {
                throw new Exception("手机号解密数据格式错误");
            }

            return [
                'phoneNumber' => $dataObj['phoneNumber'],
                'purePhoneNumber' => $dataObj['purePhoneNumber'] ?? $dataObj['phoneNumber'],
                'countryCode' => $dataObj['countryCode'] ?? '86'
            ];

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 刷新访问令牌
     * @param string $refreshToken 刷新令牌
     * @return array
     * @throws Exception
     */
    public function refreshToken($refreshToken)
    {
        try {
            $url = "https://open.xiaohongshu.com/api/sns/oauth/refresh_token";
            $params = [
                'appid' => $this->appId,
                'grant_type' => 'refresh_token',
                'refresh_token' => $refreshToken
            ];

            $response = $this->sendRequest($url, $params);

            return [
                'access_token' => $result['access_token'] ?? '',
                'refresh_token' => $result['refresh_token'] ?? '',
                'expires_in' => $result['expires_in'] ?? 7200
            ];

        } catch (Exception $e) {
            throw $e;
        }
    }
}
