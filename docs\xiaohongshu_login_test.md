# 小红书授权登录功能测试文档

## 功能概述
本文档描述了小红书授权登录功能的测试步骤和验证要点。

## 测试环境准备

### 1. 数据库迁移
执行以下SQL脚本来更新数据库结构：

```sql
-- 1. 添加用户来源字段
source application/admin/command/Migration/add_user_source_field.sql

-- 2. 添加小红书配置
source application/admin/command/Migration/add_xhs_config.sql
```

### 2. 小红书小程序配置
1. 登录管理后台
2. 进入 系统管理 -> 旅游配置 -> 平台配置
3. 点击"小红书小程序"配置
4. 填入以下信息：
   - 是否启用：开启
   - 小程序名称：您的小红书小程序名称
   - APPID：小红书小程序的APPID
   - APPSECRET：小红书小程序的APPSECRET

## 测试步骤

### 1. 后端API测试

#### 1.1 测试小红书登录接口
```bash
POST /api/xilutour.user/xhslogin
Content-Type: application/json

{
    "code": "小红书登录返回的code",
    "platform": "xhs"
}
```

预期响应：
```json
{
    "code": 1,
    "msg": "登录成功",
    "data": {
        "userinfo": {...},
        "third": {
            "third_id": 1,
            "binding": 0
        }
    }
}
```

#### 1.2 测试获取手机号接口
```bash
POST /api/xilutour.user/xhs_get_mobile
Content-Type: application/json

{
    "third_id": 1,
    "code": "手机号授权code",
    "puser_id": 0
}
```

预期响应：
```json
{
    "code": 1,
    "msg": "登录成功",
    "data": {
        "userinfo": {
            "id": 1,
            "nickname": "138****1234",
            "mobile": "13812341234",
            "source": "xhs",
            "source_text": "小红书"
        },
        "third": {
            "third_id": 1,
            "binding": 1
        }
    }
}
```

### 2. 管理后台测试

#### 2.1 用户管理界面
1. 登录管理后台
2. 进入 会员管理 -> 会员管理
3. 验证用户列表中显示"用户来源"字段
4. 验证可以按用户来源进行筛选
5. 验证小红书用户显示为"小红书"来源

#### 2.2 配置管理界面
1. 进入 系统管理 -> 旅游配置
2. 验证"平台配置"标签页中有"小红书小程序"配置项
3. 验证配置表单包含所有必要字段

### 3. UniApp前端测试

#### 3.1 小红书小程序环境
1. 在小红书开发者工具中打开项目
2. 确保manifest.json中配置了正确的小红书appid
3. 测试登录页面是否正确显示小红书授权界面

#### 3.2 登录流程测试
1. 打开登录页面
2. 点击"授权登录"按钮
3. 验证是否正确调用小红书登录API
4. 验证是否正确获取用户手机号
5. 验证登录成功后是否正确跳转

## 验证要点

### 1. 数据库验证
- 检查fa_user表是否添加了source字段
- 检查fa_xilutour_third表中是否正确存储小红书登录信息
- 检查fa_xilutour_config表中是否有小红书配置项

### 2. 功能验证
- 小红书用户注册时source字段是否设置为'xhs'
- 管理后台是否能正确显示和筛选不同来源的用户
- 第三方登录表是否正确存储小红书平台信息

### 3. 错误处理验证
- 配置信息错误时是否有正确的错误提示
- 网络请求失败时是否有重试机制
- 用户拒绝授权时是否有合适的处理

## 常见问题

### 1. 小红书API调用失败
- 检查APPID和APPSECRET是否正确
- 检查小红书开放平台配置是否正确
- 检查网络连接是否正常

### 2. 用户来源显示异常
- 检查数据库迁移是否执行成功
- 检查User模型是否正确添加了source相关方法
- 检查前端JS文件是否正确配置了searchList

### 3. 登录流程异常
- 检查UniApp配置是否正确
- 检查小红书小程序权限配置
- 检查API接口是否正确响应

## 性能优化建议

1. 添加Redis缓存来存储小红书access_token
2. 优化数据库查询，为source字段添加索引
3. 添加日志记录来跟踪登录流程
4. 实现token刷新机制来保持登录状态

## 安全注意事项

1. 确保APPSECRET不会暴露在前端代码中
2. 对用户输入进行适当的验证和过滤
3. 实现适当的频率限制来防止恶意请求
4. 定期更新小红书SDK和相关依赖
