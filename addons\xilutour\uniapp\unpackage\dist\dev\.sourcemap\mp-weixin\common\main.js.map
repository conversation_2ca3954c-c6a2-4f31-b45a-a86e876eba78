{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/App.vue?a9b4", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/App.vue?e27c", "uni-app:///App.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/App.vue?0e92", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/App.vue?3353"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "prototype", "$core", "core", "$util", "util", "config", "productionTip", "App", "mpType", "app", "$mount", "onLaunch", "uni", "success", "url", "title", "icon", "setTimeout", "data", "loading", "globalData", "isIos", "platformStatus", "statusBarHeight", "screenHeight", "appid", "apiBase<PERSON>ri", "storageBaseUri", "uploadOssStatus", "al<PERSON>s", "endpoint", "logo", "refund_reasons", "apply_rule", "Event", "CurrentCityChange", "CurrentCityChange2", "loginOut", "defaultCity", "id", "name", "onShow", "options", "expire", "value", "console", "onHide"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAE3D;AAEA;AAOA;AACA;AAAgC;AAAA;AAbhC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAM1D;AACAC,YAAG,CAACC,SAAS,CAACC,KAAK,GAAGC,aAAI,CAAC,CAAC;AAC5BH,YAAG,CAACC,SAAS,CAACG,KAAK,GAAGC,aAAI,CAAC,CAAC;;AAM5B;AACA;;AAEAL,YAAG,CAACM,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIV,YAAG,mBACdQ,YAAG,EACN;AACF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;ACxBZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACyM;AACzM,gBAAgB,gNAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAA8xB,CAAgB,4xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;eCClzB;EACAC;IACA;IACAC;MACAC;QACA;UACAJ;QACA;QACA;UACAA;UACA;QACA;QACAA;QACAA;MACA;IACA;IACAA;MACA;QACA;UACAA;UACAG;YAAAE;UAAA;UACAF;YAAAG;YAAAC;UAAA;QACA;MACA;IACA;IACAC;MACA;MACAR;QAAAK;QAAAI;QAAAC;QAAAN;UACAJ;QACA;MAAA;;MAEA;MACAA;IACA;EAEA;EACAW;IACAC;IACAC;IACAC;IACAC;IACA;IACAC;IACAC;IACA;IACA;IACAC;IAEA;IACAC;IACAC;MAAAC;IAAA;IACAzB;MACA0B;MACAC;MACAC;IACA;IACAC;MACAC;MACAC;MACAC;IACA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;QACA;QACA;QACA;UACA;UACAC;QACA;QACA;UACA;YAAAC;YAAAC;UAAA;UACA;YACAhC;UACA;YACA;YACAiC;UACA;QAEA;MACA;IAEA;EACA;EACAC;IACAD;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7FA;AAAA;AAAA;AAAA;AAAi+C,CAAgB,u5CAAG,EAAC,C;;;;;;;;;;;ACAr/C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App'\r\n\r\nimport core from './xilu/core.js';\r\n\r\nimport util from './xilu/util.js';\r\n\r\n// 挂载\r\nVue.prototype.$core = core; //core\r\nVue.prototype.$util = util; //util\r\n\r\n\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\n\r\n// import uView from '@/uni_modules/uview-ui'\r\n// Vue.use(uView)\r\n\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n  ...App\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\n\texport default {\n\t\tonLaunch: function() {\n\t\t\tlet app = this;\n\t\t\tuni.getSystemInfo({\n\t\t\t\tsuccess: res => {\n\t\t\t\t\tif (res.platform == 'ios' || res.platform == 'mac') {\n\t\t\t\t\t\tapp.globalData.isIos = true;\n\t\t\t\t\t}\n\t\t\t\t\tif (res.statusBarHeight < 20) {\n\t\t\t\t\t\tapp.globalData.statusBarHeight = 20;\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tapp.globalData.statusBarHeight = res.statusBarHeight;\n\t\t\t\t\tapp.globalData.screenHeight = res.screenHeight;\n\t\t\t\t}\n\t\t\t});\n\t\t\tapp.$core.onRequestComplete(function (ret, response) {\n\t\t\t\tif(ret && ret.code===401) {\n\t\t\t\t\tif(app.$core.getUserinfo()) {\n\t\t\t\t\t  app.$core.logout();\n\t\t\t\t\t  uni.navigateTo({url: '/pages/login/login'});\n\t\t\t\t\t  uni.showToast({title: '登录过期,请重新登录',icon:'none'});\n\t\t\t\t\t}\n\t\t\t   }\n\t\t\t});\n\t\t\tsetTimeout(function () {\n\t\t\t\t  // app初始化完成后再执行\n\t\t\t\t  app.$core.get({url:'xilutour.common/init_config',data:{},loading:false,success:(ret)=>{\n\t\t\t\t\tapp.globalData.config = ret.data.config;\n\t\t\t\t  }})\n\t\t\t\t  \n\t\t\t\t  //1.定位\n\t\t\t\t  app.$core.getLocation();\n\t\t\t},1);\n\t\t\t\n\t\t},\n\t\tglobalData: {\n\t\t\tisIos: false,\n\t\t\tplatformStatus: 1,\n\t\t\tstatusBarHeight: 20,\n\t\t\tscreenHeight:0,\n\t\t\t//小程序配置的接口请求域名，为项目部署的服务器路径\n\t\t\tappid: 'wx2e8b2af5ed5b28b4',\n\t\t\tapiBaseUri: \"http://www.travel.com/api\",\n\t\t\t // apiBaseUri: \"https://www.example.com/api\",\n\t\t\t////前端上传图片补全域名，如oss的https://xxxx.oss-cn-shanghai.aliyuncs.com，或https://your.site.com\n\t\t\tstorageBaseUri: \"https://bucket.oss-cn-shanghai.aliyuncs.com\",\n\t\t\n\t\t\t //阿里OSS的上传路径\n\t\t\tuploadOssStatus: false,\n\t\t\talioss: {endpoint:'https://bucket.oss-cn-shanghai.aliyuncs.com'},\n\t\t\tconfig:{\n\t\t\t\tlogo:'',\n\t\t\t\trefund_reasons:[],\n\t\t\t\tapply_rule:''\n\t\t\t},\n\t\t\tEvent: {\n\t\t\t\tCurrentCityChange: \"currentCityChange\",\n\t\t\t\tCurrentCityChange2: \"currentCityChange2\",\n\t\t\t\tloginOut: \"loginOut\",\n\t\t\t},\n\t\t\tdefaultCity:{\n\t\t\t\tid: 104,\n\t\t\t\tname: '上海市'\n\t\t\t},\n\t\t},\n\t\tonShow: function(e) {\n\t\t\tif(e.query){\n\t\t\t\tif(e.query.scene){\n\t\t\t\t\tlet scene = decodeURIComponent(e.query.scene);\n\t\t\t\t\tlet options = {}\n\t\t\t\t\tfor (var i = 0; i < scene.split('&').length;i++){\n\t\t\t\t\t    var arr = scene.split('&')[i].split('=');\n\t\t\t\t\t    options[arr[0]] = arr[1];\n\t\t\t\t\t}\n\t\t\t\t\tif(options.uid != 'undefined' && options.uid > 0){\n\t\t\t\t\t\tlet a = {expire: 0,value: options.uid};\n\t\t\t\t\t\ttry{\n\t\t\t\t\t\t\tuni.setStorageSync('puser_id' + this.globalData.appid, a);\n\t\t\t\t\t\t}catch(e){\n\t\t\t\t\t\t\t//TODO handle the exception\n\t\t\t\t\t\t\tconsole.log(e);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tonHide: function() {\n\t\t\tconsole.log('App Hide')\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/*每个页面公共css */\n\t@import url('/static/css/global.css');\n\t// @import 'uni_modules/uview-ui/index.scss'\n</style>", "import mod from \"-!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494342657\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}