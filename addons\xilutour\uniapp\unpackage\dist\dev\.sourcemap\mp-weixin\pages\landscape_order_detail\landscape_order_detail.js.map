{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_order_detail/landscape_order_detail.vue?9c9a", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_order_detail/landscape_order_detail.vue?2fe7", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_order_detail/landscape_order_detail.vue?46fd", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_order_detail/landscape_order_detail.vue?9cb0", "uni-app:///pages/landscape_order_detail/landscape_order_detail.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_order_detail/landscape_order_detail.vue?32c3", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/landscape_order_detail/landscape_order_detail.vue?1559"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "orderId", "order", "state_text", "order_no", "total_count", "createtime_text", "total_price", "pay_price", "coupon_price", "order_project", "tel", "address", "scenery_name", "thumb_image", "project_name", "onLoad", "methods", "fetchDetail", "url", "order_id", "success", "uni", "title", "codeCopy", "console", "orderCancel", "content", "page", "loading", "payment", "type", "pay_type", "platform", "callphone", "phoneNumber", "bindOpenLocation", "latitude", "longitude", "name", "refund", "icon", "fail", "bindComment", "events", "commentSuccess", "res"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,+BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+I;AAC/I;AAC0E;AACL;AACsC;;;AAG3G;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,4FAAM;AACR,EAAE,6GAAM;AACR,EAAE,sHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iHAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAA+0B,CAAgB,+yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqJn2B;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QAEA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAAC;QAAAnB;UAAAoB;QAAA;QAAAC;UACA;UACAC;YACAC;UACA;QACA;MAAA;IACA;IACAC;MACAF;QACAtB;QACAqB;UACAI;QACA;MACA;IAEA;IACA;IACAC;MACA;MACA;MACAJ;QACAC;QACAI;QACAN;UACA;YACAO;cAAAT;cAAAnB;gBAAAoB;cAAA;cAAAS;cAAAR;gBACAO;cACA;YAAA;UACA;QACA;MACA;IACA;IACAE;MAAA;MACA;MAEA;QAAAX;QAAAnB;UAAA+B;UAAAC;UAAAZ;UAAAa;QAAA;QAAAZ;UACA;UACA,4CAEA;QACA;MAAA;IAEA;IAEA;IACAa;MACA;MACAZ;QACAa;MACA;IACA;IACA;IACAC;MACA;MACA;MACAd;QACAe;QACAC;QACAC;QACA3B;MACA;IACA;IACA;IACA4B;MACA;MACA;MACAlB;QACAC;QACAI;QACAN;UACA;YACAO;cAAAT;cAAAnB;gBAAAoB;cAAA;cAAAC;gBACAC;kBAAAC;kBAAAkB;gBAAA;gBACAb;cACA;cAAAc;gBACApB;kBACAC;kBACAI;gBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAgB;MAAA;MACA;MACArB;QACAH;QACAyB;UACAC;YACA3C;YACA;UACA;QACA;QACAmB;UACAyB;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxRA;AAAA;AAAA;AAAA;AAAkiD,CAAgB,m6CAAG,EAAC,C;;;;;;;;;;;ACAtjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/landscape_order_detail/landscape_order_detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/landscape_order_detail/landscape_order_detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./landscape_order_detail.vue?vue&type=template&id=8fe3bdf4&scoped=true&\"\nvar renderjs\nimport script from \"./landscape_order_detail.vue?vue&type=script&lang=js&\"\nexport * from \"./landscape_order_detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./landscape_order_detail.vue?vue&type=style&index=0&id=8fe3bdf4&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8fe3bdf4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/landscape_order_detail/landscape_order_detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_order_detail.vue?vue&type=template&id=8fe3bdf4&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.order.state == 1 && _vm.order.order_qrcode.length > 0\n  var l0 = g0\n    ? _vm.__map(_vm.order.order_qrcode, function (qrcode, index) {\n        var $orig = _vm.__get_orig(qrcode)\n        var g1 = _vm.order.order_qrcode.length\n        return {\n          $orig: $orig,\n          g1: g1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_order_detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_order_detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"page-foot ptb15\">\r\n\t\t\t<view class=\"g_order_foot1 flex-box\">\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"col-price fs24\"  v-if=\"order.state==0\">\r\n\t\t\t\t\t\t<!-- <view>请在24小时内支付</view>\r\n\t\t\t\t\t\t<view>过期自动取消</view> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"order.state==0\" class=\"btn2\" @click=\"orderCancel()\" >取消订单</view>\r\n\t\t\t\t<view v-if=\"order.state==0\" class=\"btn3\" @click=\"payment()\">立即支付</view>\r\n\t\t\t\t<button v-if=\"order.state==1\" class=\"btn2\" open-type=\"contact\">联系客服</button>\r\n\t\t\t\t<view class=\"btn3\" v-if=\"order.state==2 && order.comment_status==0\" @click=\"bindComment()\">立即评价</view>\r\n\t\t\t\t<view class=\"btn_apply\" v-if=\"order.state==1\" @click=\"refund()\">申请退款</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"xilu_status\">\r\n\t\t\t\t<view class=\"title\">{{order.state_text}}</view>\r\n\t\t\t\t<view v-if=\"order.state == 0\">请在{{order.expiretime_text}}前支付订单，过期自动取消</view>\r\n\r\n\t\t\t\t<!-- \t\t\t\t<view class=\"title\">待使用</view>-->\r\n\t\t\t\t<view v-if=\"order.state == 1\">订单已支付完成～</view> \r\n\r\n\t\t\t\t<!-- \t<view class=\"title\">已完成</view>-->\r\n\t\t\t\t<view v-else-if=\"order.state == 2\">本次服务已完成，感谢使用</view> \r\n\r\n\t\t\t\t<!-- \t\t\t<view class=\"title\">退款/取消</view>-->\r\n\t\t\t\t<view v-else-if=\"order.state>2\">退款理由 不想去了</view> \r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_goods flex-box mb30\">\r\n\t\t\t\t<image class=\"img\" :src=\"order.order_project.thumb_image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"m-ellipsis fs36 col-10 mb10\">{{order.order_project.scenery_name}}</view>\r\n\t\t\t\t\t<view class=\"fs30 col-3 mb10\">项目名称项目名称</view>\r\n\t\t\t\t\t<view class=\"flex-box col-3 mb10\">\r\n\t\t\t\t\t\t<text class=\"fs24\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs30 flex-1\">{{order.order_project.project_price}}</text>\r\n\t\t\t\t\t\t<view class=\"fs30 col-89 pr40\">数量 {{order.total_count}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box\">\r\n\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t<text class=\"fs30 col-89\">实付款 </text>\r\n\t\t\t\t\t\t\t<text class=\"fs30 col-price\">¥</text>\r\n\t\t\t\t\t\t\t<text class=\"fs40 col-price\">{{order.pay_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"opening_box mb30\">\r\n\t\t\t\t<view class=\"flex-box mb30\">\r\n\t\t\t\t\t<view class=\"col-normal fs30 mr30\">开放时间</view>\r\n\t\t\t\t\t<view class=\"fs30 col-5 mr15\">{{order.order_project.work_time}}</view>\r\n\t\t\t\t\t<image @click=\"callphone()\" class=\"icon\" src=\"/static/icon/icon_phone.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-box flex-align-start\">\r\n\t\t\t\t\t<view class=\"flex-1 mr10 fs30 col-5\">{{order.order_project.city?order.order_project.city.name:''}}{{order.order_project.district?order.order_project.district.name:''}}{{order.order_project.address}}</view>\r\n\t\t\t\t\t<image v-if=\"order.order_project.lat\" @click=\"bindOpenLocation()\" class=\"icon\" src=\"/static/icon/icon_go.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_code_swiper\" v-if=\"order.state==1&&order.order_qrcode.length>0\">\r\n\t\t\t\t<swiper :circular=\"true\" :interval=\"3000\" :duration=\"1000\" next-margin=\"35\">\r\n\t\t\t\t\t<swiper-item v-for=\"(qrcode,index) in order.order_qrcode\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"xilu_code_box \" :class=\"{disabled:qrcode.verifier_status==1}\">\r\n\t\t\t\t\t\t\t<view class=\"fs34 col-10 mb40\">券码{{index+1}}</view>\r\n\t\t\t\t\t\t\t<view class=\"code\">\r\n\t\t\t\t\t\t\t\t<image :src=\"qrcode.qrcode_text\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"mask\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"num_box flex-box\">\r\n\t\t\t\t\t\t\t\t<view class=\"fs30 col-5 mr30\">券码</view>\r\n\t\t\t\t\t\t\t\t<view class=\"flex-1 fs30 col-10 num\">{{qrcode.code}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"copy\" v-if=\"qrcode.verifier_status==0 && qrcode.code\" @click=\"codeCopy(qrcode.code)\">复制</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex-box flex-center\">\r\n\t\t\t\t\t\t\t\t<view class=\"index\">{{index+1}}/{{order.order_qrcode.length}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\r\n\t\t\t\t</swiper>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"g_order_info\">\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">订单号</view>\r\n\t\t\t\t\t<view class=\"fs30 col-10\">{{order.order_no}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">订单时间</view>\r\n\t\t\t\t\t<view class=\"fs30 col-10\">{{order.createtime_text}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">支付方式</view>\r\n\t\t\t\t\t<image src=\"/static/icon/icon_wx.png\" mode=\"aspectFit\" class=\"g-icon30 mr15\"></image>\r\n\t\t\t\t\t<view class=\"fs30 col-10\">微信支付</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">商品金额</view>\r\n\t\t\t\t\t<view class=\"col-10\">\r\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs40\">{{order.total_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">优惠劵</view>\r\n\t\t\t\t\t<view class=\"col-10\">\r\n\t\t\t\t\t\t<text class=\"fs24\">-¥</text>\r\n\t\t\t\t\t\t<text class=\"fs34\">{{order.coupon_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">支付金额</view>\r\n\t\t\t\t\t<view class=\"col-10\">\r\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs40\">{{order.pay_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"flex-box mb50\" v-if=\"order.state>2\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">退款金额</view>\r\n\t\t\t\t\t<view class=\"col-price\">\r\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs40\">{{order.refund_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"flex-box flex-end mb45\">\r\n\t\t\t\t\t<view class=\"fs30 col-89 mr20\">共计</view>\r\n\t\t\t\t\t<view class=\"col-price\">\r\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs40\">{{order.pay_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\torderId: 0,\r\n\t\t\t\torder:{\n\t\t\t\t\tstate_text:'',\n\t\t\t\t\torder_no:'',\n\t\t\t\t\ttotal_count: 0,\n\t\t\t\t\tcreatetime_text: '',\n\t\t\t\t\ttotal_price: 0,\n\t\t\t\t\tpay_price: 0,\n\t\t\t\t\tcoupon_price: 0,\n\t\t\t\t\torder_project:{\n\t\t\t\t\t\ttel:'',\n\t\t\t\t\t\taddress:'',\n\t\t\t\t\t\tscenery_name:'',\n\t\t\t\t\t\tthumb_image: '',\n\t\t\t\t\t\tproject_name: '',\n\t\t\t\t\t\t\n\t\t\t\t\t},\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\n\t\t\tthis.orderId = options.id || 0;\r\n\t\t\tthis.fetchDetail();\r\n\t\t},\n\t\tmethods:{\n\t\t\tfetchDetail(){\n\t\t\t\tthis.$core.get({url:'xilutour.scenery_order/detail',data:{order_id: this.orderId},success:(ret)=>{\n\t\t\t\t\tthis.order = ret.data;\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: ret.data.state_text\n\t\t\t\t\t})\n\t\t\t\t}});\n\t\t\t},\n\t\t\tcodeCopy(code){\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: code,\n\t\t\t\t\tsuccess: function () {\n\t\t\t\t\t\tconsole.log('success');\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t},\n\t\t\t//取消\n\t\t\torderCancel(){\n\t\t\t\tlet page = this;\n\t\t\t\tlet order = this.order;\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle:'提示',\n\t\t\t\t\tcontent: '确认取消订单？',\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\tif(res.confirm){\n\t\t\t\t\t\t\tpage.$core.post({url:'xilutour.scenery_order/cancel',data:{order_id: order.id},loading:true,success:(ret)=>{\n\t\t\t\t\t\t\t\tpage.fetchDetail();\n\t\t\t\t\t\t\t }});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tpayment(){\n\t\t\t\tlet order = this.order;\n\t\t\t\t//#ifdef MP-WEIXIN\n\t\t\t\tthis.$core.post({url:'xilutour.pay/pay',data:{type:'scenery_order',pay_type:1,order_id:order.id,platform:'wxmini'},success:(ret)=>{\n\t\t\t\t\tlet wxconfig =  ret.data;\n\t\t\t\t\tthis.$core.payment(wxconfig,function(){\n\t\t\t\t\t\t\n\t\t\t\t\t})\n\t\t\t\t}});\n\t\t\t\t//#endif\n\t\t\t},\n\t\t\t\n\t\t\t//拨打电话\n\t\t\tcallphone(){\n\t\t\t\tlet tel = this.order.order_project.tel;\n\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\tphoneNumber: tel\n\t\t\t\t})\n\t\t\t},\n\t\t\t//导航\n\t\t\tbindOpenLocation(){\n\t\t\t\tlet scenery = this.order.order_project;\n\t\t\t\tlet address = (scenery.city?scenery.city.name:'') + (scenery.district?scenery.district.name:'') + (scenery.address)\n\t\t\t\tuni.openLocation({\n\t\t\t\t\tlatitude: Number(scenery.lat),\n\t\t\t\t\tlongitude: Number(scenery.lng),\n\t\t\t\t\tname: scenery.scenery_name,\n\t\t\t\t\taddress: address\n\t\t\t\t})\n\t\t\t},\n\t\t\t//退款\n\t\t\trefund(){\n\t\t\t\tlet page = this;\n\t\t\t\tlet order = this.order;\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '确认退款',\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\tif(res.confirm){\n\t\t\t\t\t\t\tpage.$core.post({url:'xilutour.scenery_order/refund',data:{order_id:order.id},success:(ret)=>{\n\t\t\t\t\t\t\t\tuni.showToast({title: ret.msg,icon: 'none'});\n\t\t\t\t\t\t\t\tpage.fetchDetail()\n\t\t\t\t\t\t\t},fail:(ret)=>{\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle:'提示',\n\t\t\t\t\t\t\t\t\tcontent: ret.msg\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tbindComment(){\n\t\t\t\tlet order = this.order;\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/immediate_evaluation/immediate_evaluation',\n\t\t\t\t\tevents:{\n\t\t\t\t\t\tcommentSuccess: data=>{\n\t\t\t\t\t\t\torder.comment_status=1;\n\t\t\t\t\t\t\tthis.order = order;\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\tres.eventChannel.emit(\"addComment\",order)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.g_order_info {\r\n\t\tpadding-bottom: 5rpx;\r\n\t}\r\n\r\n\t.g_order_foot1 {\r\n\t\tpadding-left: 30rpx;\r\n\t}\r\n\r\n\t.xilu {\r\n\t\t&_status {\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tmargin: 0 0 40rpx;\r\n\t\t\tbackground: rgba(5, 185, 174, 0.1);\r\n\t\t\tborder-radius: 22rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #555555;\r\n\t\t\tline-height: 30rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\tmargin: 0 0 20rpx;\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: var(--normal);\r\n\t\t\t\tline-height: 36rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_goods {\r\n\r\n\r\n\t\t\t.img {\r\n\t\t\t\tmargin-right: 30rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 180rpx;\r\n\t\t\t\theight: 180rpx;\r\n\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.opening_box {\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t.icon {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 34rpx;\r\n\t\t\t\theight: 34rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_code_box {\r\n\t\t\tmargin: 0 0 30rpx;\r\n\t\t\tpadding: 30rpx 30rpx 50rpx;\r\n\t\t\twidth: 670rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t\tborder-radius: 25rpx;\r\n\r\n\t\t\t.code {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin: 0 auto 30rpx;\r\n\t\t\t\twidth: 260rpx;\r\n\t\t\t\theight: 260rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\twidth: 260rpx;\r\n\t\t\t\t\theight: 260rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.mask {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\twidth: 260rpx;\r\n\t\t\t\t\theight: 260rpx;\r\n\t\t\t\t\tbackground: #D8D8D8;\r\n\t\t\t\t\topacity: 0.9;\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.num_box {\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tpadding: 0 30rpx 0 35rpx;\r\n\t\t\t\twidth: 530rpx;\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tborder-radius: 34rpx;\r\n\t\t\t\tborder: 1px solid #EEEEEE;\r\n\r\n\t\t\t\t.copy {\r\n\t\t\t\t\twidth: 66rpx;\r\n\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\tbackground: var(--normal);\r\n\t\t\t\t\tborder-radius: 14rpx;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\tline-height: 40rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_code_box.disabled {\r\n\t\t\t.code {\r\n\t\t\t\t.mask {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.num_box {\r\n\t\t\t\t.num {\r\n\t\t\t\t\tcolor: #898989;\r\n\t\t\t\t\ttext-decoration: line-through;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.copy {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_code_swiper {\r\n\t\t\tmargin: 0 0 30rpx;\r\n\r\n\t\t\tswiper {\r\n\t\t\t\theight: 590rpx;\r\n\r\n\t\t\t\t.xilu_code_box {\r\n\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\twidth: 568rpx;\r\n\t\t\t\t\tpadding: 30rpx;\r\n\r\n\t\t\t\t\t.num_box {\r\n\t\t\t\t\t\twidth: 508rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.index {\r\n\t\t\t\t\t\tmargin: 30rpx auto 0;\r\n\t\t\t\t\t\tpadding: 0 6rpx;\r\n\t\t\t\t\t\theight: 34rpx;\r\n\t\t\t\t\t\tbackground: rgba(5, 185, 174, 0.1);\r\n\t\t\t\t\t\tborder-radius: 11rpx;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #05B9AE;\r\n\t\t\t\t\t\tline-height: 34rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.page-foot~.container {\r\n\t\t\tpadding-bottom: 170rpx;\r\n\t\t}\r\n\r\n\t\t.container {\r\n\t\t\tpadding-top: 30rpx;\r\n\t\t\tpadding-left: 40rpx;\r\n\t\t\tpadding-right: 40rpx;\r\n\t\t}\r\n\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_order_detail.vue?vue&type=style&index=0&id=8fe3bdf4&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./landscape_order_detail.vue?vue&type=style&index=0&id=8fe3bdf4&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341208\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}