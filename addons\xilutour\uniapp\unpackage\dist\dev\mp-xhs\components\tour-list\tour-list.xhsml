<view><block xhs:for="{{tourList}}" xhs:for-item="item" xhs:for-index="__i0__"><navigator class="item" url="{{'/pages/travel_detail/travel_detail?id='+item.id}}" hover-class="none"><view class="mb20"><block xhs:if="{{item.level}}"><text class="level">{{item.level.name}}</text></block><text class="g_feng">{{item.points}}</text><text class="fs36 col-10">{{item.name}}</text></view><view class="desc m-ellipsis mb15">{{item.sub_name}}</view><view class="flex-box mb20"><block xhs:if="{{item.tour_date}}"><view class="flex-1 col-price"><text class="fs30">¥</text><text class="fs40">{{item.tour_date.salesprice}}</text><text class="fs30">起</text></view></block><view class="fs28 col-a">{{item.view_count+"人浏览"}}</view></view><block xhs:if="{{item.images_text.length>0}}"><view class="img_wrap flex-box flex-align-start"><image class="img1" src="{{item.images_text[0]}}" mode="aspectFill"></image><block xhs:if="{{item.images_text.length>1}}"><view class="flex-1"><image class="img2" src="{{item.images_text[1]}}" mode="aspectFill"></image><block xhs:if="{{item.images_text.length>2}}"><image class="img2" src="{{item.images_text[2]}}" mode="aspectFill"></image></block></view></block></view></block></navigator></block></view>