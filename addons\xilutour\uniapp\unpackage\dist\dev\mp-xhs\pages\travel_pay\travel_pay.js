(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/travel_pay/travel_pay"],{

/***/ 239:
/*!******************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/main.js?{"page":"pages%2Ftravel_pay%2Ftravel_pay"} ***!
  \******************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 3);
__webpack_require__(/*! uni-pages */ 25);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 24));
var _travel_pay = _interopRequireDefault(__webpack_require__(/*! ./pages/travel_pay/travel_pay.vue */ 240));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_travel_pay.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["createPage"]))

/***/ }),

/***/ 240:
/*!***********************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue ***!
  \***********************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _travel_pay_vue_vue_type_template_id_59cce134_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./travel_pay.vue?vue&type=template&id=59cce134&scoped=true& */ 241);
/* harmony import */ var _travel_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./travel_pay.vue?vue&type=script&lang=js& */ 243);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _travel_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _travel_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _travel_pay_vue_vue_type_style_index_0_id_59cce134_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./travel_pay.vue?vue&type=style&index=0&id=59cce134&lang=less&scoped=true& */ 245);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 31);

var renderjs





/* normalize component */

var component = Object(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _travel_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _travel_pay_vue_vue_type_template_id_59cce134_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _travel_pay_vue_vue_type_template_id_59cce134_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "59cce134",
  null,
  false,
  _travel_pay_vue_vue_type_template_id_59cce134_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/travel_pay/travel_pay.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 241:
/*!******************************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue?vue&type=template&id=59cce134&scoped=true& ***!
  \******************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_template_id_59cce134_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_pay.vue?vue&type=template&id=59cce134&scoped=true& */ 242);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_template_id_59cce134_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_template_id_59cce134_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_template_id_59cce134_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_template_id_59cce134_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 242:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue?vue&type=template&id=59cce134&scoped=true& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uniPopup: function () {
      return __webpack_require__.e(/*! import() | uni_modules/uni-popup/components/uni-popup/uni-popup */ "uni_modules/uni-popup/components/uni-popup/uni-popup").then(__webpack_require__.bind(null, /*! @/uni_modules/uni-popup/components/uni-popup/uni-popup.vue */ 397))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 243:
/*!************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue?vue&type=script&lang=js& ***!
  \************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_pay.vue?vue&type=script&lang=js& */ 244);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 244:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var validate = __webpack_require__(/*! ../../xilu/validate.js */ 116);
var _default = {
  data: function data() {
    return {
      tour: {
        coupon_id: 0,
        tour_id: 0,
        buy_adult_count: 0,
        buy_child_count: 0,
        tour_date_id: 0,
        contact_name: '',
        contact_mobile: ''
      },
      order: {
        coupon_price: 0,
        coupon_list: [],
        pay_price: 0,
        total_price: 0,
        tour: {
          name: '',
          sub_name: '',
          thumb_image_text: ''
        },
        tour_date: {
          appoint_date_text: "",
          appoint_date_week: "",
          appoint_end_date_text: "",
          appoint_end_date_week: "",
          id: 0,
          originprice: 0,
          salesprice: 0,
          set_count: 0,
          status: "normal",
          tour_id: 0
        }
      },
      loading: false,
      travelerList: []
    };
  },
  onLoad: function onLoad() {
    var page = this;
    this.getOpenerEventChannel().on("tourTransfor", function (tour) {
      tour.coupon_id = 0;
      tour.contact_name = '';
      tour.contact_mobile = '';
      page.tour = tour;
      page.preOrder();
    });
  },
  methods: {
    preOrder: function preOrder() {
      var _this = this;
      this.$core.post({
        url: 'xilutour.tour_order/pre_order',
        data: this.tour,
        success: function success(ret) {
          _this.order = ret.data;
          if (ret.data.coupon) _this.tour.coupon_id = ret.data.coupon.id;
        },
        fail: function fail(err) {
          uni.showModal({
            title: '提示',
            content: err.msg,
            showCancel: false,
            success: function success(res) {
              if (res.confirm) {
                uni.navigateBack({});
              }
            }
          });
          return false;
        }
      });
    },
    //下单
    createOrder: function createOrder() {
      var _this2 = this;
      var order = this.tour;
      var travelerList = this.travelerList;
      if (travelerList.length <= 0 || travelerList.length != order.buy_adult_count + order.buy_child_count) {
        uni.showToast({
          title: "请选择出行人信息",
          icon: 'none'
        });
        return;
      }
      var travelerIds = [];
      travelerList.forEach(function (item) {
        return travelerIds.push(item.id);
      });
      order.traveler_ids = travelerIds.join(',');
      var rule = [{
        name: 'contact_name',
        nameChn: '姓名',
        rules: ['require'],
        errorMsg: {
          require: '请填写姓名'
        }
      }, {
        name: 'contact_mobile',
        nameChn: '手机号',
        rules: ['require', 'length:11'],
        errorMsg: {
          require: '请填写手机号',
          length: "手机号错误"
        }
      }, {
        name: 'tour_id',
        nameChn: '线路',
        rules: ['gt:0'],
        errorMsg: {
          gt: '线路错误'
        }
      }, {
        name: 'tour_date_id',
        nameChn: '线路日期',
        rules: ['gt:0'],
        errorMsg: {
          gt: '线路日期错误'
        }
      }];
      // 是否全部通过，返回Boolean
      if (!validate.check(order, rule)) {
        uni.showToast({
          title: validate.getError()[0],
          icon: 'none'
        });
        return;
      }
      this.loading = true;
      this.$core.post({
        url: 'xilutour.tour_order/create_order',
        data: order,
        loading: true,
        success: function success(ret) {
          _this2.loading = false;
          //下单成功，前往收银台
          _this2.payment(ret.data);
        },
        fail: function fail(err) {
          _this2.loading = false;
          uni.showModal({
            title: '提示',
            content: err.msg,
            showCancel: err.code == 3 ? true : false,
            success: function success(res) {
              if (res.confirm) {
                if (err.code == 3) {
                  uni.redirectTo({
                    url: '/pages/travel_order/travel_order'
                  });
                }
              }
            }
          });
          return false;
        }
      });
    },
    payment: function payment(order) {},
    //添加出行人
    bindTraveler: function bindTraveler() {
      var _this3 = this;
      var tour = this.tour;
      uni.navigateTo({
        url: '/pages/choose_traveler/choose_traveler',
        events: {
          chooseSuccess: function chooseSuccess(data) {
            _this3.travelerList = data;
            _this3.$forceUpdate();
          }
        },
        success: function success(res) {
          res.eventChannel.emit("travelTransfor", tour);
        }
      });
    },
    //删除
    bindDel: function bindDel(index) {
      var travelerList = this.travelerList;
      travelerList.splice(index, 1);
      this.travelerList = travelerList;
    },
    // 打开优惠券弹窗
    couponPopOpen: function couponPopOpen() {
      this.$refs.couponPopup.open();
    },
    // 关闭优惠券弹窗
    couponPopClose: function couponPopClose() {
      this.$refs.couponPopup.close();
    },
    //选择优惠券
    bindChooseCoupon: function bindChooseCoupon(index) {
      this.tour.coupon_id = this.order.coupon_list[index].id;
      this.preOrder();
      this.couponPopClose();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-xhs/dist/index.js */ 1)["default"]))

/***/ }),

/***/ 245:
/*!*********************************************************************************************************************************************************!*\
  !*** D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue?vue&type=style&index=0&id=59cce134&lang=less&scoped=true& ***!
  \*********************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_style_index_0_id_59cce134_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_pay.vue?vue&type=style&index=0&id=59cce134&lang=less&scoped=true& */ 246);
/* harmony import */ var _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_style_index_0_id_59cce134_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_style_index_0_id_59cce134_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_style_index_0_id_59cce134_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_style_index_0_id_59cce134_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_10_oneOf_1_0_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_10_oneOf_1_1_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_2_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_10_oneOf_1_3_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_less_loader_dist_cjs_js_ref_10_oneOf_1_4_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_10_oneOf_1_5_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_xbuilder_HBuilderX_4_23_2024070804_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_travel_pay_vue_vue_type_style_index_0_id_59cce134_lang_less_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 246:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-2!./node_modules/postcss-loader/src??ref--10-oneOf-1-3!./node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--10-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_pay/travel_pay.vue?vue&type=style&index=0&id=59cce134&lang=less&scoped=true& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[239,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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