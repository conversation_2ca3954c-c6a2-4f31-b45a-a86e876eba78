{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_order_detail/travel_order_detail.vue?1cbb", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_order_detail/travel_order_detail.vue?96db", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_order_detail/travel_order_detail.vue?6742", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_order_detail/travel_order_detail.vue?35d3", "uni-app:///pages/travel_order_detail/travel_order_detail.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_order_detail/travel_order_detail.vue?db6a", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/travel_order_detail/travel_order_detail.vue?8279"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "orderId", "order", "order_no", "total_count", "createtime_text", "total_price", "pay_price", "coupon_price", "order_tour", "thumb_image", "tour_name", "appoint_end_date_text", "appoint_end_date_week", "appoint_date_text", "appoint_date_week", "series_days", "tour_date_salesprice", "order_traveler", "onLoad", "methods", "fetchDetail", "url", "order_id", "success", "uni", "title", "bindRefund", "events", "aftersaleSuccess", "orderCancel", "content", "page", "loading", "payment", "pay_type", "platform", "bindComment", "commentSuccess", "res"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,4BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4I;AAC5I;AACuE;AACL;AACsC;;;AAGxG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,yFAAM;AACR,EAAE,0GAAM;AACR,EAAE,mHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA40B,CAAgB,4yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyIh2B;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAAC;QAAAtB;UAAAuB;QAAA;QAAAC;UACA;UACAC;YACAC;UACA;QACA;MAAA;IACA;IACA;IACAC;MAAA;MACA;MACAF;QACAH;QACAM;UACAC;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAL;QACAC;QACAK;QACAP;UACA;YACAQ;cAAAV;cAAAtB;gBAAAuB;cAAA;cAAAU;cAAAT;gBACAQ;cACA;YAAA;UACA;QACA;MACA;IACA;IACAE;MAAA;MACA;MAEA;QAAAZ;QAAAtB;UAAAmC;UAAAZ;UAAAa;QAAA;QAAAZ;UACA;UACA,4CAEA;QACA;MAAA;IAEA;IACAa;MAAA;MACA;MACAZ;QACAH;QACAM;UACAU;YACApC;YACA;UACA;QACA;QACAsB;UACAe;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtOA;AAAA;AAAA;AAAA;AAA+hD,CAAgB,g6CAAG,EAAC,C;;;;;;;;;;;ACAnjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/travel_order_detail/travel_order_detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/travel_order_detail/travel_order_detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./travel_order_detail.vue?vue&type=template&id=554a44d6&scoped=true&\"\nvar renderjs\nimport script from \"./travel_order_detail.vue?vue&type=script&lang=js&\"\nexport * from \"./travel_order_detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./travel_order_detail.vue?vue&type=style&index=0&id=554a44d6&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"554a44d6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/travel_order_detail/travel_order_detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_order_detail.vue?vue&type=template&id=554a44d6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_order_detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_order_detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"page-foot ptb15\">\r\n\t\t\t<view class=\"g_order_foot1 flex-box\" v-if=\"order.state == 0 || order.state == 2\">\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<!-- <view class=\"col-price fs24\">\r\n\t\t\t\t\t\t<view>请在24小时内支付</view>\r\n\t\t\t\t\t\t<view>过期自动取消</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn2\" v-if=\"order.state == 0\" @click=\"orderCancel()\">取消订单</view>\r\n\t\t\t\t<view class=\"btn3\" v-if=\"order.state == 0\" @click=\"payment()\">立即支付</view>\r\n\t\t\t\t<view class=\"btn3\" v-if=\"order.state==2 && order.comment_status==0\" @click=\"bindComment()\">立即评价</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"xilu_status\">\r\n\t\t\t\t<view class=\"title\">{{order.state_text}}</view>\r\n\t\t\t\t<view v-if=\"order.state==0\">请在{{order.expiretime_text}}前支付订单，过期自动取消</view>\r\n\t\t\t\t<!-- <view class=\"title\" v-else-if=\"order.state==1\">已支付</view> -->\r\n\t\t\t\t<view v-else-if=\"order.state==1\">订单已支付完成～</view>\r\n\t\t\t\t<!-- \t<view class=\"title\">已完成</view>-->\r\n\t\t\t\t<view v-else-if=\"order.state==2\">本次服务已完成，感谢使用</view> \r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_goods flex-box mb30\">\r\n\t\t\t\t<image class=\"img\" :src=\"order.order_tour.thumb_image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"m-ellipsis fs36 col-10 mb20\">{{order.order_tour.tour_name}}</view>\r\n\t\t\t\t\t<view class=\"flex-box col-3 mb20\">\r\n\t\t\t\t\t\t<text class=\"fs24\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs30 flex-1\">{{order.order_tour.tour_date_salesprice}}</text>\r\n\t\t\t\t\t\t<view class=\"fs30 col-89 pr40\">数量 {{order.total_count}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box\" style=\"height: 70rpx;\">\r\n\t\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t\t<text class=\"fs30 col-89\">实付款 </text>\r\n\t\t\t\t\t\t\t<text class=\"fs30 col-price\">¥</text>\r\n\t\t\t\t\t\t\t<text class=\"fs40 col-price\">{{order.pay_price}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"btn_apply\" v-if=\"(order.state==1 || order.state==5) && order.is_refund==1\" @click=\"bindRefund()\">申请退款</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_travel_time flex-box\">\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"fs24 col-89 mb15\">出发日期</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text class=\"fs36 col-10 mr15\">{{order.order_tour.appoint_date_text}}</text>\r\n\t\t\t\t\t\t<text class=\"fs30 col-5\">{{order.order_tour.appoint_date_week}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"line\"></view>\r\n\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t<view class=\"fs24 col-89 mb15\">结束日期</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text class=\"fs36 col-10 mr15\">{{order.order_tour.appoint_end_date_text}}</text>\r\n\t\t\t\t\t\t<text class=\"fs30 col-5\">{{order.order_tour.appoint_end_date_week}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"fs30 col-normal\">{{order.order_tour.series_days}}天</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_info_box fs30\">\r\n\t\t\t\t<view class=\"fs34 col-10 mb40\">联系人</view>\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"col-5 flex-1\">姓名</view>\r\n\t\t\t\t\t<view class=\"col-10\">{{order.contact_name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-box\">\r\n\t\t\t\t\t<view class=\"col-5 flex-1\">手机号码</view>\r\n\t\t\t\t\t<view class=\"col-10\">{{order.contact_mobile}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"xilu_info_box fs30\">\r\n\t\t\t\t<view class=\"fs34 col-10\">出行人信息</view>\r\n\t\t\t\t<view class=\"flex-box mt40\" v-for=\"(traveler,index) in order.order_traveler\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"fs30 col-10 mr20\">{{traveler.username}}</view>\r\n\t\t\t\t\t<view class=\"flex-1\">\r\n\t\t\t\t\t\t<image class=\"g-icon30\" v-if=\"traveler.gender==1\" src=\"../../static/icon/icon_gender1.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<image class=\"g-icon30\" v-else-if=\"traveler.gender==2\" src=\"../../static/icon/icon_gender2.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"identity1\" v-if=\"traveler.adult_type==1\">成人</view>\n\t\t\t\t\t<view class=\"identity2\" v-else-if=\"traveler.adult_type==2\">儿童</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"g_order_info\">\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">订单号</view>\r\n\t\t\t\t\t<view class=\"fs30 col-10\">{{order.order_no}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">订单时间</view>\r\n\t\t\t\t\t<view class=\"fs30 col-10\">{{order.createtime_text}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">支付方式</view>\r\n\t\t\t\t\t<image src=\"/static/icon/icon_wx.png\" mode=\"aspectFit\" class=\"g-icon30 mr15\"></image>\r\n\t\t\t\t\t<view class=\"fs30 col-10\">微信支付</view>\r\n\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">商品金额</view>\r\n\t\t\t\t\t<view class=\"col-10\">\r\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs40\">{{order.total_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t<view class=\"flex-box mb50\">\r\n\t\t\t\t\t<view class=\"fs30 col-5 flex-1\">优惠劵</view>\r\n\t\t\t\t\t<view class=\"col-10\">\r\n\t\t\t\t\t\t<text class=\"fs24\">-¥</text>\r\n\t\t\t\t\t\t<text class=\"fs34\">{{order.coupon_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"flex-box flex-end mb45\">\r\n\t\t\t\t\t<view class=\"fs30 col-89 mr20\">共计</view>\r\n\t\t\t\t\t<view class=\"col-price\">\r\n\t\t\t\t\t\t<text class=\"fs30\">¥</text>\r\n\t\t\t\t\t\t<text class=\"fs40\">{{order.pay_price}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"fs24 col-5 ptb30\" v-if=\"order.state == 1\">提示：{{order.tips}}</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\n\t\t\t\torderId: 0,\r\n\t\t\t\torder:{\n\t\t\t\t\torder_no:'',\n\t\t\t\t\ttotal_count: 0,\n\t\t\t\t\tcreatetime_text: '',\n\t\t\t\t\ttotal_price: 0,\n\t\t\t\t\tpay_price: 0,\n\t\t\t\t\tcoupon_price: 0,\n\t\t\t\t\torder_tour:{\n\t\t\t\t\t\tthumb_image: '',\n\t\t\t\t\t\ttour_name: '',\n\t\t\t\t\t\tappoint_end_date_text: '',\n\t\t\t\t\t\tappoint_end_date_week: '',\n\t\t\t\t\t\tappoint_date_text: '',\n\t\t\t\t\t\tappoint_date_week: '',\n\t\t\t\t\t\tseries_days: 0,\n\t\t\t\t\t\ttour_date_salesprice: 0,\n\t\t\t\t\t},\n\t\t\t\t\torder_traveler:[]\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\n\t\t\tthis.orderId = options.id || 0;\r\n\t\t\tthis.fetchDetail();\r\n\t\t},\n\t\tmethods:{\n\t\t\tfetchDetail(){\n\t\t\t\tthis.$core.get({url:'xilutour.tour_order/detail',data:{order_id: this.orderId},success:(ret)=>{\n\t\t\t\t\tthis.order = ret.data;\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: ret.data.state_text\n\t\t\t\t\t})\n\t\t\t\t}});\n\t\t\t},\n\t\t\t//退款\n\t\t\tbindRefund(){\n\t\t\t\tlet order = this.order;\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/apply_refund/apply_refund?id='+order.id,\n\t\t\t\t\tevents:{\n\t\t\t\t\t\taftersaleSuccess: data=>{\n\t\t\t\t\t\t\tthis.fetchDetail();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t//取消\n\t\t\torderCancel(){\n\t\t\t\tlet page = this;\n\t\t\t\tlet order = this.order;\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle:'提示',\n\t\t\t\t\tcontent: '确认取消订单？',\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\tif(res.confirm){\n\t\t\t\t\t\t\tpage.$core.post({url:'xilutour.tour_order/cancel',data:{order_id: order.id},loading:true,success:(ret)=>{\n\t\t\t\t\t\t\t\tpage.fetchDetail();\n\t\t\t\t\t\t\t }});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tpayment(){\n\t\t\t\tlet order = this.order;\n\t\t\t\t//#ifdef MP-WEIXIN\n\t\t\t\tthis.$core.post({url:'xilutour.pay/pay',data:{pay_type:1,order_id:order.id,platform:'wxmini'},success:(ret)=>{\n\t\t\t\t\tlet wxconfig =  ret.data;\n\t\t\t\t\tthis.$core.payment(wxconfig,function(){\n\t\t\t\t\t\t\n\t\t\t\t\t})\n\t\t\t\t}});\n\t\t\t\t//#endif\n\t\t\t},\n\t\t\tbindComment(){\n\t\t\t\tlet order = this.order;\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/tour_evaluation/tour_evaluation',\n\t\t\t\t\tevents:{\n\t\t\t\t\t\tcommentSuccess: data=>{\n\t\t\t\t\t\t\torder.comment_status=1;\n\t\t\t\t\t\t\tthis.order = order;\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\tres.eventChannel.emit(\"addComment\",order)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.g_order_info{\r\n\t\tpadding-bottom: 5rpx;\r\n\t}\r\n\t.g_order_foot1 {\r\n\t\tpadding-left: 30rpx;\r\n\t}\r\n\t.xilu {\r\n\t\t&_status {\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tmargin: 0 0 40rpx;\r\n\t\t\tbackground: rgba(5, 185, 174, 0.1);\r\n\t\t\tborder-radius: 22rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #555555;\r\n\t\t\tline-height: 30rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\tmargin: 0 0 20rpx;\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: var(--normal);\r\n\t\t\t\tline-height: 36rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_goods {\r\n\t\t\t.btn_apply {\r\n\t\t\t\twidth: 158rpx;\r\n\t\t\t\theight: 70rpx;\r\n\t\t\t\tborder-radius: 25rpx;\r\n\t\t\t\tborder: 1rpx solid var(--normal);\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: var(--normal);\r\n\t\t\t\tline-height: 70rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\r\n\t\t\t.img {\r\n\t\t\t\tmargin-right: 30rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 180rpx;\r\n\t\t\t\theight: 180rpx;\r\n\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_travel_time {\r\n\t\t\tpadding: 0 40rpx 0 30rpx;\r\n\t\t\tmargin: 0 0 30rpx;\r\n\t\t\theight: 120rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t.line {\r\n\t\t\t\tmargin: 0 60rpx;\r\n\t\t\t\twidth: 25rpx;\r\n\t\t\t\theight: 2rpx;\r\n\t\t\t\tbackground-color: var(--normal);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_info_box {\r\n\t\t\tmargin: 0 0 30rpx;\r\n\t\t\tpadding: 30rpx 30rpx 40rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t\tborder-radius: 25rpx;\r\n\r\n\t\t\t.identity1 {\r\n\t\t\t\twidth: 60rpx;\r\n\t\t\t\theight: 36rpx;\r\n\t\t\t\tbackground: #FFAB29;\r\n\t\t\t\tborder-radius: 5rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tline-height: 36rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\r\n\t\t\t.identity2 {\r\n\t\t\t\twidth: 60rpx;\r\n\t\t\t\theight: 36rpx;\r\n\t\t\t\tbackground: var(--normal);\r\n\t\t\t\tborder-radius: 5rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tline-height: 36rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t\t.page-foot~.container {\r\n\t\t\tpadding-bottom: 170rpx;\r\n\t\t}\r\n\r\n\t\t.container {\r\n\t\t\tpadding-top: 30rpx;\r\n\t\t\tpadding-left: 40rpx;\r\n\t\t\tpadding-right: 40rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_order_detail.vue?vue&type=style&index=0&id=554a44d6&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./travel_order_detail.vue?vue&type=style&index=0&id=554a44d6&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341206\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}