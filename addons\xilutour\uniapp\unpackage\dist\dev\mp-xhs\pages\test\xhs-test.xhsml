<view class="container data-v-d5a5ad60"><view class="header data-v-d5a5ad60"><text class="title data-v-d5a5ad60">小红书登录测试</text></view><view class="test-section data-v-d5a5ad60"><text class="section-title data-v-d5a5ad60">1. 测试获取用户信息</text><button data-event-opts="{{[['tap',[['testGetUserProfile',['$event']]]]]}}" class="test-btn data-v-d5a5ad60" bindtap="__e">获取用户信息</button><block xhs:if="{{userInfo}}"><view class="user-info data-v-d5a5ad60"><text class="data-v-d5a5ad60">{{"昵称: "+userInfo.nickName}}</text><text class="data-v-d5a5ad60">{{"性别: "+genderText}}</text><block xhs:if="{{userInfo.avatarUrl}}"><image class="avatar data-v-d5a5ad60" src="{{userInfo.avatarUrl}}"></image></block></view></block></view><view class="test-section data-v-d5a5ad60"><text class="section-title data-v-d5a5ad60">2. 测试完整登录流程</text><button data-event-opts="{{[['tap',[['testFullLogin',['$event']]]]]}}" class="test-btn data-v-d5a5ad60" bindtap="__e">完整登录测试</button><block xhs:if="{{loginResult}}"><view class="login-result data-v-d5a5ad60"><text class="data-v-d5a5ad60">{{"登录状态: "+(loginResult.success?'成功':'失败')}}</text><block xhs:if="{{loginResult.message}}"><text class="data-v-d5a5ad60">{{"消息: "+loginResult.message}}</text></block><block xhs:if="{{loginResult.userinfo}}"><text class="data-v-d5a5ad60">{{"用户ID: "+loginResult.userinfo.id}}</text></block></view></block></view><view class="test-section data-v-d5a5ad60"><text class="section-title data-v-d5a5ad60">3. 调试信息</text><view class="debug-info data-v-d5a5ad60"><block xhs:for="{{debugLogs}}" xhs:for-item="log" xhs:for-index="index" xhs:key="index"><text class="debug-log data-v-d5a5ad60">{{log}}</text></block></view><button data-event-opts="{{[['tap',[['clearLogs',['$event']]]]]}}" class="clear-btn data-v-d5a5ad60" bindtap="__e">清除日志</button></view></view>