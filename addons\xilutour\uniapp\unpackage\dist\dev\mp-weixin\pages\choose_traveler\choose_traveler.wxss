.xilu .page-foot.data-v-64cfede6 {
  padding: 20rpx 65rpx;
  background-color: #FFF;
}
.xilu .page-foot .btn1.data-v-64cfede6 {
  width: 290rpx;
  height: 90rpx;
  border-radius: 30rpx;
  border: 2rpx solid var(--normal);
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: var(--normal);
  line-height: 88rpx;
  text-align: center;
}
.xilu .page-foot .btn2.data-v-64cfede6 {
  width: 290rpx;
  height: 90rpx;
  border-radius: 30rpx;
  background: var(--normal);
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 90rpx;
  text-align: center;
}
.xilu .container.data-v-64cfede6 {
  padding: 30rpx 40rpx 160rpx !important;
}
.xilu_traveler.data-v-64cfede6 {
  margin: 0 0 30rpx;
  padding: 0 30rpx;
  width: 670rpx;
  height: 200rpx;
  background: #F7F9FB;
  border-radius: 20rpx;
  font-size: 30rpx;
  line-height: 32rpx;
}
.xilu_traveler .icon_gender.data-v-64cfede6 {
  display: block;
  width: 30rpx;
  height: 30rpx;
}
.xilu_traveler .icon_check.data-v-64cfede6 {
  display: block;
  width: 40rpx;
  height: 40rpx;
}

