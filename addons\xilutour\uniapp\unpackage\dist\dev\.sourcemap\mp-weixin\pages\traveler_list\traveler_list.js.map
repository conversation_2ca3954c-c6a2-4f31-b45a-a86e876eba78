{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/traveler_list/traveler_list.vue?189b", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/traveler_list/traveler_list.vue?eacc", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/traveler_list/traveler_list.vue?070a", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/traveler_list/traveler_list.vue?3f86", "uni-app:///pages/traveler_list/traveler_list.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/traveler_list/traveler_list.vue?c032", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/traveler_list/traveler_list.vue?8c6a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "travelerList", "onLoad", "methods", "fetch", "url", "success", "fail", "addTraveler", "uni", "events", "setSuccess", "bindEdit", "res", "bindDel", "title", "content", "page", "traveler_id", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsC11B;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;EAEA;EACAC;IACAC;MAAA;MACA;QAAAC;QAAAL;QAAAM;UACA;QACA;QACAC,0BAEA;MACA;IACA;IACA;IACAC;MAAA;MACAC;QACAJ;QACAK;UACAC;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACAH;QACAJ;QACAK;UACAC;YACA;UACA;QACA;QACAL;UACAO;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAL;QACAM;QACAC;QACAV;UACA;YACAW;cAAAZ;cAAAL;gBAAAkB;cAAA;cAAAZ;gBACAG;kBAAAM;kBAAAI;gBAAA;gBACAlB;gBACAgB;cACA;cACAV,0BAEA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1GA;AAAA;AAAA;AAAA;AAAyhD,CAAgB,05CAAG,EAAC,C;;;;;;;;;;;ACA7iD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/traveler_list/traveler_list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/traveler_list/traveler_list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./traveler_list.vue?vue&type=template&id=1a799274&scoped=true&\"\nvar renderjs\nimport script from \"./traveler_list.vue?vue&type=script&lang=js&\"\nexport * from \"./traveler_list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./traveler_list.vue?vue&type=style&index=0&id=1a799274&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1a799274\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/traveler_list/traveler_list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./traveler_list.vue?vue&type=template&id=1a799274&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./traveler_list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./traveler_list.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"xilu\">\n\t\t<view class=\"page-foot flex-box\" @click=\"addTraveler()\">\n\t\t\t<view class=\"g-btn1 flex-1\">新建出行人</view>\n\t\t</view>\n\t\t<view class=\"container\">\n\t\t\t<view class=\"xilu_traveler\"  v-for=\"(item,index) in travelerList\" :key=\"index\">\n\t\t\t\t<view class=\"\">\n\t\t\t\t\t<view class=\"flex-box mb30\">\n\t\t\t\t\t\t<view class=\"col-10 mr20\">{{item.username}}</view>\n\t\t\t\t\t\t<image v-if=\"item.gender == 1\" class=\"icon_gender\" src=\"/static/icon/icon_gender1.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t<image v-else class=\"icon_gender\" src=\"/static/icon/icon_gender2.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex-box mb20\">\n\t\t\t\t\t\t<view class=\"col-9 mr15\">身份证号</view>\n\t\t\t\t\t\t<view class=\"col-3 flex-1\">{{item.idcard}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex-box\">\n\t\t\t\t\t\t<view class=\"col-9 mr15\">手机号码</view>\n\t\t\t\t\t\t<view class=\"col-3 flex-1\">{{item.mobile}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex-box flex-end fs30 col-89\">\n\t\t\t\t\t<view class=\"flex-box mr30\" @click=\"bindEdit(index)\">\n\t\t\t\t\t\t<image src=\"/static/icon/icon_edit1.png\" mode=\"aspectFit\" class=\"icon\"></image>\n\t\t\t\t\t\t<view>编辑</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex-box\" @click=\"bindDel(index)\">\n\t\t\t\t\t\t<image src=\"/static/icon/icon_del1.png\" mode=\"aspectFit\" class=\"icon\"></image>\n\t\t\t\t\t\t<view>删除</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttravelerList:[]\n\t\t\t};\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.fetch();\n\t\t\t\n\t\t},\n\t\tmethods:{\n\t\t\tfetch() {\n\t\t\t\tthis.$core.get({url: 'xilutour.traveler/lists',data: this.tour,success: ret => {\n\t\t\t\t\t\tthis.travelerList = ret.data;\n\t\t\t\t\t},\n\t\t\t\t\tfail: err => {\n\t\t\t\t\t\t\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t//添加出行\n\t\t\taddTraveler(){\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/new_traveler/new_traveler',\n\t\t\t\t\tevents:{\n\t\t\t\t\t\tsetSuccess: data=>{\n\t\t\t\t\t\t\tthis.fetch();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tbindEdit(index){\n\t\t\t\tlet item = this.travelerList[index];\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/new_traveler/new_traveler',\n\t\t\t\t\tevents:{\n\t\t\t\t\t\tsetSuccess: data=>{\n\t\t\t\t\t\t\tthis.fetch();\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\tres.eventChannel.emit(\"editTransfor\",item)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tbindDel(index){\n\t\t\t\tlet page = this;\n\t\t\t\tlet travelerList = page.travelerList\n\t\t\t\tlet item = travelerList[index];\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '确认删除',\n\t\t\t\t\tsuccess(res){\n\t\t\t\t\t\tif(res.confirm){\n\t\t\t\t\t\t\tpage.$core.post({url: 'xilutour.traveler/del',data: {traveler_id: item.id},success: ret => {\n\t\t\t\t\t\t\t\tuni.showToast({title:'删除成功', icon:'none'})\n\t\t\t\t\t\t\t\t\ttravelerList.splice(index,1);\n\t\t\t\t\t\t\t\t\tpage.travelerList = travelerList;\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: err => {\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"less\" scoped>\n\t.xilu {\n\t\t.page-foot {\n\t\t\tpadding: 20rpx 40rpx;\n\t\t\tbackground-color: #FFF;\n\n\t\t\t.btn1 {\n\t\t\t\twidth: 290rpx;\n\t\t\t\theight: 90rpx;\n\t\t\t\tborder-radius: 30rpx;\n\t\t\t\tborder: 2rpx solid var(--normal);\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: var(--normal);\n\t\t\t\tline-height: 88rpx;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\n\t\t\t.btn2 {\n\t\t\t\twidth: 290rpx;\n\t\t\t\theight: 90rpx;\n\t\t\t\tborder-radius: 30rpx;\n\t\t\t\tbackground: var(--normal);\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tline-height: 90rpx;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t}\n\n\t\t.container {\n\t\t\tpadding: 30rpx 40rpx 160rpx !important;\n\t\t}\n\n\t\t&_traveler {\n\t\t\tmargin: 0 0 30rpx;\n\t\t\tpadding: 30rpx;\n\t\t\twidth: 670rpx;\n\t\t\tbackground: #F7F9FB;\n\t\t\tborder-radius: 20rpx;\n\t\t\tfont-size: 30rpx;\n\t\t\tline-height: 32rpx;\n\n\t\t\t.icon_gender {\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: 30rpx;\n\t\t\t\theight: 30rpx;\n\t\t\t}\n\n\t\t\t.icon_check {\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 40rpx;\n\t\t\t}\n\n\t\t\t.icon {\n\t\t\t\tmargin-right: 8rpx;\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: 30rpx;\n\t\t\t\theight: 30rpx;\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./traveler_list.vue?vue&type=style&index=0&id=1a799274&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./traveler_list.vue?vue&type=style&index=0&id=1a799274&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341172\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}