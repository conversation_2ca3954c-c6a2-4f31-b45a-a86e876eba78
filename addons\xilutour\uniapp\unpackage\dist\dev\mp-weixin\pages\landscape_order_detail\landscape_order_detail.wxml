<view class="xilu data-v-8fe3bdf4"><view class="page-foot ptb15 data-v-8fe3bdf4"><view class="g_order_foot1 flex-box data-v-8fe3bdf4"><view class="flex-1 data-v-8fe3bdf4"><block wx:if="{{order.state==0}}"><view class="col-price fs24 data-v-8fe3bdf4"></view></block></view><block wx:if="{{order.state==0}}"><view data-event-opts="{{[['tap',[['orderCancel']]]]}}" class="btn2 data-v-8fe3bdf4" bindtap="__e">取消订单</view></block><block wx:if="{{order.state==0}}"><view data-event-opts="{{[['tap',[['payment']]]]}}" class="btn3 data-v-8fe3bdf4" bindtap="__e">立即支付</view></block><block wx:if="{{order.state==1}}"><button class="btn2 data-v-8fe3bdf4" open-type="contact">联系客服</button></block><block wx:if="{{order.state==2&&order.comment_status==0}}"><view data-event-opts="{{[['tap',[['bindComment']]]]}}" class="btn3 data-v-8fe3bdf4" bindtap="__e">立即评价</view></block><block wx:if="{{order.state==1}}"><view data-event-opts="{{[['tap',[['refund']]]]}}" class="btn_apply data-v-8fe3bdf4" bindtap="__e">申请退款</view></block></view></view><view class="container data-v-8fe3bdf4"><view class="xilu_status data-v-8fe3bdf4"><view class="title data-v-8fe3bdf4">{{order.state_text}}</view><block wx:if="{{order.state==0}}"><view class="data-v-8fe3bdf4">{{"请在"+order.expiretime_text+"前支付订单，过期自动取消"}}</view></block><block wx:if="{{order.state==1}}"><view class="data-v-8fe3bdf4">订单已支付完成～</view></block><block wx:else><block wx:if="{{order.state==2}}"><view class="data-v-8fe3bdf4">本次服务已完成，感谢使用</view></block><block wx:else><block wx:if="{{order.state>2}}"><view class="data-v-8fe3bdf4">退款理由 不想去了</view></block></block></block></view><view class="xilu_goods flex-box mb30 data-v-8fe3bdf4"><image class="img data-v-8fe3bdf4" src="{{order.order_project.thumb_image}}" mode="aspectFill"></image><view class="flex-1 data-v-8fe3bdf4"><view class="m-ellipsis fs36 col-10 mb10 data-v-8fe3bdf4">{{order.order_project.scenery_name}}</view><view class="fs30 col-3 mb10 data-v-8fe3bdf4">项目名称项目名称</view><view class="flex-box col-3 mb10 data-v-8fe3bdf4"><text class="fs24 data-v-8fe3bdf4">¥</text><text class="fs30 flex-1 data-v-8fe3bdf4">{{order.order_project.project_price}}</text><view class="fs30 col-89 pr40 data-v-8fe3bdf4">{{"数量 "+order.total_count}}</view></view><view class="flex-box data-v-8fe3bdf4"><view class="flex-1 data-v-8fe3bdf4"><text class="fs30 col-89 data-v-8fe3bdf4">实付款</text><text class="fs30 col-price data-v-8fe3bdf4">¥</text><text class="fs40 col-price data-v-8fe3bdf4">{{order.pay_price}}</text></view></view></view></view><view class="opening_box mb30 data-v-8fe3bdf4"><view class="flex-box mb30 data-v-8fe3bdf4"><view class="col-normal fs30 mr30 data-v-8fe3bdf4">开放时间</view><view class="fs30 col-5 mr15 data-v-8fe3bdf4">{{order.order_project.work_time}}</view><image class="icon data-v-8fe3bdf4" src="/static/icon/icon_phone.png" mode="aspectFit" data-event-opts="{{[['tap',[['callphone']]]]}}" bindtap="__e"></image></view><view class="flex-box flex-align-start data-v-8fe3bdf4"><view class="flex-1 mr10 fs30 col-5 data-v-8fe3bdf4">{{(order.order_project.city?order.order_project.city.name:'')+(order.order_project.district?order.order_project.district.name:'')+order.order_project.address}}</view><block wx:if="{{order.order_project.lat}}"><image class="icon data-v-8fe3bdf4" src="/static/icon/icon_go.png" mode="aspectFit" data-event-opts="{{[['tap',[['bindOpenLocation']]]]}}" bindtap="__e"></image></block></view></view><block wx:if="{{$root.g0}}"><view class="xilu_code_swiper data-v-8fe3bdf4"><swiper circular="{{true}}" interval="{{3000}}" duration="{{1000}}" next-margin="35" class="data-v-8fe3bdf4"><block wx:for="{{$root.l0}}" wx:for-item="qrcode" wx:for-index="index" wx:key="index"><swiper-item class="data-v-8fe3bdf4"><view class="{{['xilu_code_box','','data-v-8fe3bdf4',(qrcode.$orig.verifier_status==1)?'disabled':'']}}"><view class="fs34 col-10 mb40 data-v-8fe3bdf4">{{"券码"+(index+1)}}</view><view class="code data-v-8fe3bdf4"><image src="{{qrcode.$orig.qrcode_text}}" mode="aspectFit" class="data-v-8fe3bdf4"></image><view class="mask data-v-8fe3bdf4"></view></view><view class="num_box flex-box data-v-8fe3bdf4"><view class="fs30 col-5 mr30 data-v-8fe3bdf4">券码</view><view class="flex-1 fs30 col-10 num data-v-8fe3bdf4">{{qrcode.$orig.code}}</view><block wx:if="{{qrcode.$orig.verifier_status==0&&qrcode.$orig.code}}"><view data-event-opts="{{[['tap',[['codeCopy',['$0'],[[['order.order_qrcode','',index,'code']]]]]]]}}" class="copy data-v-8fe3bdf4" bindtap="__e">复制</view></block></view><view class="flex-box flex-center data-v-8fe3bdf4"><view class="index data-v-8fe3bdf4">{{index+1+"/"+qrcode.g1}}</view></view></view></swiper-item></block></swiper></view></block><view class="g_order_info data-v-8fe3bdf4"><view class="flex-box mb50 data-v-8fe3bdf4"><view class="fs30 col-5 flex-1 data-v-8fe3bdf4">订单号</view><view class="fs30 col-10 data-v-8fe3bdf4">{{order.order_no}}</view></view><view class="flex-box mb50 data-v-8fe3bdf4"><view class="fs30 col-5 flex-1 data-v-8fe3bdf4">订单时间</view><view class="fs30 col-10 data-v-8fe3bdf4">{{order.createtime_text}}</view></view><view class="flex-box mb50 data-v-8fe3bdf4"><view class="fs30 col-5 flex-1 data-v-8fe3bdf4">支付方式</view><image class="g-icon30 mr15 data-v-8fe3bdf4" src="/static/icon/icon_wx.png" mode="aspectFit"></image><view class="fs30 col-10 data-v-8fe3bdf4">微信支付</view></view><view class="flex-box mb50 data-v-8fe3bdf4"><view class="fs30 col-5 flex-1 data-v-8fe3bdf4">商品金额</view><view class="col-10 data-v-8fe3bdf4"><text class="fs30 data-v-8fe3bdf4">¥</text><text class="fs40 data-v-8fe3bdf4">{{order.total_price}}</text></view></view><view class="flex-box mb50 data-v-8fe3bdf4"><view class="fs30 col-5 flex-1 data-v-8fe3bdf4">优惠劵</view><view class="col-10 data-v-8fe3bdf4"><text class="fs24 data-v-8fe3bdf4">-¥</text><text class="fs34 data-v-8fe3bdf4">{{order.coupon_price}}</text></view></view><view class="flex-box mb50 data-v-8fe3bdf4"><view class="fs30 col-5 flex-1 data-v-8fe3bdf4">支付金额</view><view class="col-10 data-v-8fe3bdf4"><text class="fs30 data-v-8fe3bdf4">¥</text><text class="fs40 data-v-8fe3bdf4">{{order.pay_price}}</text></view></view><block wx:if="{{order.state>2}}"><view class="flex-box mb50 data-v-8fe3bdf4"><view class="fs30 col-5 flex-1 data-v-8fe3bdf4">退款金额</view><view class="col-price data-v-8fe3bdf4"><text class="fs30 data-v-8fe3bdf4">¥</text><text class="fs40 data-v-8fe3bdf4">{{order.refund_price}}</text></view></view></block><view class="flex-box flex-end mb45 data-v-8fe3bdf4"><view class="fs30 col-89 mr20 data-v-8fe3bdf4">共计</view><view class="col-price data-v-8fe3bdf4"><text class="fs30 data-v-8fe3bdf4">¥</text><text class="fs40 data-v-8fe3bdf4">{{order.pay_price}}</text></view></view></view></view></view>