{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/store_information/store_information.vue?bff6", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/store_information/store_information.vue?473c", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/store_information/store_information.vue?725d", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/store_information/store_information.vue?3306", "uni-app:///pages/store_information/store_information.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/store_information/store_information.vue?65e5", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/store_information/store_information.vue?038e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "sceneryList", "data", "statusBarHeight", "categoryList", "sceneryListMore", "page", "query", "category_id", "onLoad", "onPullDownRefresh", "onReachBottom", "onUnload", "methods", "tabClick", "refreshPage", "url", "loading", "success", "uni", "refresh", "fetch"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,0BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACqE;AACL;AACsC;;;AAGtG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA00B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACgC91B;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAH;MACAI;QAAAC;MAAA;MACAC;QAAAC;MAAA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC,+BACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QAAAC;QAAAd;QAAAe;QAAAC;UACA;QACA;MAAA;MACA;MACA;MACAC;IACA;IACAC;MACA;MACA;QAAAd;MAAA;MACA;IACA;IACAe;MACA;MACAd;MACA,wHAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrFA;AAAA;AAAA;AAAA;AAA6hD,CAAgB,85CAAG,EAAC,C;;;;;;;;;;;ACAjjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/store_information/store_information.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/store_information/store_information.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./store_information.vue?vue&type=template&id=5b293664&scoped=true&\"\nvar renderjs\nimport script from \"./store_information.vue?vue&type=script&lang=js&\"\nexport * from \"./store_information.vue?vue&type=script&lang=js&\"\nimport style0 from \"./store_information.vue?vue&type=style&index=0&id=5b293664&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5b293664\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/store_information/store_information.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./store_information.vue?vue&type=template&id=5b293664&scoped=true&\"", "var components\ntry {\n  components = {\n    sceneryList: function () {\n      return import(\n        /* webpackChunkName: \"components/scenery-list/scenery-list\" */ \"@/components/scenery-list/scenery-list.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./store_information.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./store_information.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"container\" style=\"padding-bottom: 30rpx;\">\r\n\r\n\t\t\t<view class=\"pr\">\r\n\t\t\t\t\r\n\r\n\t\t\t\t<view class=\"xilu_select\">\r\n\t\t\t\t\t<view class=\"g_tab\">\r\n\t\t\t\t\t\t<view class=\"item\" :class=\"{'active': query.category_id == -1}\" @click=\"tabClick(-1)\">全部</view>\r\n\t\t\t\t\t\t<view class=\"item\" :class=\"{'active': query.category_id == item.id}\" @click=\"tabClick(item.id)\" v-for=\"(item,index) in categoryList\">{{item.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"p40\">\r\n\t\t\t\t\t<view class=\"g_landscape_list\">\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<scenery-list :sceneryList=\"sceneryList\"></scenery-list>\n\t\t\t\t\t\t<view class=\"g-btn3-wrap\">\n\t\t\t\t\t\t\t<view class=\"g-btn3\" @click=\"fetch\">{{sceneryListMore.text}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\n\timport sceneryList from '@/components/scenery-list/scenery-list.vue'\n\tconst app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tsceneryList\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatusBarHeight: 20,\r\n\t\t\t\tcategoryList:[],\n\t\t\t\tsceneryList:[],\n\t\t\t\tsceneryListMore:{page:1},\n\t\t\t\tquery:{category_id: -1}\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\n\t\t\tthis.refreshPage();\r\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tthis.refreshPage();\n\t\t},\n\t\tonReachBottom() {\n\t\t\tthis.fetch();\n\t\t},\n\t\tonUnload() {\n\t\t},\r\n\t\tmethods: {\n\t\t\t//切换分类\r\n\t\t\ttabClick(id) {\r\n\t\t\t\tthis.query.category_id = id;\n\t\t\t\tthis.refresh();\r\n\t\t\t},\n\t\t\trefreshPage(){\n\t\t\t\t //分类\n\t\t\t\t this.$core.get({url:'xilutour.common/scenery_category',data:{},loading:false,success:(ret)=>{\n\t\t\t\t \tthis.categoryList = ret.data;\n\t\t\t\t  }});\n\t\t\t\t  //列表\n\t\t\t\t  this.refresh();\n\t\t\t\t  uni.stopPullDownRefresh();\n\t\t\t},\n\t\t\trefresh(){\n\t\t\t\tthis.sceneryList = [];\n\t\t\t\tthis.sceneryListMore = {page:1};\n\t\t\t\tthis.fetch();\n\t\t\t},\n\t\t\tfetch(){\n\t\t\t\tlet query = this.query;\n\t\t\t\tquery.pagesize = 10;\n\t\t\t\tthis.$util.fetch(this, 'xilutour.user/verifier_shop', query, 'sceneryListMore', 'sceneryList', 'data', data=>{\n\t\t\t\t  \n\t\t\t\t})\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.xilu {\r\n\t\t&_swiper {\r\n\t\t\tposition: relative;\r\n\t\t\tpadding: 30rpx 40rpx 40rpx;\r\n\r\n\t\t\t.swiper {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 670rpx;\r\n\t\t\t\theight: 320rpx;\r\n\r\n\t\t\t\t.nav {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\twidth: 670rpx;\r\n\t\t\t\t\theight: 320rpx;\r\n\t\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.img {\r\n\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\twidth: 670rpx;\r\n\t\t\t\t\theight: 320rpx;\r\n\t\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.swiper_dots {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright: 0;\r\n\r\n\t\t\t\t.dots {\r\n\t\t\t\t\tmargin: 0 4rpx;\r\n\t\t\t\t\twidth: 14rpx;\r\n\t\t\t\t\theight: 4rpx;\r\n\t\t\t\t\tbackground: #D8D8D8;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.dots.active {\r\n\t\t\t\t\tbackground: #333333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t&_select{\r\n\t\t\tposition: sticky;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\tz-index: 10;\r\n\t\t\tbackground-color: #fff;\r\n\t\t}\r\n\t\t.container{\r\n\t\t\toverflow-y: unset;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./store_information.vue?vue&type=style&index=0&id=5b293664&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./store_information.vue?vue&type=style&index=0&id=5b293664&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341219\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}