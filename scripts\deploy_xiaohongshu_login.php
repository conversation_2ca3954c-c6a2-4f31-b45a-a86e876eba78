<?php
/**
 * 小红书登录功能部署脚本
 * 
 * 使用方法：
 * php scripts/deploy_xiaohongshu_login.php
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 获取项目根目录
$rootPath = dirname(__DIR__);

echo "=== 小红书登录功能部署脚本 ===\n";
echo "项目根目录: {$rootPath}\n\n";

// 1. 检查必要文件是否存在
echo "1. 检查必要文件...\n";
$requiredFiles = [
    'application/admin/command/Migration/add_user_source_field.sql',
    'application/admin/command/Migration/add_xhs_config.sql',
    'addons/xilutour/controller/XiaohongshuMini.php',
    'docs/xiaohongshu_login_test.md'
];

$missingFiles = [];
foreach ($requiredFiles as $file) {
    $fullPath = $rootPath . '/' . $file;
    if (!file_exists($fullPath)) {
        $missingFiles[] = $file;
        echo "  ❌ 缺少文件: {$file}\n";
    } else {
        echo "  ✅ 文件存在: {$file}\n";
    }
}

if (!empty($missingFiles)) {
    echo "\n❌ 部署失败：缺少必要文件\n";
    exit(1);
}

// 2. 检查数据库连接配置
echo "\n2. 检查数据库配置...\n";
$configFile = $rootPath . '/application/database.php';
if (!file_exists($configFile)) {
    echo "  ❌ 数据库配置文件不存在\n";
    exit(1);
}

// 3. 执行数据库迁移
echo "\n3. 执行数据库迁移...\n";
try {
    // 这里需要根据实际的数据库配置来执行SQL
    echo "  ℹ️  请手动执行以下SQL文件：\n";
    echo "     - application/admin/command/Migration/add_user_source_field.sql\n";
    echo "     - application/admin/command/Migration/add_xhs_config.sql\n";
    echo "  ⚠️  注意：请在执行前备份数据库\n";
} catch (Exception $e) {
    echo "  ❌ 数据库迁移失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 4. 检查权限
echo "\n4. 检查文件权限...\n";
$writableDirectories = [
    'runtime',
    'public/uploads',
    'application/admin/command/Migration'
];

foreach ($writableDirectories as $dir) {
    $fullPath = $rootPath . '/' . $dir;
    if (is_dir($fullPath) && is_writable($fullPath)) {
        echo "  ✅ 目录可写: {$dir}\n";
    } else {
        echo "  ⚠️  目录不可写: {$dir}\n";
    }
}

// 5. 生成配置检查清单
echo "\n5. 生成配置检查清单...\n";
$checklistContent = <<<EOT
# 小红书登录功能配置检查清单

## 数据库配置
- [ ] 执行 add_user_source_field.sql 迁移脚本
- [ ] 执行 add_xhs_config.sql 迁移脚本
- [ ] 验证 fa_user 表添加了 source 字段
- [ ] 验证 fa_xilutour_config 表添加了小红书配置

## 管理后台配置
- [ ] 登录管理后台
- [ ] 进入 系统管理 -> 旅游配置 -> 平台配置
- [ ] 配置小红书小程序信息：
  - [ ] 启用小红书登录
  - [ ] 填入正确的 APPID
  - [ ] 填入正确的 APPSECRET
  - [ ] 上传小程序头像和二维码（可选）

## UniApp配置
- [ ] 更新 manifest.json 中的小红书 appid
- [ ] 确保小红书开发者工具中项目配置正确
- [ ] 测试小红书小程序登录功能

## 功能测试
- [ ] 测试小红书登录API接口
- [ ] 测试获取手机号API接口
- [ ] 测试管理后台用户来源显示
- [ ] 测试UniApp登录流程

## 上线前检查
- [ ] 确保生产环境数据库已更新
- [ ] 确保小红书开放平台配置正确
- [ ] 确保API接口在生产环境正常工作
- [ ] 进行完整的登录流程测试

EOT;

$checklistFile = $rootPath . '/deployment_checklist.md';
file_put_contents($checklistFile, $checklistContent);
echo "  ✅ 配置检查清单已生成: deployment_checklist.md\n";

// 6. 显示后续步骤
echo "\n=== 部署完成 ===\n";
echo "✅ 文件部署完成\n";
echo "\n📋 后续步骤：\n";
echo "1. 手动执行数据库迁移脚本\n";
echo "2. 在管理后台配置小红书小程序信息\n";
echo "3. 更新UniApp的manifest.json配置\n";
echo "4. 按照 docs/xiaohongshu_login_test.md 进行功能测试\n";
echo "5. 参考 deployment_checklist.md 完成上线前检查\n";

echo "\n📖 相关文档：\n";
echo "- 测试文档: docs/xiaohongshu_login_test.md\n";
echo "- 检查清单: deployment_checklist.md\n";

echo "\n🎉 小红书登录功能部署脚本执行完成！\n";
?>
