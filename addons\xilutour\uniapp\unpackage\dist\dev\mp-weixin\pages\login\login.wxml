<view><view class="container"><view class="authorize_content"><view class="header"><image class="head" src="{{logo}}" mode></image></view><view class="title">请确认以下授权信息</view><view class="info">·获取您的信息(手机号等)</view><button class="btn1" hover-class="none" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">授权登录</button><button class="btn2" hover-class="none" data-event-opts="{{[['tap',[['closeLogin',['$event']]]]]}}" bindtap="__e">暂不登录</button></view></view><uni-popup class="vue-ref" vue-id="35a7246c-1" type="center" data-ref="agreePopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="agreePop tc" catchtouchmove="true"><view class="fs28 col-black content"><rich-text nodes="{{content}}"></rich-text></view><view class="foot-wrap flex-box m-hairline--top"><view data-event-opts="{{[['tap',[['agreePopClose',['$event']]]]]}}" class="btn flex-1 col-6 m-hairline--right" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['agreePopClose',['$event']]]]]}}" class="btn flex-1 col-red" bindtap="__e">确定</view></view></view></uni-popup></view>