<template>
	<view class="xilu">
		<view class="page-foot" @click="bindSave()">
			<view class="g-btn1">确定</view>
		</view>
		<view class="container">
			<view class="g_input_box flex-box mb30">
				<view class="fs30 col-5">姓名</view>
				<input class="flex-1 tr fs30 col-10" v-model="traveler.username" type="text" placeholder="请输入姓名" placeholder-class="col-10"/>
			</view>
			<view class="g_input_box flex-box mb30">
				<view class="fs30 col-5 flex-1">性别</view>
				<view class="flex-box flex-between flex-1 fs30 col-10">
						<view class="flex-box" v-for="(item,index) in genderList" :key="index" @click="bindGenderChange(item.id)">
							<image v-if="item.id==traveler.gender" class="icon_check" src="/static/icon/icon_checkon.png" mode="aspectFit"></image>
							<image v-else class="icon_check" src="/static/icon/icon_check.png" mode="aspectFit"></image>
							<view>{{item.name}}</view>
						</view>
				</view>
			</view>
			<view class="g_input_box flex-box mb30">
				<view class="fs30 col-5 flex-1">类型</view>
				<view class="flex-box flex-between flex-1 fs30 col-10">
						<view class="flex-box" v-for="(item,index) in adultTypeList" :key="index" @click="bindAdultTypeChange(item.id)">
							<image v-if="item.id==traveler.adult_type" class="icon_check" src="/static/icon/icon_checkon.png" mode="aspectFit"></image>
							<image v-else class="icon_check" src="/static/icon/icon_check.png" mode="aspectFit"></image>
							<view>{{item.name}}</view>
						</view>
				</view>
			</view>
			<view class="g_input_box flex-box mb30">
				<view class="fs30 col-5">身份证号</view>
				<input class="flex-1 tr fs30 col-10" v-model="traveler.idcard" maxlength="18" type="idcard" placeholder="请输入身份证号" placeholder-class="col-10"/>
			</view>
			<view class="g_input_box flex-box mb30">
				<view class="fs30 col-5">手机号码</view>
				<input class="flex-1 tr fs30 col-10" v-model="traveler.mobile" maxlength="11" type="number" placeholder="请输入手机号码" placeholder-class="col-10"/>
			</view>

		</view>
	</view>
</template>

<script>
// import { func } from "prop-types";
	var validate = require("../../xilu/validate.js");
	export default {
		data() {
			return {
				genderList:[{id:1,name:'男'},{id:2,name:'女'}],
				adultTypeList:[{id:1,name:'成人'},{id:2,name:'儿童'}],
				traveler:{
					username:'',
					idcard:'',
					mobile:'',
					gender: 1,
					adult_type:1,
				}
			};
		},
		onLoad() {
			let page = this;
			this.getOpenerEventChannel().on("editTransfor",function(data){
				page.traveler = data
			})
		},
		methods:{
			//性别
			bindGenderChange(id){
				this.traveler.gender = id;
			},
			//类型
			bindAdultTypeChange(id){
				this.traveler.adult_type = id;
			},
			
			bindSave(){
				let formData = this.traveler;
				var rule = [
					{name: 'username',nameChn: '姓名',rules: ['require'],errorMsg: {require: '请填写姓名'}},
					{name: 'idcard',nameChn: '身份证号',rules: ['require','length:18'],errorMsg: {require: '请填写身份证号',length:"身份证长度错误"}},
					{name: 'mobile',nameChn: '手机号',rules: ['require','length:11'],errorMsg: {require: '请填写手机号',length:"手机号错误"}},
				    
				];
				// 是否全部通过，返回Boolean
				if (!validate.check(formData, rule)) {
				    uni.showToast({
				        title: validate.getError()[0],
				        icon: 'none'
				    });
				    return;
				}
				this.$core.post({url: 'xilutour.traveler/set_traveler',data: formData,success: ret => {
				    this.getOpenerEventChannel().emit("setSuccess",{})
				    uni.navigateBack({});
				    uni.showToast({
				        title: '提交成功',
				        icon: 'none'
				    });
				}})
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		.page-foot {
			padding: 20rpx 65rpx;
			background-color: #FFF;
		}

		.container {
			padding: 30rpx 40rpx 160rpx !important;
		}

	}


</style>