{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"enablePullDownRefresh":true
			}
		},
		{
			"path" : "pages/travel/travel",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : true,
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/landscape/landscape",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : true,
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/personal_center/personal_center",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : true,
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/login/login",
			"style" :
			{
				"navigationBarTitleText" : "登录",
				"enablePullDownRefresh" : true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/test/xhs-test",
			"style": {
				"navigationBarTitleText": "小红书登录测试",
				"navigationBarBackgroundColor": "#ff2442",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path" : "pages/landscape_detail/landscape_detail",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : false,
				"navigationStyle": "custom"
			}
		},{
			"path" : "pages/filtrate/filtrate",
			"style" : 
			{
				"navigationBarTitleText" : "筛选",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/choose_traveler/choose_traveler",
			"style" : 
			{
				"navigationBarTitleText" : "选择出行人",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/change_city/change_city",
			"style" : 
			{
				"navigationBarTitleText" : "更换城市",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/new_traveler/new_traveler",
			"style" : 
			{
				"navigationBarTitleText" : "新建出行人",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/basic_information/basic_information",
			"style" : 
			{
				"navigationBarTitleText" : "基本信息",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/invite_friends/invite_friends",
			"style" : 
			{
				"navigationBarTitleText" : "邀请好友",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/problem/problem",
			"style" : 
			{
				"navigationBarTitleText" : "常见问题",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/problem_detail/problem_detail",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/singlepage/singlepage",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : false
			}
		},
		
		{
			"path" : "pages/my_message/my_message",
			"style" : 
			{
				"navigationBarTitleText" : "我的消息",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/withdrawal_record/withdrawal_record",
			"style" : 
			{
				"navigationBarTitleText" : "提现记录",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/withdrawal/withdrawal",
			"style" : 
			{
				"navigationBarTitleText" : "申请提现",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/my_commission/my_commission",
			"style" : 
			{
				"navigationBarTitleText" : "我的佣金",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/write_off_order/write_off_order",
			"style" : 
			{
				"navigationBarTitleText" : "核销订单",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/write_off_successfully/write_off_successfully",
			"style" : 
			{
				"navigationBarTitleText" : "核销成功",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/write_off_failed/write_off_failed",
			"style" : 
			{
				"navigationBarTitleText" : "核销失败",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/store_information/store_information",
			"style" : 
			{
				"navigationBarTitleText" : "门店信息",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/write_off_confirmation/write_off_confirmation",
			"style" : 
			{
				"navigationBarTitleText" : "核销确认",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/select_specification/select_specification",
			"style" : 
			{
				"navigationBarTitleText" : "选择规格",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/travel_pay/travel_pay",
			"style" : 
			{
				"navigationBarTitleText" : "立即支付",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/landscape_pay/landscape_pay",
			"style" : 
			{
				"navigationBarTitleText" : "立即支付",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/landscape_order/landscape_order",
			"style" : 
			{
				"navigationBarTitleText" : "景点订单",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/travel_order/travel_order",
			"style" : 
			{
				"navigationBarTitleText" : "旅游订单",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/landscape_order_detail/landscape_order_detail",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/travel_order_detail/travel_order_detail",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/apply_refund/apply_refund",
			"style" : 
			{
				"navigationBarTitleText" : "申请退款",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/coupon/coupon",
			"style" : 
			{
				"navigationBarTitleText" : "优惠券",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/my_collection/my_collection",
			"style" : 
			{
				"navigationBarTitleText" : "我的收藏",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/more_landscape/more_landscape",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : false,
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/popular_travel/popular_travel",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : false,
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/travel_detail/travel_detail",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : false,
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/search/search",
			"style" : 
			{
				"navigationBarTitleText" : "",
				"enablePullDownRefresh" : false,
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/traveler_list/traveler_list",
			"style" : 
			{
				"navigationBarTitleText" : "出行人",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/immediate_evaluation/immediate_evaluation",
			"style" : 
			{
				"navigationBarTitleText" : "立即评价",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/tour_evaluation/tour_evaluation",
			"style" : 
			{
				"navigationBarTitleText" : "线路评价",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/article_list/article_list",
			"style" : 
			{
				"navigationBarTitleText" : "攻略",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/article_detail/article_detail",
			"style" : 
			{
				"navigationBarTitleText" : "攻略详情",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/article_list1/article_list1",
			"style" : 
			{
				"navigationBarTitleText" : "攻略",
				"enablePullDownRefresh" : false
			}
		}
	],
	"tabBar": {
		"color": "#898989",
		"selectedColor": "#05B9AE",
		"backgroundColor":"#FFFFFF",
		"borderStyle": "black",
		"list": [{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "/static/icon/icon_foot1.png",
				"selectedIconPath": "/static/icon/icon_foot1_on.png"
			},
			{
				"pagePath": "pages/travel/travel",
				"text": "路线",
				"iconPath": "/static/icon/icon_foot2.png",
				"selectedIconPath": "/static/icon/icon_foot2_on.png"
			},
			{
				"pagePath": "pages/landscape/landscape",
				"text": "景点",
				"iconPath": "/static/icon/icon_foot3.png",
				"selectedIconPath": "/static/icon/icon_foot3_on.png"
			},
			{
				"pagePath": "pages/personal_center/personal_center",
				"text": "我的",
				"iconPath": "/static/icon/icon_foot4.png",
				"selectedIconPath": "/static/icon/icon_foot4_on.png"
			}
		]
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#F8F8F8"
	},
	"uniIdRouter": {}
}
