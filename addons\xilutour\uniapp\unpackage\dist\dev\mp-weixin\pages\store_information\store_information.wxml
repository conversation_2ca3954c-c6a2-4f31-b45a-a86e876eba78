<view class="xilu data-v-5b293664"><view class="container data-v-5b293664" style="padding-bottom:30rpx;"><view class="pr data-v-5b293664"><view class="xilu_select data-v-5b293664"><view class="g_tab data-v-5b293664"><view data-event-opts="{{[['tap',[['tabClick',[-1]]]]]}}" class="{{['item','data-v-5b293664',(query.category_id==-1)?'active':'']}}" bindtap="__e">全部</view><block wx:for="{{categoryList}}" wx:for-item="item" wx:for-index="index"><view data-event-opts="{{[['tap',[['tabClick',['$0'],[[['categoryList','',index,'id']]]]]]]}}" class="{{['item','data-v-5b293664',(query.category_id==item.id)?'active':'']}}" bindtap="__e">{{item.name}}</view></block></view></view><view class="p40 data-v-5b293664"><view class="g_landscape_list data-v-5b293664"><scenery-list vue-id="09fe1722-1" sceneryList="{{sceneryList}}" class="data-v-5b293664" bind:__l="__l"></scenery-list><view class="g-btn3-wrap data-v-5b293664"><view data-event-opts="{{[['tap',[['fetch',['$event']]]]]}}" class="g-btn3 data-v-5b293664" bindtap="__e">{{sceneryListMore.text}}</view></view></view></view></view></view></view>