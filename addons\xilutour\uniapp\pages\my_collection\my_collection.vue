<template>
	<view class="xilu">
		<view class="page-head bg-white">
			<view class="g_tab">
				<view class="item" :class="{'active': tabIdx == 1}" @click="tabClick(1)">景点</view>
				<view class="item" :class="{'active': tabIdx == 2}" @click="tabClick(2)">路线</view>
			</view>
		</view>

		<view class="container">
			<view class="g_landscape_list" v-if="tabIdx == 1">
				<scenery-list :sceneryList="sceneryList"></scenery-list>
				<view class="g-btn3-wrap">
					<view class="g-btn3" @click="fetchScenery">{{sceneryListMore.text}}</view>
				</view>
			</view>

			<view class="g_travel_list" v-if="tabIdx == 2">
				<tour-list :tourList="tourList"></tour-list>
				
				<view class="g-btn3-wrap">
					<view class="g-btn3" @click="fetch">{{tourListMore.text}}</view>
				</view>
			</view>

		</view>

	</view>
</template>

<script>
	import sceneryList from '@/components/scenery-list/scenery-list.vue';
	export default {
		components: {
			sceneryList
		},
		data() {
			return {
				tabIdx: 1,
				sceneryList:[],
				sceneryListMore:{page:1},
				
				tourList:[],
				tourListMore:{page:1}
			};
		},
		onLoad() {
			this.fetchScenery();
			this.fetchTour();
		},
		onReachBottom() {
			if(this.tabIdx == 1){
				this.fetchScenery();
			}else{
				this.fetchTour();
			}
		},
		methods: {
			tabClick(i) {
				this.tabIdx = i;
				// this.refresh();
			},
			
			refresh(){
				if(this.tabIdx == 1){
					this.sceneryList = [];
					this.sceneryListMore = {page:1};
					this.fetchScenery();
				}else{
					this.tourList = [];
					this.tourListMore = {page:1};
					this.fetchTour();
				}
				
			},
			fetchScenery(){
				this.$util.fetch(this, 'xilutour.scenery/collection_list', {pagesize:10}, 'sceneryListMore', 'sceneryList', 'data', data=>{
				  
				})
			},
			fetchTour(){
				this.$util.fetch(this, 'xilutour.tour/collection_list', {pagesize:10}, 'tourListMore', 'tourList', 'data', data=>{
				  
				})
			},
		}
	}
</script>

<style lang="less" scoped>


	.xilu {
		.container {
			padding: 106rpx 40rpx 40rpx;
		}
	}
</style>