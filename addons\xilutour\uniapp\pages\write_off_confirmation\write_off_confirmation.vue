<template>
	<view class="xilu">
		<view class="container">
			<view class="xilu_hexiao_box">
				<image src="../../static/icon/icon_hexiao.png" mode="aspectFill" class="bg"></image>
				<view class="inner">
					<view class="p30">
						<image :src="order.order.order_project.thumb_image" mode="aspectFill" class="img"></image>
						<view class="flex-box pb30 mb30">
							<view class="flex-1 col-6">项目名称</view>
							<view class="col-3">{{order.order.order_project.project_name}}</view>
						</view>
						<view class="flex-box pb30 mb30">
							<view class="flex-1 col-6">当前状态</view>
							<view style="color: red;">{{order.qrcode.verifier_status==0?'未核销':'已核销'}}</view>
						</view>
						<view class="flex-box pb30 mb30">
							<view class="flex-1 col-6">订单编号</view>
							<view class="col-3">{{order.order.order_no}}</view>
						</view>
					</view>
					<view class="btn" @click="bindConfirm()">确认核销</view>
				</view>
			</view>
			
			<view class="cancel" @click="bindCancel()">
				取消核销
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				token: '',
				order:{
					order:{
						order_project:{
							thumb_image:''
						}
					},
					qrcode:{
						verifier_status: 0
					}
					
				}
			};
		},
		onLoad(options) {
			this.token = options.scene;
			this.fetchScenery();
		},
		methods:{
			fetchScenery(){
				this.$core.post({url:'xilutour.scenery_order/order_confirm',data:{token:this.token},success:(ret)=>{
					this.order = ret.data;
				},fail:(ret)=>{
					uni.showModal({
						title:'提示',
						content: ret.msg,
						success() {
							if(getCurrentPages().length == 1){
								uni.switchTab({
									url: '/pages/index/index'
								})
							}else{
								uni.navigateBack({})
							}
						}
					})
					return false;
				}
				});
			},
			//确认核销
			bindConfirm(){
				let page = this;
				uni.showModal({
					title: '提示',
					content: '确认核销？',
					success(res) {
						if(res.confirm){
							page.$core.post({url:'xilutour.scenery_order/verifier_order',data:{token:page.token},success:(ret)=>{
								uni.redirectTo({
									url: '/pages/write_off_successfully/write_off_successfully'
								})
							},fail:(ret)=>{
								uni.redirectTo({
									url: '/pages/write_off_failed/write_off_failed?msg='+ret.msg
								})
								return false;
							}
							});
						}
					}
				})
				
			},
			//取消
			bindCancel(){
				if(getCurrentPages().length == 1){
					uni.switchTab({
						url: '/pages/index/index'
					})
				}else{
					uni.navigateBack({})
				}
			},
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		.cancel{
			padding: 30rpx 0;
			margin: 20rpx 0 0;
			font-size: 34rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #898989;
			line-height: 36rpx;
			text-align: center;
		}
		&_hexiao_box {
			position: relative;
			width: 690rpx;
			height: 1198rpx;

			.bg {
				display: block;
				width: 690rpx;
				height: 1198rpx;
			}

			.inner {
				position: absolute;
				top: 0;
				right: 0;
				left: 0;
				bottom: 0;
				font-size: 30rpx;
				line-height: 32rpx;

				.img {
					margin: 0 0 78rpx;
					display: block;
					width: 630rpx;
					height: 630rpx;
					border-radius: 20rpx;
				}
				.btn{
					position: absolute;
					bottom: 0;
					right: 0;
					left: 0;
					width: 690rpx;
					height: 110rpx;
					background: #05B9AE;
					border-radius: 0 0 20rpx 20rpx;
					font-size: 34rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 110rpx;
					text-align: center;
				}
			}
		}

		.container {
			padding: 30rpx;
			background: #F7F9FB;
		}
	}
</style>