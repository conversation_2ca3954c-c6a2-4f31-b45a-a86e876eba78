.xilu_swiper.data-v-2f7c86f4 {
  position: relative;
  padding: 30rpx 40rpx 40rpx;
}
.xilu_swiper .swiper.data-v-2f7c86f4 {
  display: block;
  width: 670rpx;
  height: 320rpx;
}
.xilu_swiper .swiper .nav.data-v-2f7c86f4 {
  position: relative;
  width: 670rpx;
  height: 320rpx;
  border-radius: 15rpx;
}
.xilu_swiper .swiper .img.data-v-2f7c86f4 {
  margin: 0 auto;
  display: block;
  width: 670rpx;
  height: 320rpx;
  border-radius: 15rpx;
}
.xilu_swiper .swiper_dots.data-v-2f7c86f4 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.xilu_swiper .swiper_dots .dots.data-v-2f7c86f4 {
  margin: 0 4rpx;
  width: 14rpx;
  height: 4rpx;
  background: #D8D8D8;
}
.xilu_swiper .swiper_dots .dots.active.data-v-2f7c86f4 {
  background: #333333;
}
.xilu_select.data-v-2f7c86f4 {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: #fff;
}
.xilu .container.data-v-2f7c86f4 {
  overflow-y: unset;
}

