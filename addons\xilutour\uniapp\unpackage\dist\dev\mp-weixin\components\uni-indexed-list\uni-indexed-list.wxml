<view class="uni-indexed-list data-v-acb53438 vue-ref" id="list" data-ref="list"><scroll-view class="uni-indexed-list__scroll data-v-acb53438" scroll-into-view="{{scrollViewId}}" scroll-y="{{true}}"><block wx:for="{{lists}}" wx:for-item="list" wx:for-index="idx" wx:key="idx"><view id="{{'uni-indexed-list-'+idx}}" class="data-v-acb53438"><indexed-list-item vue-id="{{'e9f050bc-1-'+idx}}" list="{{list}}" loaded="{{loaded}}" idx="{{idx}}" showSelect="{{showSelect}}" data-event-opts="{{[['^itemClick',[['onClick']]]]}}" bind:itemClick="__e" class="data-v-acb53438" bind:__l="__l"></indexed-list-item></view></block></scroll-view><view data-event-opts="{{[['touchstart',[['touchStart',['$event']]]],['touchmove',[['touchMove',['$event']]]],['touchend',[['touchEnd',['$event']]]],['mousedown',[['mousedown',['$event']]]],['mousemove',[['mousemove',['$event']]]],['mouseleave',[['mouseleave',['$event']]]]]}}" class="uni-indexed-list__menu data-v-acb53438" bindtouchstart="__e" catchtouchmove="__e" bindtouchend="__e" catchmousedown="__e" catchmousemove="__e" catchmouseleave="__e"><block wx:for="{{lists}}" wx:for-item="list" wx:for-index="key" wx:key="key"><view class="{{['uni-indexed-list__menu-item','data-v-acb53438',touchmoveIndex==key?'uni-indexed-list__menu--active':'']}}"><text class="{{['uni-indexed-list__menu-text','data-v-acb53438',touchmoveIndex==key?'uni-indexed-list__menu-text--active':'']}}">{{list.key}}</text></view></block></view><block wx:if="{{touchmove}}"><view class="uni-indexed-list__alert-wrapper data-v-acb53438"><text class="uni-indexed-list__alert data-v-acb53438">{{lists[touchmoveIndex].key}}</text></view></block></view>