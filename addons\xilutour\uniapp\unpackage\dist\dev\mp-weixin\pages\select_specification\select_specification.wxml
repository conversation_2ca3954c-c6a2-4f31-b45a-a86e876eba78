<view class="xilu data-v-92a7c9f4"><view data-event-opts="{{[['tap',[['bindNext',['$event']]]]]}}" class="page-foot data-v-92a7c9f4" bindtap="__e"><view class="g-btn1 data-v-92a7c9f4">下一步</view></view><view class="container data-v-92a7c9f4"><view class="xilu_travel flex-box data-v-92a7c9f4"><image class="img mr30 data-v-92a7c9f4" src="{{tour.thumb_image_text}}" mode="aspectFill"></image><view class="flex-1 data-v-92a7c9f4"><view class="fs36 col-10 mb40 data-v-92a7c9f4">{{tour.name}}</view><block wx:if="{{chooseDate}}"><view class="fs30 col-5 mb20 data-v-92a7c9f4">{{"已选："+chooseDate.appoint_date_text}}</view></block><block wx:else><view class="fs30 col-5 mb20 data-v-92a7c9f4">亲选择行程</view></block><view class="fs26 col-89 data-v-92a7c9f4">{{"报名截止时间出发前"+tour.enroll_days+"天"}}</view></view></view><view class="xilu_title data-v-92a7c9f4">出行人数</view><view class="xilu_number flex-box data-v-92a7c9f4"><view class="mr40 data-v-92a7c9f4">成人</view><block wx:if="{{chooseDate}}"><view class="flex-1 data-v-92a7c9f4">{{"¥"+chooseDate.salesprice}}</view></block><image src="/static/icon/icon_jian.png" mode="aspectFit" data-event-opts="{{[['tap',[['bindAdultChangeCount',['minus']]]]]}}" bindtap="__e" class="data-v-92a7c9f4"></image><view class="num data-v-92a7c9f4">{{buyAdultCount}}</view><image src="/static/icon/icon_jia.png" mode="aspectFit" data-event-opts="{{[['tap',[['bindAdultChangeCount',['plus']]]]]}}" bindtap="__e" class="data-v-92a7c9f4"></image></view><view class="xilu_number flex-box data-v-92a7c9f4"><view class="mr40 data-v-92a7c9f4">儿童</view><block wx:if="{{chooseDate}}"><view class="flex-1 data-v-92a7c9f4">{{"¥"+chooseDate.child_salesprice}}</view></block><image src="/static/icon/icon_jian.png" mode="aspectFit" data-event-opts="{{[['tap',[['bindChildChangeCount',['minus']]]]]}}" bindtap="__e" class="data-v-92a7c9f4"></image><view class="num data-v-92a7c9f4">{{buyChildCount}}</view><image src="/static/icon/icon_jia.png" mode="aspectFit" data-event-opts="{{[['tap',[['bindChildChangeCount',['plus']]]]]}}" bindtap="__e" class="data-v-92a7c9f4"></image></view><view class="xilu_title data-v-92a7c9f4">选择出发日期</view><view class="xilu_month data-v-92a7c9f4"><block wx:for="{{tourDates}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['bindMonthChange',[index]]]]]}}" class="{{['item','data-v-92a7c9f4',(tourDatesIndex==index)?'active':'']}}" bindtap="__e"><view class="data-v-92a7c9f4"><text class="fs36 data-v-92a7c9f4">{{item.month}}</text><text class="fs24 data-v-92a7c9f4">月</text></view><view class="fs24 data-v-92a7c9f4">{{item.year}}</view></view></block></view><view class="xilu_day flex-box flex-wrap data-v-92a7c9f4"><block wx:for="{{dateList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['bindArrangementChange',['$0'],[[['dateList','',index]]]]]]]}}" class="{{['item','data-v-92a7c9f4',item.sku_count<=0?'disabled':chooseDate&&chooseDate.id==item.id?'active':'']}}" bindtap="__e"><view class="mb10 data-v-92a7c9f4">{{"剩"+item.sku_count+"名"}}</view><text class="data-v-92a7c9f4">{{item.appoint_date_text}}</text></view></block></view></view></view>