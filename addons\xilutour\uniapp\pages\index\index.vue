<template>
	<view class="xilu">
		<view class="container" style="padding-bottom: 30rpx;">

			<image class="m-backdrop" src="/static/icon/icon_bg1.png" mode="widthFix"></image>
			<view class="m-header">
				<image class="m-backdrop" src="/static/icon/icon_bg1.png" mode="widthFix"></image>
				<view class="g-custom-nav flex-box plr30"
					:style="{ paddingTop: statusBarHeight + 'px', height: 'calc(90rpx + ' + statusBarHeight + 'px)' }">
					<view class="message" :class="{active:messageCount>0}" @click="bindMessage()">
						<image src="/static/icon/icon_message.png" mode="aspectFit"></image>
						<view>消息</view>
					</view>
					<view class="search_box flex-box">
						<view class="addr m-ellipsis" @click="bindCityChange()">{{currentCity?currentCity.name:''}}</view>
						<image class="icon_arrow" src="/static/icon/icon_arrow.png"></image>
						<view class="line"></view>
						<image class="icon_search" src="/static/icon/icon_search.png"></image>
						<input @click="bindSearch()" disabled="true" class="input flex-1" type="text" placeholder="出发城市/目的地" placeholder-class="cola" />
					</view>
				</view>
			</view>
			<view class="pr" :style="{ paddingTop: 'calc(90rpx + ' + statusBarHeight + 'px)' }">
				<view class="xilu_swiper">
					<swiper class="swiper" :current="swiperCurrent" circular previous-margin="38px" next-margin="38px" @change="swiperChange">
						<swiper-item v-for="(item,index) in bannerList" :key="index">
							<view class="nav" :class="{'scale': swiperCurrent !==index}" @click="bannerjump(item.minapp_url)">
								<image :src="item.thumb_image_text" mode="aspectFill" class="img"></image>
							</view>
						</swiper-item>
					</swiper>
					<view class="swiper_dots flex-box flex-center">
						<view class="dots" v-for="(item,index) in bannerList" :key="index" :class="{'active': swiperCurrent == index}">
						</view>
					</view>
				</view>

				<view class="plr40">
					<view class="xilu_menu flex-box flex-between">
						<view class="item" @click="navigation(index)" v-for="(item,index) in navigationList" :key="index">
							<image :src="item.icon_text" mode="aspectFill"></image>
							<view class="fs30 col-3">{{item.name}}</view>
						</view>
					</view>

					<navigator class="xilu_banner" :url="adList[0].minapp_url" hover-class="none" v-if="adList.length>0">
						<image :src="adList[0].thumb_image_text" mode="aspectFill"></image>
					</navigator>

					<view class="xilu_title flex-box">
						<view class="flex-1 title">景点推荐</view>
						<navigator class="more" url="/pages/more_landscape/more_landscape" hover-class="none">更多+</navigator>
					</view>

					<view class="xilu_landscape">
						<navigator class="item" :url="'/pages/landscape_detail/landscape_detail?id='+item.id" hover-class="none" v-for="(item,index) in sceneryList">
							<image :src="item.thumb_image_text" mode="aspectFill">
							</image>
							<view class="name m-ellipsis mb20">{{item.name}}</view>
							<view class="col-price" v-if="item.project">
								<text class="fs30">¥</text>
								<text class="fs40">{{item.project.salesprice}}</text>
								<text class="fs30">起</text>
							</view>
						</navigator>
					</view>

					<view class="xilu_title flex-box">
						<view class="flex-1 title">线路推荐</view>
						<navigator class="more" url="/pages/popular_travel/popular_travel" hover-class="none">更多+</navigator>
					</view>

					<view class="g_travel_list">
						<tour-list :tourList="tourList"></tour-list>
						<view class="nothing" v-if="tourListMore.nothing">
							<image src="/static/icon/icon_nothing.png" mode="aspectFit"></image>
							<text>暂无内容</text>
						</view>
						<view class="g-btn3-wrap" v-else>
							<view class="g-btn3" @click="fetchTourList">{{tourListMore.text}}</view>
						</view>
					</view>

				</view>

			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import tourList from '@/components/tour-list/tour-list.vue';
	export default {
		components: {
			tourList
		},
		data() {
			return {
				statusBarHeight: 20,
				messageCount: 0,
				bannerList:[],
				adList:[],
				swiperCurrent: 0,
				navigationList:[],
				sceneryList:[],
				tourList:[],
				tourListMore:{page:1},
				currentCity: null,
			};
		},
		onLoad() {
			this.statusBarHeight = getApp().globalData.statusBarHeight;
			this.refreshPage();
			let page = this;
			this.currentCity = this.$core.getCurrentCity();
			uni.$on(app.globalData.Event.CurrentCityChange, function(currentCity) {
				page.currentCity = currentCity;
				page.refreshPage();
			})
		},
		onShow() {
			if(this.$core.getUserinfo()){
				this.$util.getMessageCount(false).then(count=>{
					this.messageCount = count
				})
			}
		},
		onReachBottom() {
			this.fetchTourList()
		},
		onPullDownRefresh() {
			this.refreshPage();
		},
		onShareAppMessage() {
			
		},
		onShareTimeline() {
			
		},
		onUnload() {
			uni.$off(app.globalData.Event.CurrentCityChange,this);
		},
		methods: {
			//更换城市
			bindCityChange(){
				uni.navigateTo({
				    url: '/pages/change_city/change_city'
				})
			},
			//搜索
			search() {
			    uni.navigateTo({
			        url: '/pages/search_result/search_result'
			    })
			},
			swiperChange(e) {
				this.swiperCurrent = e.detail.current
			},
			bindSearch(){
				uni.navigateTo({
					url: '/pages/search/search'
				})
			},
			refreshPage(){
				//轮播
				this.$core.get({url:'xilutour.banner/index',data:{group:'index'},loading:false,success:(ret)=>{
					this.bannerList = ret.data;
				 }});
				 
				 //中间图
				 this.$core.get({url:'xilutour.banner/index',data:{group:'index2'},loading:false,success:(ret)=>{
				 	this.adList = ret.data;
				  }});
				 
				//中间金刚区
				this.$core.get({url: 'xilutour.common/navication',data:{},loading:false,success:(ret)=>{
					this.navigationList = ret.data;
				}})
				
				//热门景点
				this.$core.get({url: 'xilutour.scenery/hot_list',data:{},loading:false,success:(ret)=>{
					this.sceneryList = ret.data;
				}})
				//线路推荐
				this.tourList = [];
				this.tourListMore = {page: 1};
				this.fetchTourList();
				uni.stopPullDownRefresh();
			},
			fetchTourList(){
				this.$util.fetch(this, 'xilutour.tour/recommend_list', {pagesize:10}, 'tourListMore', 'tourList', 'data', data=>{
				  
				})
			},
			//轮播图
			bannerjump(url){
				if(url.trim() == ''){
					return '';
				}
				uni.navigateTo({
					url: url
				})
			},
			//金刚区
			navigation(index){
				let item = this.navigationList[index];
				let url = ''
				if(item.module_type == 3){
					url = '/pages/article_list/article_list'
				}else if(item.module_type==2){
					url = '/pages/more_landscape/more_landscape';
				}else{
					url = '/pages/popular_travel/popular_travel';
				}
				if(item.module_type != 3){
					if(item.type==1){
						//标签
						url += '?tag_id='+item.type_value;
					}else if(item.type == 2){
						//分类
						url += '?category_id='+item.type_value
					}
				}
				if(url){
					uni.navigateTo({
						url: url
					})
				}
			},
			bindMessage(){
				uni.navigateTo({
					url: '/pages/my_message/my_message'
				})
			}
			
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		&_swiper {
			position: relative;
			padding: 30rpx 0 50rpx;

			.swiper {
				display: block;
				width: 750rpx;
				height: 320rpx;

				.nav {
					position: relative;
					width: 600rpx;
					height: 320rpx;
					border-radius: 15rpx;
				}

				.nav.scale {
					transform: scale(0.9);
				}

				.img {
					margin: 0 auto;
					display: block;
					width: 600rpx;
					height: 320rpx;
					border-radius: 15rpx;
				}
			}

			.swiper_dots {
				position: absolute;
				bottom: 30rpx;
				left: 0;
				right: 0;

				.dots {
					margin: 0 4rpx;
					width: 14rpx;
					height: 4rpx;
					background: #D8D8D8;
				}

				.dots.active {
					background: #333333;
				}
			}
		}

		&_menu {
			margin: 0 0 25rpx;

			image {
				margin: 0 auto 30rpx;
				display: block;
				width: 100rpx;
				height: 100rpx;
				border-radius: 32rpx;
			}
		}

		&_banner {
			margin: 0 0 25rpx;

			image {
				display: block;
				width: 670rpx;
				height: 198rpx;
			}
		}

		&_title {
			margin: 0 0 40rpx;

			.title {
				font-size: 40rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				color: #101010;
				line-height: 42rpx;
			}

			.more {
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #898989;
				line-height: 32rpx;
			}
		}

		&_landscape {
			white-space: nowrap;
			overflow-x: scroll;
			overflow-y: hidden;
			font-size: 0;
			margin: 0 0 60rpx;

			.item {
				margin: 0 20rpx 0 0;
				display: inline-block;
				width: 290rpx;
				vertical-align: top;

				image {
					margin: 0 0 20rpx;
					display: block;
					width: 290rpx;
					height: 330rpx;
					border-radius: 20rpx;
				}

				.name {
					font-size: 36rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					color: #101010;
					line-height: 38rpx;
				}
			}
		}
	}
</style>