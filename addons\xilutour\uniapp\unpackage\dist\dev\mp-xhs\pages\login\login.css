
.head {
	margin-right: 26rpx;
	width: 128rpx;
	height: 128rpx;
	border: 4rpx solid var(--normal);
	border-radius: 50%;
}
.authorize_content {
	padding: 120rpx 75rpx;
	box-sizing: border-box;
}
.authorize_content .header {
	width: 201rpx;
	height: 201rpx;
	/* border: 6rpx solid #fff; */
	/* box-shadow: 0px 3rpx 8rpx 0px rgba(213, 213, 213, 0.4); */
	border-radius: 50%;
	overflow: hidden;
	margin: 0 auto;
}
.authorize_content .header image {
	width: 100%;
	height: 100%;
	display: block;
}
.authorize_content .title {
	font-size: 32rpx;
	color: #333333;
	padding-top: 50rpx;
	margin-top: 40rpx;
	/* border-top: 1rpx solid #EDEDED; */
	text-align: center;
}
.authorize_content .info {
	font-size: 28rpx;
	color: #999999;
	padding-top: 30rpx;
	padding-bottom: 70rpx;
	text-align: center;
}
.authorize_content button {
	width: 600rpx;
	margin-bottom: 40rpx;
}
.container {
	min-height: calc(100vh - env(safe-area-inset-top) - 0px - 0px);
	overflow: hidden;
	font-size: 28rpx;
	box-sizing: border-box;
}
.btn1 {
	height: 80rpx;
	border-radius: 50rpx;
	text-align: center;
	background-color: var(--normal);
	color: #FFFFFF;
	font-size: 28rpx;
	line-height: 80rpx;
}
.btn2 {
	background-color: #D8D8D8;
	color: #FFFFFF;
	height: 80rpx;
	border-radius: 50rpx;
	text-align: center;
	font-size: 28rpx;
	line-height: 80rpx;
}
.user-rules {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 80rpx;
}
.primary-color {
	color: #0896e0;
}
.u-checkbox__label {
	color: #999999 !important;
}
.rules {
	margin-top: 20rpx;
}
.login-wrap {
	padding: 215rpx 30rpx 30rpx;
}
.login-wrap .title {
	margin: 0 0 125rpx 50rpx;
	font-size: 50rpx;
	font-weight: bold;
	color: #ffffff;
	line-height: 56rpx;
}
.login-box {
	padding: 90rpx 50rpx 50rpx;
	width: 690rpx;
	height: 645rpx;
	background: #ffffff;
	box-shadow: 0 6rpx 29rpx 1rpx rgba(45, 45, 45, 0.14);
	border-radius: 20rpx;
}
.login-box .input-box {
	padding: 0 50rpx;
	height: 80rpx;
	border: 1rpx solid #cdcdcd;
	border-radius: 40rpx;
}
.login-box .input-box + .input-box {
	margin-top: 50rpx;
}
.login-box .input-box image {
	display: block;
	width: 30rpx;
	height: 44rpx;
}
.login-box .btn-login {
	margin: 125rpx auto 40rpx;
	width: 590rpx;
	height: 92rpx;
	background: linear-gradient(180deg, #ed1f34, #f24682);
	border-radius: 10rpx;
	font-size: 32rpx;
	font-weight: 500;
	color: #ffffff;
	line-height: 92rpx;
	text-align: center;
}
.foot-row image {
	margin: 0 20rpx 0 0;
	display: block;
	width: 38rpx;
	height: 38rpx;
	border-radius: 50%;
}
.agreePop {
	padding: 30rpx 0 0;
	width: 520rpx;
	background: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
}
.agreePop .content {
	margin: 0 auto 25rpx;
	padding: 0 30rpx;
	max-height: 500rpx;
	overflow-y: scroll;
}
.agreePop .btn {
	height: 90rpx;
	line-height: 90rpx;
}

