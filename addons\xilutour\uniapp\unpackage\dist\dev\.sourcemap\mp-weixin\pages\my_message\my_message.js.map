{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/my_message/my_message.vue?b34e", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/my_message/my_message.vue?fd24", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/my_message/my_message.vue?6780", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/my_message/my_message.vue?246c", "uni-app:///pages/my_message/my_message.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/my_message/my_message.vue?d81d", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/my_message/my_message.vue?6572"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "tabIdx", "systemList", "systemListMore", "page", "personalList", "personalListMore", "onLoad", "onReachBottom", "methods", "tabClick", "fetchSystem", "pagesize", "fetchPersonal", "bindRead", "uni", "url", "message_id", "loading", "success", "fail", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0Cv1B;EACAC;IACA;MACAC;MAEAC;MACAC;QAAAC;MAAA;MAEAC;MACAC;QAAAF;MAAA;IACA;EACA;EACAG;IACA;IACA;EACA;EACAC;IACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;QAAAC;MAAA,4DAEA;IACA;IACAC;MACA;QAAAD;MAAA,gEAEA;IACA;IACA;IACAE;MAAA;MACA;MACA;MACA;QACAC;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;MACA;QACA;MACA;MACA;QAAAA;QAAAhB;UAAAiB;QAAA;QAAAC;QAAAC;UACA;UACA;QACA;QAAAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxGA;AAAA;AAAA;AAAA;AAAshD,CAAgB,u5CAAG,EAAC,C;;;;;;;;;;;ACA1iD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my_message/my_message.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my_message/my_message.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my_message.vue?vue&type=template&id=19b51ec6&scoped=true&\"\nvar renderjs\nimport script from \"./my_message.vue?vue&type=script&lang=js&\"\nexport * from \"./my_message.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my_message.vue?vue&type=style&index=0&id=19b51ec6&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"19b51ec6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my_message/my_message.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my_message.vue?vue&type=template&id=19b51ec6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my_message.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my_message.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"xilu\">\r\n\t\t<view class=\"page-head\">\r\n\t\t\t<view class=\"g_tab\">\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 1}\" @click=\"tabClick(1)\">系统消息</view>\r\n\t\t\t\t<view class=\"item\" :class=\"{'active': tabIdx == 2}\" @click=\"tabClick(2)\">我的消息</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"container\">\n\t\t\t<view v-if=\"tabIdx == 1\">\r\n\t\t\t\t<view class=\"xilu_message\" v-for=\"(item,index) in systemList\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"flex-box\">\r\n\t\t\t\t\t\t<view class=\"fs30 col-10 flex-1 fwb\">{{item.title}}</view>\r\n\t\t\t\t\t\t<view class=\"fs28 col-5\">{{item.createtime_text}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mt20\"><rich-text :nodes=\"item.content\">{{item.content}}</rich-text></view>\r\n\t\t\t\t</view>\n\t\t\t\t<view class=\"g-btn3-wrap\">\n\t\t\t\t\t<view class=\"g-btn3\" @click=\"fetchSystem()\">{{systemListMore.text}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\r\n\t\t\t<view v-if=\"tabIdx == 2\">\r\n\t\t\t\t<view class=\"xilu_message\" @click=\"bindRead(index)\" :class=\"{active:item.read==0}\" v-for=\"(item,index) in personalList\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"flex-box\">\r\n\t\t\t\t\t\t<view class=\"fs30 col-10 flex-1 fwb\">{{item.title}}</view>\r\n\t\t\t\t\t\t<view class=\"fs28 col-5\">{{item.createtime_text}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex-box mt25\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"item.extra.thumb_image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t<view class=\"flex-1\">{{item.content}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\n\t\t\t\t<view class=\"g-btn3-wrap\">\n\t\t\t\t\t<view class=\"g-btn3\" @click=\"fetchPersonal()\">{{personalListMore.text}}</view>\n\t\t\t\t</view>\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttabIdx: 1,\n\t\t\t\t\n\t\t\t\tsystemList: [],\n\t\t\t\tsystemListMore:{page:1},\n\t\t\t\t\n\t\t\t\tpersonalList:[],\n\t\t\t\tpersonalListMore: {page:1}\r\n\t\t\t};\r\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.fetchSystem();\n\t\t\tthis.fetchPersonal();\n\t\t},\n\t\tonReachBottom() {\n\t\t\tif(this.tabIdx == 2){\n\t\t\t\tthis.fetchPersonal();\n\t\t\t}else{\n\t\t\t\tthis.fetchSystem();\n\t\t\t}\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttabClick(i) {\r\n\t\t\t\tthis.tabIdx = i;\r\n\t\t\t},\n\t\t\tfetchSystem() {\n\t\t\t\tthis.$util.fetch(this, 'xilutour.message/system_list', {pagesize:20}, 'systemListMore', 'systemList', 'data', data=>{\n\t\t\t\t  \n\t\t\t\t})\n\t\t\t},\n\t\t\tfetchPersonal() {\n\t\t\t\tthis.$util.fetch(this, 'xilutour.message/personal_list', {pagesize:20}, 'personalListMore', 'personalList', 'data', data=>{\n\t\t\t\t  \n\t\t\t\t})\n\t\t\t},\n\t\t\t//提交\n\t\t\tbindRead(index){\n\t\t\t\tlet messageList = this.personalList;\n\t\t\t\tlet message = messageList[index];\n\t\t\t\tif(message.type==1 || message.type == 3 || message.type == 5){\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/landscape_order_detail/landscape_order_detail?id='+message.extra.order_id\n\t\t\t\t\t})\n\t\t\t\t}else if(message.type == 2 || message.type == 4){\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/travel_order_detail/travel_order_detail?id='+message.extra.order_id\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tif(message.read == 1){\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\tthis.$core.post({url: 'xilutour.message/set_read',data: {message_id: message.id},loading: false,success: ret => {\n\t\t\t\t\t\tthis.personalList[index].read = 1;\n\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t},fail: err => {\n\t\t\t\t\t\tconsole.log(err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.xilu {\r\n\t\t&_message {\r\n\t\t\tposition: relative;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tmargin: 0 0 30rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 30rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-family: PingFangSC, PingFang SC;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #333333;\r\n\t\t\tline-height: 40rpx;\r\n\r\n\t\t\t.img {\r\n\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 120rpx;\r\n\t\t\t\theight: 120rpx;\r\n\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&_message.active::after {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\tright: 20rpx;\r\n\t\t\ttop: 20rpx;\r\n\t\t\twidth: 12rpx;\r\n\t\t\theight: 12rpx;\r\n\t\t\tbackground: #DB1428;\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\r\n\t\t.container {\r\n\t\t\tpadding: 110rpx 30rpx 30rpx;\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t}\r\n\t\t\r\n\t\t\t\r\n\t\t.page-head{\r\n\t\t\tbackground: #F7F9FB;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my_message.vue?vue&type=style&index=0&id=19b51ec6&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my_message.vue?vue&type=style&index=0&id=19b51ec6&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341228\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}