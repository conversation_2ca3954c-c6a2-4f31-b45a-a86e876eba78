.xilu .container.data-v-bff09234 {
  padding: 30rpx 40rpx;
  background: #F7F9FB;
}
.xilu_data_box.data-v-bff09234 {
  padding: 0 32rpx 0 40rpx;
  margin: 0 0 40rpx;
  width: 670rpx;
  height: 168rpx;
  border-radius: 24rpx;
  background: var(--normal);
}
.xilu_data_box .num.data-v-bff09234 {
  margin: 0 0 14rpx;
  font-size: 50rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 52rpx;
}
.xilu_data_box .btn.data-v-bff09234 {
  width: 170rpx;
  height: 80rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  font-size: 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #00B6AB;
  line-height: 80rpx;
  text-align: center;
}
.xilu_friend.data-v-bff09234 {
  margin: 0 0 30rpx;
  padding: 0 30rpx;
  width: 670rpx;
  height: 160rpx;
  background: #FFFFFF;
  box-shadow: 0 4rpx 20rpx 5rpx rgba(183, 189, 202, 0.05);
  border-radius: 24rpx;
}
.xilu_friend .img.data-v-bff09234 {
  margin-right: 30rpx;
  display: block;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}
.xilu_friend .name.data-v-bff09234 {
  margin: 0 0 30rpx;
  font-size: 30rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #101010;
  line-height: 32rpx;
}
.xilu_friend .time.data-v-bff09234 {
  font-size: 28rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #898989;
  line-height: 30rpx;
}
.xilu .share-pop.data-v-bff09234 {
  width: 642rpx;
}
.xilu .share-pop .img_post.data-v-bff09234 {
  margin: 0 0 30rpx;
  display: block;
  width: 642rpx;
  border-radius: 20rpx;
}
.xilu .share-pop .pop_btn.data-v-bff09234 {
  margin: 0;
  padding: 0;
  background-color: unset;
}
.xilu .share-pop .pop_btn.data-v-bff09234::after {
  content: none;
}
.xilu .share-pop .icon.data-v-bff09234 {
  margin: 0 auto 10rpx;
  display: block;
  width: 118rpx;
  height: 118rpx;
  border-radius: 50%;
}
.xilu .share-pop.scale.data-v-bff09234 {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}

