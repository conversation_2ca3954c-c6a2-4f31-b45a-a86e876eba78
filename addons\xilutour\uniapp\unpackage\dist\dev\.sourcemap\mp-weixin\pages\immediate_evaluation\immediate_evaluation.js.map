{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/immediate_evaluation/immediate_evaluation.vue?5aed", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/immediate_evaluation/immediate_evaluation.vue?6f03", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/immediate_evaluation/immediate_evaluation.vue?d023", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/immediate_evaluation/immediate_evaluation.vue?2444", "uni-app:///pages/immediate_evaluation/immediate_evaluation.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/immediate_evaluation/immediate_evaluation.vue?754d", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/immediate_evaluation/immediate_evaluation.vue?d036"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "order", "state_text", "order_no", "total_count", "createtime_text", "total_price", "pay_price", "coupon_price", "order_project", "tel", "address", "scenery_name", "thumb_image", "project_name", "images", "comment", "star", "content", "onLoad", "page", "methods", "changeRate", "chooseImages", "uni", "count", "success", "files", "that", "filePath", "bindDel", "bindSave", "formData", "name", "nameChn", "rules", "errorMsg", "require", "max", "title", "icon", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,6BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6I;AAC7I;AACwE;AACL;AACsC;;;AAGzG;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,2GAAM;AACR,EAAE,oHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAA60B,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyDj2B;AAAA,eACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MACAC;MACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC;QACAC;QACAC;UACA;UACAC;YAYAC;cACAC;cACAH;gBACAX;gBACAa;cACA;YACA;UAEA;QACA;MACA;IACA;IACAE;MACA;IACA;IACAC;MAAA;MACA;MACAC;MACAA;MACAA;MACAA;MACA,YACA;QAAAC;QAAAC;QAAAC;QAAAC;UAAAC;UAAAC;QAAA;MAAA,EACA;MACA;MACA;QACAd;UACAe;UACAC;QACA;QACA;MACA;MACA;QAAAC;QAAAzC;QAAA0B;UACA;UACAF;UACAA;YACAe;YACAC;UACA;QACA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3JA;AAAA;AAAA;AAAA;AAAgiD,CAAgB,i6CAAG,EAAC,C;;;;;;;;;;;ACApjD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/immediate_evaluation/immediate_evaluation.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/immediate_evaluation/immediate_evaluation.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./immediate_evaluation.vue?vue&type=template&id=2183b546&scoped=true&\"\nvar renderjs\nimport script from \"./immediate_evaluation.vue?vue&type=script&lang=js&\"\nexport * from \"./immediate_evaluation.vue?vue&type=script&lang=js&\"\nimport style0 from \"./immediate_evaluation.vue?vue&type=style&index=0&id=2183b546&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2183b546\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/immediate_evaluation/immediate_evaluation.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./immediate_evaluation.vue?vue&type=template&id=2183b546&scoped=true&\"", "var components\ntry {\n  components = {\n    uniRate: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-rate/components/uni-rate/uni-rate\" */ \"@/uni_modules/uni-rate/components/uni-rate/uni-rate.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.images.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./immediate_evaluation.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./immediate_evaluation.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"xilu\">\n\t\t<view class=\"page-foot bg-white\">\n\t\t\t<view class=\"ptb20\" @click=\"bindSave()\">\n\t\t\t\t<view class=\"g-btn1\" style=\"width: 670rpx;\">确定提交</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"container\">\n\t\t\t<view class=\"xilu_goods flex-box mb40\">\n\t\t\t\t<image class=\"img\" :src=\"order.order_project.thumb_image\" mode=\"aspectFill\"></image>\n\t\t\t\t<view class=\"flex-1\">\n\t\t\t\t\t<view class=\"m-ellipsis fs36 col-10 mb20\">{{order.order_project.project_name}}</view>\n\t\t\t\t\t<view class=\"flex-box col-3 mb40\">\n\t\t\t\t\t\t<text class=\"fs24\">¥</text>\n\t\t\t\t\t\t<text class=\"fs30 flex-1\">{{order.order_project.project_price}}</text>\n\t\t\t\t\t\t<view class=\"fs30 col-89 pr40\">数量 {{order.total_count}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex-box\">\n\t\t\t\t\t\t<view class=\"flex-1\">\n\t\t\t\t\t\t\t<text class=\"fs30 col-89\">实付款 </text>\n\t\t\t\t\t\t\t<text class=\"fs30 col-price\">¥</text>\n\t\t\t\t\t\t\t<text class=\"fs40 col-price\">{{order.pay_price}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"xilu_box1\">\n\t\t\t\t<view class=\"flex-box flex-center\">\n\t\t\t\t\t\t\n\t\t\t\t\t\t<uni-rate :value=\"comment.star\" color=\"#F8E9D0\" active-color=\"#FFAB29\" :size=\"36\" :margin=\"10\" @change=\"changeRate\"/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"fs30 col-89 tc mt25 pb30\">星级选择</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"xilu_box1\">\n\t\t\t\t<textarea class=\"textarea\" v-model=\"comment.content\" placeholder=\"请输入评价内容\"></textarea>\n\t\t\t</view>\n\n\t\t\t<view class=\"xilu_box1\">\n\t\t\t\t<view class=\"fs30 col-5 mb30\">上传图片</view>\n\t\t\t\t<view class=\"flex-box flex-wrap\">\n\t\t\t\t\t<view class=\"upload\" v-for=\"(img,index) in images\" :key=\"index\">\n\t\t\t\t\t\t<image :src=\"img\" mode=\"aspectFill\" class=\"img\"></image>\n\t\t\t\t\t\t<image src=\"/static/icon/icon_close.png\" mode=\"aspectFit\" @click=\"bindDel(index)\" class=\"del\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"upload\" v-if=\"images.length<9\" @click=\"chooseImages()\">\n\t\t\t\t\t\t<image src=\"../../static/icon/icon_upload.png\" mode=\"aspectFill\" class=\"img\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\tvar validate = require(\"../../xilu/validate.js\");\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\torder:{\n\t\t\t\t\tstate_text:'',\n\t\t\t\t\torder_no:'',\n\t\t\t\t\ttotal_count: 0,\n\t\t\t\t\tcreatetime_text: '',\n\t\t\t\t\ttotal_price: 0,\n\t\t\t\t\tpay_price: 0,\n\t\t\t\t\tcoupon_price: 0,\n\t\t\t\t\torder_project:{\n\t\t\t\t\t\ttel:'',\n\t\t\t\t\t\taddress:'',\n\t\t\t\t\t\tscenery_name:'',\n\t\t\t\t\t\tthumb_image: '',\n\t\t\t\t\t\tproject_name: '',\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\timages: [],\n\t\t\t\tcomment:{\n\t\t\t\t\tstar: 5,\n\t\t\t\t\tcontent: ''\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\tonLoad(options) {\n\t\t\tlet page = this;\n\t\t\tthis.getOpenerEventChannel().on(\"addComment\",function(data){\n\t\t\t\tpage.order = data\n\t\t\t})\n\t\t},\n\t\tmethods:{\n\t\t\tchangeRate(e){\n\t\t\t\tthis.comment.star = e.value;\n\t\t\t},\n\t\t\tchooseImages(){\n\t\t\t\tlet that = this;\n\t\t\t\tlet images = that.images;\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 9-images.length,\n\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\tlet files = res.tempFiles;\n\t\t\t\t\t\tfiles.map(item => {\n\t\t\t\t\t\t\t// #ifdef H5\n\t\t\t\t\t\t\tthat.$core.uploadFileH5({\n\t\t\t\t\t\t\t\tfilePath: item.path,\n\t\t\t\t\t\t\t\tsuccess: (ret, response) => {\n\t\t\t\t\t\t\t\t\timages.push(ret.data.url);\n\t\t\t\t\t\t\t\t\tthat.images = images;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\t\t\tthat.$core.uploadFile({\n\t\t\t\t\t\t\t\tfilePath: item.path,\n\t\t\t\t\t\t\t\tsuccess: (ret, response) => {\n\t\t\t\t\t\t\t\t\timages.push(ret.data.url);\n\t\t\t\t\t\t\t\t\tthat.images = images;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tbindDel(index){\n\t\t\t\tthis.images.splice(index,1);\n\t\t\t},\n\t\t\tbindSave(){\n\t\t\t\tlet formData = this.comment;\n\t\t\t\tformData.order_id = this.order.id;\n\t\t\t\tformData.scenery_id = this.order.order_project.scenery_id;\n\t\t\t\tformData.scenery_project_id = this.order.order_project.project_id;\n\t\t\t\tformData.images = this.images.length>0?this.images.join(','):'';\n\t\t\t\tvar rule = [\n\t\t\t\t\t{name: 'content',nameChn: '评价内容',rules: ['require','max:200'],errorMsg: {require: '请填写评价内容',max: '评价内容最多200'}},\n\t\t\t\t];\n\t\t\t\t// 是否全部通过，返回Boolean\n\t\t\t\tif (!validate.check(formData, rule)) {\n\t\t\t\t    uni.showToast({\n\t\t\t\t        title: validate.getError()[0],\n\t\t\t\t        icon: 'none'\n\t\t\t\t    });\n\t\t\t\t    return;\n\t\t\t\t}\n\t\t\t\tthis.$core.post({url: 'xilutour.scenery_comment/add_comment',data: formData,success: ret => {\n\t\t\t\t    this.getOpenerEventChannel().emit(\"commentSuccess\",{})\n\t\t\t\t    uni.navigateBack({});\n\t\t\t\t    uni.showToast({\n\t\t\t\t        title: '提交成功',\n\t\t\t\t        icon: 'none'\n\t\t\t\t    });\n\t\t\t\t}})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"less\" scoped>\n\t.xilu {\n\t\t&_goods {\n\t\t\t.img {\n\t\t\t\tmargin-right: 30rpx;\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: 180rpx;\n\t\t\t\theight: 180rpx;\n\t\t\t\tborder-radius: 15rpx;\n\t\t\t}\n\t\t}\n\n\t\t&_box1 {\n\t\t\tmargin: 0 0 30rpx;\n\t\t\tpadding: 40rpx 30rpx 0;\n\t\t\twidth: 670rpx;\n\t\t\tbackground: #F7F9FB;\n\t\t\tborder-radius: 30rpx;\n\n\t\t\t.textarea {\n\t\t\t\tpadding: 0 0 10rpx;\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 210rpx;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-family: PingFangSC, PingFang SC;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #555555;\n\t\t\t\tline-height: 34rpx;\n\t\t\t}\n\n\t\t\t.upload {\n\t\t\t\tposition: relative;\n\t\t\t\tmargin: 0 30rpx 30rpx 0;\n\t\t\t\twidth: 150rpx;\n\t\t\t\theight: 150rpx;\n\t\t\t\tborder-radius: 15rpx;\n\n\t\t\t\t.img {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\twidth: 150rpx;\n\t\t\t\t\theight: 150rpx;\n\t\t\t\t\tborder-radius: 15rpx;\n\t\t\t\t}\n\t\t\t\t.del{\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tright: -18rpx;\n\t\t\t\t\ttop: -18rpx;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\twidth: 36rpx;\n\t\t\t\t\theight: 36rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.container {\n\t\t\tpadding: 30rpx 40rpx 160rpx !important;\n\t\t}\n\n\t}\n</style>", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./immediate_evaluation.vue?vue&type=style&index=0&id=2183b546&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./immediate_evaluation.vue?vue&type=style&index=0&id=2183b546&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341174\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}