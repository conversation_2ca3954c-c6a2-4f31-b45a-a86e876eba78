<view class="xilu data-v-2998cc44"><view class="container data-v-2998cc44"><block wx:for="{{sceneryList}}" wx:for-item="order" wx:for-index="index" wx:key="index"><view class="g_order1 data-v-2998cc44"><view class="flex-box mb25 data-v-2998cc44"><view class="fs26 col-89 data-v-2998cc44">订单号</view><view class="flex-1 fs26 col-5 data-v-2998cc44">{{order.order_no}}</view><view class="fs30 col-3 data-v-2998cc44">已核销</view></view><view class="flex-box data-v-2998cc44"><image class="img data-v-2998cc44" src="{{order.order_project.thumb_image}}" mode="aspectFill"></image><view class="flex-1 data-v-2998cc44"><view class="m-ellipsis fs36 col-10 mb20 data-v-2998cc44">{{order.order_project.project_name}}</view><view class="flex-box col-3 mb35 data-v-2998cc44"><text class="fs24 data-v-2998cc44">¥</text><text class="fs30 flex-1 data-v-2998cc44">{{order.order_project.project_price}}</text><view class="fs30 col-89 data-v-2998cc44">{{"数量 "+order.total_count}}</view></view><view class="data-v-2998cc44"><text class="fs30 col-89 data-v-2998cc44">核销金额</text><text class="fs30 col-price data-v-2998cc44">¥</text><text class="fs40 col-price data-v-2998cc44">{{order.verifer_money}}</text></view></view></view><view class="flex-box pt30 fs26 col-89 data-v-2998cc44"><view class="flex-1 data-v-2998cc44">{{"核销数: "+order.my_count}}</view><view class="data-v-2998cc44">{{"核销时间: "+order.verifytime_text}}</view></view></view></block><view class="g-btn3-wrap data-v-2998cc44"><view data-event-opts="{{[['tap',[['fetch',['$event']]]]]}}" class="g-btn3 data-v-2998cc44" bindtap="__e">{{sceneryListMore.text}}</view></view></view></view>