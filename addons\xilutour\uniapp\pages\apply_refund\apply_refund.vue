<template>
	<view class="xilu">
		<view class="page-foot bg-white">
			<view class="ptb20" @click="formSubmit()">
				<view class="g-btn1">确定提交</view>
			</view>
		</view>
		<view class="container">
			<view class="xilu_goods flex-box mb40">
				<image class="img" :src="order.order_tour.thumb_image" mode="aspectFill"></image>
				<view class="flex-1">
					<view class="m-ellipsis fs36 col-10 mb20">{{order.order_tour.tour_name}}</view>
					<view class="flex-box col-3 mb40">
						<text class="fs24">¥</text>
						<text class="fs30 flex-1">{{order.order_tour.tour_date_salesprice}}</text>
						<view class="fs30 col-89 pr40">数量 {{order.total_count}}</view>
					</view>
					<view class="flex-box">
						<view class="flex-1">
							<text class="fs30 col-89">实付款 </text>
							<text class="fs30 col-price">¥</text>
							<text class="fs40 col-price">{{order.pay_price}}</text>
						</view>
					</view>
				</view>
			</view>

			<picker :range="refundReasons" :value="reasonValue" mode="selector" @change="reasonChange">
				<view class="g_input_box flex-box mb30">
					<view class="fs30 col-5 ">申请原因</view>
					<view class="m-arrow-right flex-1 fs30 col-10 tr pr30">{{aftersale.reason?aftersale.reason:'请选择'}}</view>
				</view>
			</picker>

			<view class="g_input_box flex-box mb20">
				<view class="fs30 col-5 ">退款金额</view>
				<view class="flex-1 fs30 col-price tr">
					<text class="fs30">¥</text>
					<text class="fs40">{{order.pay_price}}</text>
				</view>
			</view>
			
			<view class="tip mb30">提示：{{order.tips}}</view>

			<view class="xilu_box1">
				<view class="fs30 col-5 mb30">申请说明</view>
				<textarea class="textarea" v-model="aftersale.refund_content" placeholder="请输入退款申请说明"></textarea>
			</view>

			<view class="xilu_box1">
				<view class="fs30 col-5 mb30">上传图片</view>
				<view class="flex-box flex-wrap">
					<view class="upload" v-for="(img,index) in images" :key="index">
						<image :src="img" mode="aspectFill" class="img"></image>
						<image src="/static/icon/icon_close.png" mode="aspectFit" class="del"></image>
					</view>
					<view class="upload" v-if="images.length<9" @click="chooseImages()">
						<image src="../../static/icon/icon_upload.png" mode="aspectFill" class="img"></image>
					</view>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
	var validate = require("../../xilu/validate.js");
	export default {
		data() {
			return {
				orderId: 0,
				refundReasons: [],
				reasonValue: 0,
				order:{
					order_no:'',
					total_count: 0,
					createtime_text: '',
					total_price: 0,
					pay_price: 0,
					coupon_price: 0,
					tips:'',
					order_tour:{
						thumb_image: '',
						tour_name: '',
						appoint_end_date_text: '',
						appoint_end_date_week: '',
						appoint_date_text: '',
						appoint_date_week: '',
						series_days: 0,
						tour_date_salesprice: 0,
					}
				},
				aftersale: {
					reason: '',
					refund_content:''
				},
				
				images: []
			};
		},
		onLoad(options) {
			this.refundReasons = getApp().globalData.config.refund_reasons;
			this.orderId = options.id || 0;
			this.fetchRefundDetail();
		},
		methods:{
			fetchRefundDetail(){
				this.$core.get({url:'xilutour.aftersale/pre_aftersale',data:{order_id: this.orderId},success:(ret)=>{
					this.order = ret.data;
				}});
			},
			reasonChange(e){
				let index = e.detail.value;
				this.reasonValue = index;
				this.aftersale.reason = this.refundReasons[index];
			},
			chooseImages(){
				let that = this;
				let images = that.images;
				uni.chooseImage({
					count: 9-images.length,
					success: res => {
						let files = res.tempFiles;
						files.map(item => {
							// #ifdef H5
							that.$core.uploadFileH5({
								filePath: item.path,
								success: (ret, response) => {
									images.push(ret.data.url);
									that.images = images;
								}
							});
							// #endif
							
							// #ifdef MP-WEIXIN
							that.$core.uploadFile({
								filePath: item.path,
								success: (ret, response) => {
									images.push(ret.data.url);
									that.images = images;
								}
							});
							// #endif
						});
					}
				});
			},
			//提交
			formSubmit() {
			    let formData = this.aftersale;
				formData.order_id = this.order.id;
				formData.price = this.order.pay_price;
				formData.images = this.images.length>0?this.images.join(','):'';
				console.log(formData);
			    var rule = [
					{name: 'order_id',nameChn: '订单ID',rules: ['require','gt:0'],errorMsg: {require: '订单错误',gt: '订单错误'},},
					{name: 'reason',nameChn: '售后原因',rules: ['require'],errorMsg: {require: '请选择售后原因'},},
					{name: 'refund_content',nameChn: '退款说明',rules: ['require','length:1,600'],errorMsg: {require: '请填写申请说明',length: "说明限制600字"},},
					];
			    // 是否全部通过，返回Boolean
				if(!validate.check(formData,rule)){
					uni.showToast({title: validate.getError()[0],icon:'none'});
					return ;
				}
				this.$core.post({url:'xilutour.aftersale/apply_aftersale',data:formData,success:ret=>{
					this.getOpenerEventChannel().emit('aftersaleSuccess',ret.data);
					uni.showModal({
						title: '提示',
						content: ret.msg,
						showCancel: false,
						success() {
							uni.navigateBack({});
						}
					})
				},fail: ret=>{
					uni.showModal({
						title: '提示',
						content: ret.msg,
						showCancel: false,
					})
					return false;
				}
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.xilu {
		&_goods {
			.img {
				margin-right: 30rpx;
				display: block;
				width: 180rpx;
				height: 180rpx;
				border-radius: 15rpx;
			}
		}

		&_box1 {
			margin: 0 0 30rpx;
			padding: 40rpx 30rpx 0;
			width: 670rpx;
			background: #F7F9FB;
			border-radius: 30rpx;

			.textarea {
				padding: 0 0 10rpx;
				width: 100%;
				height: 140rpx;
				font-size: 30rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				color: #555555;
				line-height: 34rpx;
			}

			.upload {
				position: relative;
				margin: 0 30rpx 30rpx 0;
				width: 150rpx;
				height: 150rpx;
				border-radius: 15rpx;

				.img {
					display: block;
					width: 150rpx;
					height: 150rpx;
					border-radius: 15rpx;
				}
				
				.del{
					position: absolute;
					right: -18rpx;
					top: -18rpx;
					display: block;
					width: 36rpx;
					height: 36rpx;
				}
			}
		}

		.container {
			padding: 30rpx 40rpx 160rpx !important;
		}
		
		.tip{
			font-size: 24rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			color: #D91B00;
			line-height: 26rpx;
		}
	}
</style>