{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?34f7", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?efa2", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?b743", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?70ac", "uni-app:///pages/login/login.vue", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?0945", "webpack:///D:/work/travel.uniapp.vip_CPtth6/addons/xilutour/uniapp/pages/login/login.vue?db99"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "logo", "mobile", "disabledCode", "codeText", "code", "agree", "isAgree", "content", "userAgreement", "privacyAgreement", "onLoad", "getApp", "uni", "title", "methods", "closeLogin", "delta", "agreePopOpen", "agreePopClose", "getArticle", "url", "success", "loading", "toggleAgree", "getCode", "icon", "event", "timeCut", "n", "clearInterval", "mobilelogin", "puser_id", "that", "getPhoneNumber", "provider", "iv", "encryptedData", "fail", "console", "phoneNumber", "wxAccount", "compareVersion", "v1", "v2"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC+M;AAC/M,gBAAgB,gNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA8zB,CAAgB,8xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyEl1B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAMA;IACA;MACAC;MACAC;MACAA;MACAA;QACAC;MACA;IACA;EAEA;EACAC;IACAC;MACAH;QACAI;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QAAAC;QAAArB;QAAAsB;UACA;UACA;QACA;QAAAC;MAAA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;QACAZ;UACAC;UACAY;QACA;QACA;MACA;MACA;QAAAL;QAAArB;UAAAE;UAAAyB;QAAA;QAAAL;UACA;QACA;MAAA;IACA;IACA;IACAM;MAAA;MACA;MACA;MACA;MACA;MACA;QACAC;QACA;UACAC;QACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACAlB;UACAC;UACAY;QACA;QACA;MACA;MACA;MACA;MACA;QACAb;UACAC;UACAY;QACA;QACA;MACA;MACA;MACA;QAAAL;QAAArB;UAAAgC;UAAA9B;UAAAG;QAAA;QAAAiB;UACA;UACAW;UACApB;UACAA;YACAI;UACA;QACA;MAAA;IACA;IACAiB;MACA;MACA;MACA;QACA;UACAD;YAAA5B;UAAA;QACA;UACAQ;YACAsB;YACAb;cACAW;gBAAAG;gBAAAC;cAAA;YACA;YACAC;cACAzB;gBAAA;gBAAAa;cAAA;YACA;UACA;QACA;MACA;QACAa;MACA;IACA;IAEAC;MACA;MACAxC;MACA;MACAA;MACAiC;QACAZ;QACArB;QACAsB;UACAmB;UACA;UACAR;UACAA;UACArB;UACAC;UACAA;UACAA;YACAC;UACA;QACA;QAAAwB;UACA;UACAzB;YAAA;YAAAa;UAAA;UACA;QACA;MACA;IAEA;IAGA;IACAgB;MACAC;MACAC;MACA;MAEA;QACAD;MACA;MACA;QACAC;MACA;MAEA;QACA;QACA;QAEA;UACA;QACA;UACA;QACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7QA;AAAA;AAAA;AAAA;AAAyqC,CAAgB,ylCAAG,EAAC,C;;;;;;;;;;;ACA7rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<!-- #ifdef H5 || APP-PLUS -->\n\t\t<view class=\"container\">\n\t\t\t<image class=\"m-backdrop\" src=\"/static/icon/icon_back1.png\" mode=\"widthFix\"></image>\n\t\t\t<view class=\"pr login-wrap\">\n\t\t\t\t<view class=\"title\">登录</view>\n\t\t\t\t<view class=\"login-box mb40\">\n\t\t\t\t\t<view class=\"input-box flex-box\">\n\t\t\t\t\t\t<image class=\"mr30\" src=\"/static/icon/icon_phone.png\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t<input class=\"flex-1 fs28\" v-model=\"mobile\" placeholder=\"请输入手机号\" />\n\t\t\t\t\t\t<view class=\"col-red fs28 ptb10\" @click=\"getCode()\">{{codeText}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"input-box flex-box\">\n\t\t\t\t\t\t<image class=\"mr30\" src=\"/static/icon/icon_code.png\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t<input class=\"flex-1 fs28\" v-model=\"code\" placeholder=\"请输入验证码\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"btn-login\" @click=\"mobilelogin()\">登 录</view>\n\t\t\t\t\t<view class=\"fs26 col-6 tc\">未注册用户，登录即创建账户</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"foot-row flex-box flex-center fs26 col-6\">\n\t\t\t\t\t<image @click=\"toggleAgree\" v-if=\"isAgree\" src=\"/static/icon/circle_selected.png\" mode=\"aspectFill\"></image>\n\t\t\t\t\t<image @click=\"toggleAgree\" v-else src=\"/static/icon/circle_normal.png\" mode=\"aspectFill\"></image>\n\t\t\t\t\t<view>登录即同意</view>\n\t\t\t\t\t<!-- \"agreement('user_agreement')\" -->\n\t\t\t\t\t<view class=\"col-red\" @click=\"agreePopOpen('user_agreement')\">《用户协议》</view>\n\t\t\t\t\t<view>和</view>\n\t\t\t\t\t<view class=\"col-red\" @click=\"agreePopOpen('privacy_agreement')\">《隐私注册》</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- #endif -->\n\t\t\n\t\t<!-- #ifdef MP-WEIXIN -->\n\t\t<view class=\"container\">\n\t\t\t<view class=\"authorize_content\">\n\t\t\t\t<view class=\"header\">\n\t\t\t\t\t<!-- <image src=\"/static/authorize.png\" mode=\"aspectFill\"></image> -->\n\t\t\t\t\t<image class=\"head\" :src=\"logo\" mode=\"\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"title\">请确认以下授权信息</view>\n\t\t\t\t<view class=\"info\">·获取您的信息(手机号等)</view>\n\t\t\t\t<button hover-class=\"none\" open-type=\"getPhoneNumber\" class=\"btn1\" @getphonenumber=\"getPhoneNumber\" >授权登录</button>\n\t\t\t\t<button hover-class=\"none\" class=\"btn2\" @click=\"closeLogin\">暂不登录</button>\n\t\t\t\t<!-- <view class=\"user-rules\">\n\t\t\t\t\t<u-checkbox v-model=\"agree\" active-color=\"#0896e0\">已阅读并同意以下条款</u-checkbox>\n\t\t\t\t\t<view class=\"rules\">\n\t\t\t\t\t\t<text class=\"primary-color\" @tap=\"goService\">《用户服务协议》</text>\n\t\t\t\t\t\t<text>和</text>\n\t\t\t\t\t\t<text class=\"primary-color\" @tap=\"goPrivacy\">《用户隐私政策》</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view> -->\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- #endif -->\n\t\t\n\t\t<!-- 弹窗 -->\n\t\t<uni-popup ref=\"agreePopup\" type=\"center\">\n\t\t\t<view class=\"agreePop tc\" catchtouchmove=\"true\">\n\t\t\t\t<view class=\"fs28 col-black content\">\n\t\t\t\t\t<rich-text :nodes=\"content\"></rich-text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"foot-wrap flex-box m-hairline--top\">\n\t\t\t\t\t<view @click=\"agreePopClose\" class=\"btn flex-1 col-6 m-hairline--right\">取消</view>\n\t\t\t\t\t<view class=\"btn flex-1 col-red\" @click=\"agreePopClose\">确定</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tlogo: '',\n\t\t\t\tmobile:'',\n\t\t\t\tdisabledCode: false,\n\t\t\t\tcodeText: '验证码',\n\t\t\t\tcode:'',\n\t\t\t\tagree: true,\n\t\t\t\tisAgree: true,\n\t\t\t\tcontent: '',\n\t\t\t\tuserAgreement:{},\n\t\t\t\tprivacyAgreement:{},\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\t// #ifdef H5\n\t\t\t\tthis.getArticle();\n\t\t\t//#endif\n\t\t\t\n\t\t\t//#ifdef MP-WEIXIN\n\t\t\tthis.logo = getApp().globalData.config.logo;\n\t\t\tthis.$core.wxLogin(function(data){\n\t\t\t\tgetApp().globalData.userinfo = data;\n\t\t\t\tuni.navigateBack({});\n\t\t\t\tuni.$emit(\"user_update\",{});\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '登录成功',\n\t\t\t\t});\n\t\t\t});\n\t\t\t// #endif\n\t\t},\n\t\tmethods: {\n\t\t\tcloseLogin(){\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta:1\n\t\t\t\t})\n\t\t\t},\n\t\t\tagreePopOpen(type) {\n\t\t\t\t//let title = type == 'user_agreement' ? '用户协议' : '隐私协议';\n\t\t\t\tlet content = type == 'user_agreement' ? this.userAgreement.content : this.privacyAgreement.content;\n\t\t\t\tthis.content = content;\n\t\t\t\tthis.$refs.agreePopup.open();\n\t\t\t},\n\t\t\tagreePopClose() {\n\t\t\t\tthis.$refs.agreePopup.close();\n\t\t\t},\n\t\t\tgetArticle(){\n\t\t\t\tthis.$core.post({url:'xilutour.singlepage/config_page',data:{},success:(ret)=>{\n\t\t\t\t\tthis.userAgreement = ret.data.user_agreement;\n\t\t\t\t\tthis.privacyAgreement = ret.data.privacy_agreement;\n\t\t\t\t},loading:false});\n\t\t\t},\n\t\t\ttoggleAgree() {\t\n\t\t\t\tthis.isAgree = !this.isAgree;\n\t\t\t},\n\t\t\t\n\t\t\t//获取验证码\n\t\t\tgetCode(){\n\t\t\t\tif(this.disabledCode) return false;\n\t\t\t\tlet mobile = this.mobile;\n\t\t\t\tif(!mobile){\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle:'手机号不得为空',\n\t\t\t\t\t\ticon:'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\tthis.$core.post({url:'sms/send',data:{mobile:mobile,event:'mobilelogin'},success:(ret)=>{\n\t\t\t\t\t this.timeCut();\n\t\t\t\t}});\n\t\t\t},\n\t\t\t// 倒计时\n\t\t\ttimeCut() {\n\t\t\t\tif (this.disabledCode) return;\n\t\t\t\tthis.disabledCode = true;\n\t\t\t\tlet n = 60;\n\t\t\t\tthis.codeText = n + 's';\n\t\t\t\tconst run = setInterval(() => {\n\t\t\t\t\tn -= 1;\n\t\t\t\t\tif (n < 0) {\n\t\t\t\t\t\tclearInterval(run);\n\t\t\t\t\t}\n\t\t\t\t\tthis.codeText = n + 's';\n\t\t\t\t\tif (this.codeText < 0 + 's') {\n\t\t\t\t\t\tthis.disabledCode = false;\n\t\t\t\t\t\tthis.codeText = '验证码';\n\t\t\t\t\t}\n\t\t\t\t}, 1000);\n\t\t\t},\n\t\t\tmobilelogin(){\n\t\t\t\tlet that = this;\n\t\t\t\tif(!that.isAgree){\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle:'请同意协议',\n\t\t\t\t\t\ticon:'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\tlet mobile = this.mobile;\n\t\t\t\tlet code = this.code;\n\t\t\t\tif(!mobile || !code){\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle:'手机号/验证码必填',\n\t\t\t\t\t\ticon:'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\tlet puserId = this.$core.getCache(\"puser_id\") || 0;\n\t\t\t\tthis.$core.post({url:'xilutour.user/mobilelogin',data:{puser_id:puserId,mobile:mobile,code: code},success:(ret)=>{\n\t\t\t\t\t let userinfo = ret.data.userinfo;\n\t\t\t\t\t that.$core.setUserinfo(userinfo);\n\t\t\t\t\t uni.$emit(\"user_update\",{});\n\t\t\t\t\t uni.navigateBack({\n\t\t\t\t\t \tdelta:1\n\t\t\t\t\t })\n\t\t\t\t}});\n\t\t\t},\n\t\t\tgetPhoneNumber(e){\n\t\t\t\tvar that = this;\n\t\t\t\tlet version = uni.getSystemInfoSync().SDKVersion;\n\t\t\t\tif(e.detail.errMsg == \"getPhoneNumber:ok\"){\n\t\t\t\t\tif(that.compareVersion(version,'2.21.2')>=0){\n\t\t\t\t\t\tthat.phoneNumber(that,{code: e.detail.code})\n\t\t\t\t\t}else{\n\t\t\t\t\t\tuni.login({\n\t\t\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\t\t\tsuccess: (auth) => {\n\t\t\t\t\t\t\t\tthat.phoneNumber(that,{iv: e.detail.iv,encryptedData:e.detail.encryptedData})\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\t\tuni.showToast({'title':'微信登录授权失败',icon:\"none\"});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tconsole.log(\"用户点击了拒绝\")\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tphoneNumber(that,data){\n\t\t\t\tlet wxAccount = that.$core.getCache('wx_account');\n\t\t\t\tdata['third_id'] = wxAccount.third_id;\n\t\t\t\tlet puserId = this.$core.getCache(\"puser_id\") || 0;\n\t\t\t\tdata['puser_id'] = puserId;\n\t\t\t\tthat.$core.post({\n\t\t\t\t\t  url: 'xilutour.user/get_mobile',\n\t\t\t\t\t  data: data,\n\t\t\t\t\t  success: (ret, response) => {\n\t\t\t\t\t\twxAccount['bindind'] = 1;\n\t\t\t\t\t\tlet userinfo = ret.data.userinfo;\n\t\t\t\t\t\tthat.$core.setCache('wx_account', wxAccount);\n\t\t\t\t\t\tthat.$core.setUserinfo(userinfo);\n\t\t\t\t\t\tgetApp().globalData.userinfo = userinfo;\n\t\t\t\t\t\tuni.navigateBack({});\n\t\t\t\t\t\tuni.$emit(\"user_update\",{});\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '登录成功',\n\t\t\t\t\t\t});\n\t\t\t\t\t  },fail: (ret, response) => {\n\t\t\t\t\t\t//失败，重试\n\t\t\t\t\t\tuni.showToast({'title':\"获取失败\",icon:\"none\"})\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t  }\n\t\t\t\t\t});\n\t\t\t\t\n\t\t\t},\n\t\t\t\n\t\t\t\n\t\t\t//版本比较\n\t\t\tcompareVersion(v1, v2) {\n\t\t\t  v1 = v1.split('.')\n\t\t\t  v2 = v2.split('.')\n\t\t\t  const len = Math.max(v1.length, v2.length)\n\t\t\t\n\t\t\t  while (v1.length < len) {\n\t\t\t    v1.push('0')\n\t\t\t  }\n\t\t\t  while (v2.length < len) {\n\t\t\t    v2.push('0')\n\t\t\t  }\n\t\t\t\n\t\t\t  for (let i = 0; i < len; i++) {\n\t\t\t    const num1 = parseInt(v1[i])\n\t\t\t    const num2 = parseInt(v2[i])\n\t\t\t\n\t\t\t    if (num1 > num2) {\n\t\t\t      return 1\n\t\t\t    } else if (num1 < num2) {\n\t\t\t      return -1\n\t\t\t    }\n\t\t\t  }\n\t\t\t\n\t\t\t  return 0\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.head {\n\t\tmargin-right: 26rpx;\n\t\twidth: 128rpx;\n\t\theight: 128rpx;\n\t\tborder: 4rpx solid var(--normal);\n\t\tborder-radius: 50%;\n\t}\n\n\t.authorize_content {\n\t\tpadding: 120rpx 75rpx;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.authorize_content .header {\n\t\twidth: 201rpx;\n\t\theight: 201rpx;\n\t\t/* border: 6rpx solid #fff; */\n\t\t/* box-shadow: 0px 3rpx 8rpx 0px rgba(213, 213, 213, 0.4); */\n\t\tborder-radius: 50%;\n\t\toverflow: hidden;\n\t\tmargin: 0 auto;\n\t}\n\n\t.authorize_content .header image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tdisplay: block;\n\t}\n\n\t.authorize_content .title {\n\t\tfont-size: 32rpx;\n\t\tcolor: #333333;\n\t\tpadding-top: 50rpx;\n\t\tmargin-top: 40rpx;\n\t\t/* border-top: 1rpx solid #EDEDED; */\n\t\ttext-align: center;\n\t}\n\n\t.authorize_content .info {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999999;\n\t\tpadding-top: 30rpx;\n\t\tpadding-bottom: 70rpx;\n\t\ttext-align: center;\n\t}\n\n\t.authorize_content button {\n\t\twidth: 600rpx;\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t.container {\n\t\tmin-height: calc(100vh - env(safe-area-inset-top) - var(--window-bottom) - var(--window-top));\n\t\toverflow: hidden;\n\t\tfont-size: 28rpx;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.btn1 {\n\t\theight: 80rpx;\n\t\tborder-radius: 50rpx;\n\t\ttext-align: center;\n\t\tbackground-color: var(--normal);\n\t\tcolor: #FFFFFF;\n\t\tfont-size: 28rpx;\n\t\tline-height: 80rpx;\n\t}\n\n\t.btn2 {\n\t\tbackground-color: #D8D8D8;\n\t\tcolor: #FFFFFF;\n\t\theight: 80rpx;\n\t\tborder-radius: 50rpx;\n\t\ttext-align: center;\n\t\tfont-size: 28rpx;\n\t\tline-height: 80rpx;\n\t}\n\n\t.user-rules {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin-top: 80rpx;\n\t}\n\n\t.primary-color {\n\t\tcolor: #0896e0;\n\t}\n\n\t.u-checkbox__label {\n\t\tcolor: #999999 !important;\n\t}\n\n\t.rules {\n\t\tmargin-top: 20rpx;\n\t}\n\t\n\t.login-wrap {\n\t\tpadding: 215rpx 30rpx 30rpx;\n\t}\n\t.login-wrap .title {\n\t\tmargin: 0 0 125rpx 50rpx;\n\t\tfont-size: 50rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #ffffff;\n\t\tline-height: 56rpx;\n\t}\n\t.login-box {\n\t\tpadding: 90rpx 50rpx 50rpx;\n\t\twidth: 690rpx;\n\t\theight: 645rpx;\n\t\tbackground: #ffffff;\n\t\tbox-shadow: 0 6rpx 29rpx 1rpx rgba(45, 45, 45, 0.14);\n\t\tborder-radius: 20rpx;\n\t}\n\t.login-box .input-box {\n\t\tpadding: 0 50rpx;\n\t\theight: 80rpx;\n\t\tborder: 1rpx solid #cdcdcd;\n\t\tborder-radius: 40rpx;\n\t}\n\t.login-box .input-box + .input-box {\n\t\tmargin-top: 50rpx;\n\t}\n\t.login-box .input-box image {\n\t\tdisplay: block;\n\t\twidth: 30rpx;\n\t\theight: 44rpx;\n\t}\n\t.login-box .btn-login {\n\t\tmargin: 125rpx auto 40rpx;\n\t\twidth: 590rpx;\n\t\theight: 92rpx;\n\t\tbackground: linear-gradient(180deg, #ed1f34, #f24682);\n\t\tborder-radius: 10rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #ffffff;\n\t\tline-height: 92rpx;\n\t\ttext-align: center;\n\t}\n\n\t.foot-row image {\n\t\tmargin: 0 20rpx 0 0;\n\t\tdisplay: block;\n\t\twidth: 38rpx;\n\t\theight: 38rpx;\n\t\tborder-radius: 50%;\n\t}\n\t\n\t\n\t.agreePop {\n\t\tpadding: 30rpx 0 0;\n\t\twidth: 520rpx;\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\toverflow: hidden;\n\t}\n\t.agreePop .content {\n\t\tmargin: 0 auto 25rpx;\n\t\tpadding: 0 30rpx;\n\t\tmax-height: 500rpx;\n\t\toverflow-y: scroll;\n\t}\n\t.agreePop .btn {\n\t\theight: 90rpx;\n\t\tline-height: 90rpx;\n\t}\n</style>\n", "import mod from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1746494341168\n      var cssReload = require(\"D:/xbuilder/HBuilderX.4.23.2024070804/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}